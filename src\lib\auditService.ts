import { prisma } from './prisma'
import { NextRequest } from 'next/server'

export interface AuditLogData {
  userId: string
  action: string
  entity: string
  entityId?: string
  oldValues?: any
  newValues?: any
  ipAddress?: string
  userAgent?: string
  metadata?: any
}

export class AuditService {
  // Create audit log entry
  static async log(data: AuditLogData) {
    try {
      const auditLog = await prisma.auditLog.create({
        data: {
          userId: data.userId,
          action: data.action,
          entity: data.entity,
          entityId: data.entityId,
          oldValues: data.oldValues,
          newValues: data.newValues,
          ipAddress: data.ipAddress,
          userAgent: data.userAgent
        },
        include: {
          user: {
            select: {
              id: true,
              username: true,
              fullName: true
            }
          }
        }
      })

      console.log(`Audit log created: ${data.action} on ${data.entity} by user ${data.userId}`)
      return auditLog
    } catch (error) {
      console.error('Error creating audit log:', error)
      // Don't throw error to avoid breaking main functionality
      return null
    }
  }

  // Extract request metadata
  static extractRequestMetadata(request: NextRequest) {
    const forwarded = request.headers.get('x-forwarded-for')
    const realIp = request.headers.get('x-real-ip')
    const ipAddress = forwarded?.split(',')[0] || realIp || 'unknown'
    const userAgent = request.headers.get('user-agent') || 'unknown'

    return { ipAddress, userAgent }
  }

  // Log user login
  static async logLogin(userId: string, request?: NextRequest) {
    const metadata = request ? this.extractRequestMetadata(request) : {}
    
    return await this.log({
      userId,
      action: 'LOGIN',
      entity: 'user',
      entityId: userId,
      ...metadata
    })
  }

  // Log user logout
  static async logLogout(userId: string, request?: NextRequest) {
    const metadata = request ? this.extractRequestMetadata(request) : {}
    
    return await this.log({
      userId,
      action: 'LOGOUT',
      entity: 'user',
      entityId: userId,
      ...metadata
    })
  }

  // Log checksheet creation
  static async logChecksheetCreate(userId: string, checksheetId: string, checksheetData: any, request?: NextRequest) {
    const metadata = request ? this.extractRequestMetadata(request) : {}
    
    return await this.log({
      userId,
      action: 'CREATE',
      entity: 'checksheet',
      entityId: checksheetId,
      newValues: checksheetData,
      ...metadata
    })
  }

  // Log checksheet update
  static async logChecksheetUpdate(userId: string, checksheetId: string, oldData: any, newData: any, request?: NextRequest) {
    const metadata = request ? this.extractRequestMetadata(request) : {}
    
    return await this.log({
      userId,
      action: 'UPDATE',
      entity: 'checksheet',
      entityId: checksheetId,
      oldValues: oldData,
      newValues: newData,
      ...metadata
    })
  }

  // Log checksheet submission
  static async logChecksheetSubmit(userId: string, checksheetId: string, request?: NextRequest) {
    const metadata = request ? this.extractRequestMetadata(request) : {}
    
    return await this.log({
      userId,
      action: 'SUBMIT',
      entity: 'checksheet',
      entityId: checksheetId,
      ...metadata
    })
  }

  // Log checksheet approval/rejection
  static async logChecksheetApproval(userId: string, checksheetId: string, approvalId: string, decision: string, comments?: string, request?: NextRequest) {
    const metadata = request ? this.extractRequestMetadata(request) : {}
    
    return await this.log({
      userId,
      action: decision === 'APPROVED' ? 'APPROVE' : 'REJECT',
      entity: 'checksheet',
      entityId: checksheetId,
      newValues: {
        approvalId,
        decision,
        comments
      },
      ...metadata
    })
  }

  // Log template creation
  static async logTemplateCreate(userId: string, templateId: string, templateData: any, request?: NextRequest) {
    const metadata = request ? this.extractRequestMetadata(request) : {}
    
    return await this.log({
      userId,
      action: 'CREATE',
      entity: 'template',
      entityId: templateId,
      newValues: templateData,
      ...metadata
    })
  }

  // Log template update
  static async logTemplateUpdate(userId: string, templateId: string, oldData: any, newData: any, request?: NextRequest) {
    const metadata = request ? this.extractRequestMetadata(request) : {}
    
    return await this.log({
      userId,
      action: 'UPDATE',
      entity: 'template',
      entityId: templateId,
      oldValues: oldData,
      newValues: newData,
      ...metadata
    })
  }

  // Log user creation
  static async logUserCreate(userId: string, newUserId: string, userData: any, request?: NextRequest) {
    const metadata = request ? this.extractRequestMetadata(request) : {}
    
    return await this.log({
      userId,
      action: 'CREATE',
      entity: 'user',
      entityId: newUserId,
      newValues: userData,
      ...metadata
    })
  }

  // Log user update
  static async logUserUpdate(userId: string, targetUserId: string, oldData: any, newData: any, request?: NextRequest) {
    const metadata = request ? this.extractRequestMetadata(request) : {}
    
    return await this.log({
      userId,
      action: 'UPDATE',
      entity: 'user',
      entityId: targetUserId,
      oldValues: oldData,
      newValues: newData,
      ...metadata
    })
  }

  // Log folder creation
  static async logFolderCreate(userId: string, folderId: string, folderData: any, request?: NextRequest) {
    const metadata = request ? this.extractRequestMetadata(request) : {}
    
    return await this.log({
      userId,
      action: 'CREATE',
      entity: 'folder',
      entityId: folderId,
      newValues: folderData,
      ...metadata
    })
  }

  // Log database configuration change
  static async logDatabaseConfigChange(userId: string, configData: any, request?: NextRequest) {
    const metadata = request ? this.extractRequestMetadata(request) : {}
    
    return await this.log({
      userId,
      action: 'UPDATE',
      entity: 'database_config',
      newValues: configData,
      ...metadata
    })
  }

  // Log file access/view
  static async logFileAccess(userId: string, entity: string, entityId: string, action: string = 'VIEW', request?: NextRequest) {
    const metadata = request ? this.extractRequestMetadata(request) : {}
    
    return await this.log({
      userId,
      action,
      entity,
      entityId,
      ...metadata
    })
  }

  // Get audit logs with filtering
  static async getAuditLogs(filters: {
    userId?: string
    entity?: string
    action?: string
    entityId?: string
    startDate?: Date
    endDate?: Date
    page?: number
    limit?: number
  }) {
    try {
      const {
        userId,
        entity,
        action,
        entityId,
        startDate,
        endDate,
        page = 1,
        limit = 50
      } = filters

      const skip = (page - 1) * limit
      const where: any = {}

      if (userId) where.userId = userId
      if (entity) where.entity = entity
      if (action) where.action = action
      if (entityId) where.entityId = entityId
      
      if (startDate || endDate) {
        where.createdAt = {}
        if (startDate) where.createdAt.gte = startDate
        if (endDate) where.createdAt.lte = endDate
      }

      const [logs, total] = await Promise.all([
        prisma.auditLog.findMany({
          where,
          skip,
          take: limit,
          include: {
            user: {
              select: {
                id: true,
                username: true,
                fullName: true
              }
            }
          },
          orderBy: { createdAt: 'desc' }
        }),
        prisma.auditLog.count({ where })
      ])

      return {
        logs,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    } catch (error) {
      console.error('Error fetching audit logs:', error)
      throw error
    }
  }

  // Get user activity summary
  static async getUserActivitySummary(userId: string, days: number = 30) {
    try {
      const startDate = new Date()
      startDate.setDate(startDate.getDate() - days)

      const logs = await prisma.auditLog.findMany({
        where: {
          userId,
          createdAt: {
            gte: startDate
          }
        },
        select: {
          action: true,
          entity: true,
          createdAt: true
        }
      })

      // Group by action and entity
      const summary = logs.reduce((acc, log) => {
        const key = `${log.action}_${log.entity}`
        if (!acc[key]) {
          acc[key] = {
            action: log.action,
            entity: log.entity,
            count: 0
          }
        }
        acc[key].count++
        return acc
      }, {} as Record<string, any>)

      return {
        totalActions: logs.length,
        summary: Object.values(summary),
        period: `${days} days`
      }
    } catch (error) {
      console.error('Error getting user activity summary:', error)
      throw error
    }
  }
}
