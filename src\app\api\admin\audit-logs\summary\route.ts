import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { AuditService } from '@/lib/auditService'

// GET /api/admin/audit-logs/summary - Get user activity summary
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId') || session.user.id
    const days = parseInt(searchParams.get('days') || '30')

    // Check if user can view other users' activity
    if (userId !== session.user.id && !session.user.permissions?.canManageDepartments) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const summary = await AuditService.getUserActivitySummary(userId, days)

    return NextResponse.json(summary)
  } catch (error) {
    console.error('Error fetching activity summary:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
