'use client'

import { useState } from 'react'
import { useSession, signOut } from 'next-auth/react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import {
  LayoutDashboard,
  FileText,
  FolderOpen,
  CheckSquare,
  Users,
  Settings,
  LogOut,
  Menu,
  X,
  ChevronDown,
  ChevronRight
} from 'lucide-react'
import NotificationBell from '@/components/notifications/NotificationBell'

interface NavItem {
  name: string
  href: string
  icon: React.ComponentType<{ className?: string }>
  permission?: string
  children?: NavItem[]
}

export default function Sidebar() {
  const { data: session } = useSession()
  const pathname = usePathname()
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [expandedItems, setExpandedItems] = useState<string[]>([])

  const navigation: NavItem[] = [
    {
      name: 'Dashboard',
      href: '/dashboard',
      icon: LayoutDashboard
    },
    {
      name: 'Templates',
      href: '/templates',
      icon: FileText,
      permission: 'canCreateTemplate',
      children: [
        { name: 'All Templates', href: '/templates', icon: FileText },
        { name: 'Create Template', href: '/templates/create', icon: FileText },
        { name: 'Excel Template Builder', href: '/templates/excel', icon: FileText }
      ]
    },
    {
      name: 'Checksheets',
      href: '/checksheets',
      icon: CheckSquare,
      permission: 'canCreateChecksheet',
      children: [
        { name: 'All Checksheets', href: '/checksheets', icon: CheckSquare },
        { name: 'Create Checksheet', href: '/checksheets/create', icon: CheckSquare }
      ]
    },
    {
      name: 'Folders',
      href: '/folders',
      icon: FolderOpen
    },
    {
      name: 'Approvals',
      href: '/approvals',
      icon: CheckSquare,
      permission: 'canApproveLevel1'
    },
    {
      name: 'Administration',
      href: '/admin',
      icon: Settings,
      permission: 'canCreateUser',
      children: [
        { name: 'Users', href: '/admin/users', icon: Users },
        { name: 'Departments', href: '/admin/departments', icon: Settings },
        { name: 'Roles', href: '/admin/roles', icon: Settings },
        { name: 'Database Config', href: '/admin/database', icon: Settings },
        { name: 'Audit Logs', href: '/admin/audit-logs', icon: Settings }
      ]
    }
  ]

  const hasPermission = (permission?: string) => {
    if (!permission) return true
    return session?.user?.permissions?.[permission as keyof typeof session.user.permissions] === true
  }

  const toggleExpanded = (itemName: string) => {
    setExpandedItems(prev => 
      prev.includes(itemName) 
        ? prev.filter(name => name !== itemName)
        : [...prev, itemName]
    )
  }

  const isActive = (href: string) => {
    return pathname === href || pathname.startsWith(href + '/')
  }

  const isExpanded = (itemName: string) => {
    return expandedItems.includes(itemName) || 
           navigation.find(item => item.name === itemName)?.children?.some(child => isActive(child.href))
  }

  const NavLink = ({ item, isChild = false }: { item: NavItem; isChild?: boolean }) => {
    const active = isActive(item.href)
    const hasChildren = item.children && item.children.length > 0
    const expanded = isExpanded(item.name)

    if (!hasPermission(item.permission)) {
      return null
    }

    return (
      <div>
        <div
          className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md cursor-pointer ${
            active
              ? 'bg-primary-100 text-primary-900'
              : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
          } ${isChild ? 'ml-6' : ''}`}
          onClick={() => hasChildren ? toggleExpanded(item.name) : null}
        >
          {hasChildren ? (
            <>
              <item.icon
                className={`mr-3 flex-shrink-0 h-5 w-5 ${
                  active ? 'text-primary-500' : 'text-gray-400 group-hover:text-gray-500'
                }`}
              />
              <span className="flex-1">{item.name}</span>
              {expanded ? (
                <ChevronDown className="ml-2 h-4 w-4" />
              ) : (
                <ChevronRight className="ml-2 h-4 w-4" />
              )}
            </>
          ) : (
            <Link href={item.href} className="flex items-center w-full">
              <item.icon
                className={`mr-3 flex-shrink-0 h-5 w-5 ${
                  active ? 'text-primary-500' : 'text-gray-400 group-hover:text-gray-500'
                }`}
              />
              {item.name}
            </Link>
          )}
        </div>
        
        {hasChildren && expanded && (
          <div className="mt-1 space-y-1">
            {item.children?.map((child) => (
              <NavLink key={child.name} item={child} isChild />
            ))}
          </div>
        )}
      </div>
    )
  }

  return (
    <>
      {/* Mobile menu button */}
      <div className="lg:hidden fixed top-4 left-4 z-50">
        <button
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          className="bg-white p-2 rounded-md shadow-md"
        >
          {isMobileMenuOpen ? (
            <X className="h-6 w-6" />
          ) : (
            <Menu className="h-6 w-6" />
          )}
        </button>
      </div>

      {/* Sidebar */}
      <div className={`
        fixed inset-y-0 left-0 z-40 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out
        lg:translate-x-0 lg:static lg:inset-0
        ${isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full'}
      `}>
        <div className="flex flex-col h-full">
          {/* Logo */}
          <div className="flex items-center justify-between h-16 px-4 bg-primary-600">
            <h1 className="text-xl font-bold text-white">E-Checksheet</h1>
            <NotificationBell />
          </div>

          {/* User info */}
          <div className="px-4 py-4 border-b border-gray-200">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center">
                  <span className="text-sm font-medium text-primary-600">
                    {session?.user?.fullName?.charAt(0) || 'U'}
                  </span>
                </div>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-900">
                  {session?.user?.fullName}
                </p>
                <p className="text-xs text-gray-500">
                  {session?.user?.role} • {session?.user?.department}
                </p>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-4 space-y-1 overflow-y-auto">
            {navigation.map((item) => (
              <NavLink key={item.name} item={item} />
            ))}
          </nav>

          {/* Logout */}
          <div className="px-4 py-4 border-t border-gray-200">
            <button
              onClick={() => signOut()}
              className="group flex items-center w-full px-2 py-2 text-sm font-medium text-gray-600 rounded-md hover:bg-gray-50 hover:text-gray-900"
            >
              <LogOut className="mr-3 flex-shrink-0 h-5 w-5 text-gray-400 group-hover:text-gray-500" />
              Sign out
            </button>
          </div>
        </div>
      </div>

      {/* Mobile overlay */}
      {isMobileMenuOpen && (
        <div
          className="lg:hidden fixed inset-0 z-30 bg-black bg-opacity-50"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}
    </>
  )
}
