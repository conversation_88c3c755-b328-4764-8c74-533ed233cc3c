-- Create database if not exists
IF NOT EXISTS (SELECT * FROM sys.databases WHERE name = 'e_checksheet')
BEGIN
    CREATE DATABASE e_checksheet;
END
GO

USE e_checksheet;
GO

-- Create initial roles
INSERT INTO roles (id, name, description, permissions, createdAt, updatedAt) VALUES
('role_executor', 'executor', 'Người thực hiện checksheet', 
 '{"canCreateChecksheet": true, "canEditOwnChecksheet": true, "canViewOwnChecksheet": true, "canCreateTemplate": true, "canEditOwnTemplate": true}',
 GETDATE(), GETDATE()),
('role_approver1', 'approver1', 'Người duyệt cấp 1', 
 '{"canCreateChecksheet": true, "canEditOwnChecksheet": true, "canViewOwnChecksheet": true, "canCreateTemplate": true, "canEditOwnTemplate": true, "canApproveLevel1": true, "canViewDepartmentChecksheets": true}',
 GETDATE(), GETDATE()),
('role_final_approver', 'final_approver', 'Người duyệt cuối', 
 '{"canCreateChecksheet": true, "canEditOwnChecksheet": true, "canViewOwnChecksheet": true, "canCreateTemplate": true, "canEditOwnTemplate": true, "canApproveLevel1": true, "canApproveFinal": true, "canViewAllChecksheets": true, "canCreateUser": true, "canManageRoles": true, "canManageDepartments": true}',
 GETDATE(), GETDATE());

-- Create initial departments
INSERT INTO departments (id, name, description, isActive, createdAt, updatedAt) VALUES
('dept_it', 'IT Department', 'Information Technology Department', 1, GETDATE(), GETDATE()),
('dept_hr', 'HR Department', 'Human Resources Department', 1, GETDATE(), GETDATE()),
('dept_finance', 'Finance Department', 'Finance Department', 1, GETDATE(), GETDATE()),
('dept_operations', 'Operations Department', 'Operations Department', 1, GETDATE(), GETDATE());

-- Create default admin user (password: admin123)
-- Note: In production, this should be created through the application with proper password hashing
INSERT INTO users (id, username, email, password, fullName, isActive, departmentId, roleId, createdAt, updatedAt) VALUES
('user_admin', 'admin', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'System Administrator', 1, 'dept_it', 'role_final_approver', GETDATE(), GETDATE());

-- Create sample folders
INSERT INTO folders (id, name, description, isActive, departmentId, createdById, parentId, createdAt, updatedAt) VALUES
('folder_it_templates', 'IT Templates', 'Templates for IT Department', 1, 'dept_it', 'user_admin', NULL, GETDATE(), GETDATE()),
('folder_hr_templates', 'HR Templates', 'Templates for HR Department', 1, 'dept_hr', 'user_admin', NULL, GETDATE(), GETDATE()),
('folder_finance_templates', 'Finance Templates', 'Templates for Finance Department', 1, 'dept_finance', 'user_admin', NULL, GETDATE(), GETDATE()),
('folder_operations_templates', 'Operations Templates', 'Templates for Operations Department', 1, 'dept_operations', 'user_admin', NULL, GETDATE(), GETDATE());

GO
