# E-Checksheet Management System

Hệ thống quản lý checksheet với workflow phê duyệt đa cấp, phân quyền theo bộ phận và người dùng.

## Tính năng chính

### 🔐 Hệ thống phân quyền 3 cấp
- **Executor (<PERSON><PERSON><PERSON><PERSON> thự<PERSON> hiện)**: Tạo và quản lý checksheet, tạo template
- **Approver 1 (Ngư<PERSON><PERSON> du<PERSON> cấp 1)**: <PERSON><PERSON> đầy đủ quyền của Executor + duyệt cấp 1
- **Final Approver (Người duyệt cuối)**: <PERSON><PERSON> đầy đủ quyền + duyệt cuối + quản lý user

### 📋 Quản lý Template và Checksheet
- Tạo form template với dynamic fields (text, textarea, number, date, select, checkbox, radio)
- Tạo checksheet từ template
- Lưu draft, submit để duyệt
- Workflow: Draft → Submitted → Approved Level 1 → Approved Final

### 📁 Quản lý Folder
- Tổ chức template và checksheet theo folder
- Phân quyền theo department

### ✅ Workflow phê duyệt
- Multi-level approval: Executor → Approver 1 → Final Approver
- Tracking trạng thái và comments
- Email notifications (tùy chọn)

### 📊 Dashboard và Báo cáo
- Thống kê theo status, department, user
- Filtering và search
- Recent activity

## Công nghệ sử dụng

- **Frontend**: Next.js 14, React, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes, Prisma ORM
- **Database**: SQL Server
- **Authentication**: NextAuth.js với JWT
- **UI Components**: Custom components với Tailwind CSS

## Cài đặt và Chạy

### Yêu cầu hệ thống
- Node.js 18+
- SQL Server (Local hoặc Remote)
- npm hoặc yarn

### 1. Clone repository
```bash
git clone <repository-url>
cd E_checksheet
```

### 2. Cài đặt dependencies
```bash
npm install
```

### 3. Cấu hình database
Tạo file `.env.local` từ `.env.example`:
```bash
cp .env.example .env.local
```

Cập nhật connection string trong `.env.local`:
```env
DATABASE_URL="sqlserver://localhost:1433;database=e_checksheet;user=sa;password=YourPassword123;trustServerCertificate=true"
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key-here
JWT_SECRET=your-jwt-secret-here
```

### 4. Khởi tạo database
```bash
# Generate Prisma client
npm run db:generate

# Push schema to database
npm run db:push

# Seed initial data
npm run db:seed
```

### 5. Chạy ứng dụng
```bash
npm run dev
```

Ứng dụng sẽ chạy tại: http://localhost:3000

## Tài khoản mặc định

Sau khi seed data, bạn có thể đăng nhập với:

### Admin (Final Approver)
- Username: `admin`
- Password: `admin123`
- Quyền: Toàn quyền hệ thống

### Sample Users
- Username: `john.executor` / Password: `password123` (Executor)
- Username: `jane.approver` / Password: `password123` (Approver 1)
- Username: `bob.hr` / Password: `password123` (HR Approver 1)

## Cấu trúc dự án

```
src/
├── app/                    # Next.js App Router
│   ├── api/               # API endpoints
│   ├── auth/              # Authentication pages
│   ├── dashboard/         # Dashboard page
│   └── globals.css        # Global styles
├── components/            # React components
│   ├── layout/           # Layout components
│   ├── ui/               # UI components
│   ├── templates/        # Template components
│   ├── checksheets/      # Checksheet components
│   └── approvals/        # Approval components
├── lib/                  # Utility libraries
│   ├── auth.ts           # NextAuth configuration
│   ├── prisma.ts         # Prisma client
│   └── permissions.ts    # Permission utilities
├── types/                # TypeScript types
└── scripts/              # Database scripts
```

## API Endpoints

### Authentication
- `POST /api/auth/signin` - Đăng nhập
- `POST /api/auth/signout` - Đăng xuất

### Users & Roles
- `GET /api/users` - Lấy danh sách users
- `POST /api/users` - Tạo user mới (Final Approver only)
- `GET /api/roles` - Lấy danh sách roles
- `GET /api/departments` - Lấy danh sách departments

### Folders
- `GET /api/folders` - Lấy danh sách folders
- `POST /api/folders` - Tạo folder mới
- `PUT /api/folders/[id]` - Cập nhật folder
- `DELETE /api/folders/[id]` - Xóa folder

### Templates
- `GET /api/templates` - Lấy danh sách templates
- `POST /api/templates` - Tạo template mới
- `GET /api/templates/[id]` - Lấy template theo ID
- `PUT /api/templates/[id]` - Cập nhật template
- `DELETE /api/templates/[id]` - Xóa template

### Checksheets
- `GET /api/checksheets` - Lấy danh sách checksheets
- `POST /api/checksheets` - Tạo checksheet mới
- `GET /api/checksheets/[id]` - Lấy checksheet theo ID
- `PUT /api/checksheets/[id]` - Cập nhật checksheet
- `POST /api/checksheets/[id]/submit` - Submit checksheet để duyệt
- `DELETE /api/checksheets/[id]` - Xóa checksheet

### Approvals
- `GET /api/approvals` - Lấy danh sách approvals
- `PUT /api/approvals/[id]` - Approve/Reject checksheet

### Dashboard
- `GET /api/dashboard/stats` - Lấy thống kê dashboard

## Deployment

### Production Build
```bash
npm run build
npm start
```

### Environment Variables cho Production
```env
NODE_ENV=production
DATABASE_URL="your-production-database-url"
NEXTAUTH_URL="https://your-domain.com"
NEXTAUTH_SECRET="your-production-secret"
JWT_SECRET="your-production-jwt-secret"
```

### Docker Deployment (Optional)
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]
```

## Bảo mật

- Passwords được hash với bcrypt
- JWT tokens cho session management
- Role-based access control (RBAC)
- Input validation với Zod
- SQL injection protection với Prisma

## Hỗ trợ

Nếu gặp vấn đề, vui lòng:
1. Kiểm tra logs trong console
2. Đảm bảo database connection đúng
3. Kiểm tra environment variables
4. Restart ứng dụng

## License

MIT License
