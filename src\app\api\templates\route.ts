import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const fieldSchema = z.object({
  id: z.string(),
  type: z.enum(['text', 'textarea', 'number', 'date', 'select', 'checkbox', 'radio']),
  label: z.string(),
  required: z.boolean().default(false),
  placeholder: z.string().optional(),
  options: z.array(z.string()).optional(), // For select, radio
  validation: z.object({
    min: z.number().optional(),
    max: z.number().optional(),
    pattern: z.string().optional()
  }).optional()
})

const createTemplateSchema = z.object({
  name: z.string().min(1, 'Template name is required'),
  description: z.string().optional(),
  version: z.string().default('1.0'),
  fields: z.array(fieldSchema),
  folderId: z.string().optional()
})

// GET /api/templates - Get templates with filtering
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const folderId = searchParams.get('folderId')
    const departmentId = searchParams.get('departmentId')

    const skip = (page - 1) * limit

    // Build where clause based on user permissions
    let where: any = { isActive: true }

    if (search) {
      where.OR = [
        { name: { contains: search } },
        { description: { contains: search } }
      ]
    }

    if (folderId) {
      where.folderId = folderId
    }

    if (departmentId) {
      where.departmentId = departmentId
    } else if (!session.user.permissions?.canViewAllChecksheets) {
      // If user can't view all, limit to their department
      where.departmentId = session.user.departmentId
    }

    const [templates, total] = await Promise.all([
      prisma.checksheetTemplate.findMany({
        where,
        skip,
        take: limit,
        include: {
          department: true,
          createdBy: {
            select: {
              id: true,
              username: true,
              fullName: true
            }
          },
          folder: {
            select: {
              id: true,
              name: true
            }
          },
          _count: {
            select: {
              checksheets: true
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.checksheetTemplate.count({ where })
    ])

    return NextResponse.json({
      templates,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('Error fetching templates:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// POST /api/templates - Create new template
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user can create templates
    if (!session.user.permissions?.canCreateTemplate) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const body = await request.json()
    const validatedData = createTemplateSchema.parse(body)

    // Check if folder exists and user has access
    if (validatedData.folderId) {
      const folder = await prisma.folder.findUnique({
        where: { id: validatedData.folderId }
      })

      if (!folder) {
        return NextResponse.json({ error: 'Folder not found' }, { status: 404 })
      }

      if (folder.departmentId !== session.user.departmentId && 
          !session.user.permissions?.canViewAllChecksheets) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
      }
    }

    // Create template
    const template = await prisma.checksheetTemplate.create({
      data: {
        ...validatedData,
        departmentId: session.user.departmentId,
        createdById: session.user.id
      },
      include: {
        department: true,
        createdBy: {
          select: {
            id: true,
            username: true,
            fullName: true
          }
        },
        folder: {
          select: {
            id: true,
            name: true
          }
        },
        _count: {
          select: {
            checksheets: true
          }
        }
      }
    })

    return NextResponse.json(template, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error creating template:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
