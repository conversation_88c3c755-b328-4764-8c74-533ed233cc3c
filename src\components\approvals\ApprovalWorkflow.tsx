'use client'

import { useState } from 'react'
import { Check, X, Clock, User, MessageSquare } from 'lucide-react'

interface Approval {
  id: string
  level: number
  status: 'PENDING' | 'APPROVED' | 'REJECTED'
  comments?: string
  approvedAt?: string
  approver: {
    id: string
    username: string
    fullName: string
  }
}

interface ApprovalWorkflowProps {
  approvals: Approval[]
  currentUserCanApprove?: boolean
  onApprove?: (approvalId: string, comments?: string) => Promise<void>
  onReject?: (approvalId: string, comments?: string) => Promise<void>
  isLoading?: boolean
}

export default function ApprovalWorkflow({
  approvals,
  currentUserCanApprove = false,
  onApprove,
  onReject,
  isLoading = false
}: ApprovalWorkflowProps) {
  const [selectedApproval, setSelectedApproval] = useState<string | null>(null)
  const [comments, setComments] = useState('')
  const [actionType, setActionType] = useState<'approve' | 'reject' | null>(null)

  const handleAction = async (approvalId: string, action: 'approve' | 'reject') => {
    setSelectedApproval(approvalId)
    setActionType(action)
  }

  const confirmAction = async () => {
    if (!selectedApproval || !actionType) return

    try {
      if (actionType === 'approve' && onApprove) {
        await onApprove(selectedApproval, comments)
      } else if (actionType === 'reject' && onReject) {
        await onReject(selectedApproval, comments)
      }
      
      setSelectedApproval(null)
      setActionType(null)
      setComments('')
    } catch (error) {
      console.error('Error processing approval:', error)
    }
  }

  const cancelAction = () => {
    setSelectedApproval(null)
    setActionType(null)
    setComments('')
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'APPROVED':
        return <Check className="w-5 h-5 text-green-600" />
      case 'REJECTED':
        return <X className="w-5 h-5 text-red-600" />
      case 'PENDING':
        return <Clock className="w-5 h-5 text-yellow-600" />
      default:
        return <Clock className="w-5 h-5 text-gray-400" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'APPROVED':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'REJECTED':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const sortedApprovals = [...approvals].sort((a, b) => a.level - b.level)

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      <h3 className="text-lg font-medium mb-4">Approval Workflow</h3>
      
      <div className="space-y-4">
        {sortedApprovals.map((approval, index) => (
          <div key={approval.id} className="relative">
            {/* Connection line */}
            {index < sortedApprovals.length - 1 && (
              <div className="absolute left-6 top-12 w-0.5 h-8 bg-gray-300"></div>
            )}
            
            <div className={`flex items-start gap-4 p-4 rounded-lg border ${getStatusColor(approval.status)}`}>
              <div className="flex-shrink-0 w-12 h-12 rounded-full bg-white border-2 border-current flex items-center justify-center">
                {getStatusIcon(approval.status)}
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">
                      Level {approval.level} Approval
                    </h4>
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <User className="w-4 h-4" />
                      <span>{approval.approver.fullName}</span>
                      <span className="text-gray-400">({approval.approver.username})</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(approval.status)}`}>
                      {approval.status}
                    </span>
                    
                    {approval.status === 'PENDING' && currentUserCanApprove && (
                      <div className="flex gap-2">
                        <button
                          onClick={() => handleAction(approval.id, 'approve')}
                          disabled={isLoading}
                          className="px-3 py-1 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 disabled:opacity-50"
                        >
                          Approve
                        </button>
                        <button
                          onClick={() => handleAction(approval.id, 'reject')}
                          disabled={isLoading}
                          className="px-3 py-1 bg-red-600 text-white text-sm rounded-md hover:bg-red-700 disabled:opacity-50"
                        >
                          Reject
                        </button>
                      </div>
                    )}
                  </div>
                </div>
                
                {approval.comments && (
                  <div className="mt-2 p-2 bg-white bg-opacity-50 rounded border">
                    <div className="flex items-start gap-2">
                      <MessageSquare className="w-4 h-4 text-gray-500 mt-0.5" />
                      <p className="text-sm text-gray-700">{approval.comments}</p>
                    </div>
                  </div>
                )}
                
                {approval.approvedAt && (
                  <p className="mt-1 text-xs text-gray-500">
                    {approval.status === 'APPROVED' ? 'Approved' : 'Rejected'} on{' '}
                    {new Date(approval.approvedAt).toLocaleString()}
                  </p>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Action Confirmation Modal */}
      {selectedApproval && actionType && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-medium mb-4">
              {actionType === 'approve' ? 'Approve' : 'Reject'} Checksheet
            </h3>
            
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Comments {actionType === 'reject' && <span className="text-red-500">*</span>}
              </label>
              <textarea
                value={comments}
                onChange={(e) => setComments(e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                placeholder={`Add comments for ${actionType}...`}
              />
            </div>
            
            <div className="flex justify-end gap-3">
              <button
                onClick={cancelAction}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
              >
                Cancel
              </button>
              <button
                onClick={confirmAction}
                disabled={actionType === 'reject' && !comments.trim()}
                className={`px-4 py-2 text-white rounded-md disabled:opacity-50 ${
                  actionType === 'approve'
                    ? 'bg-green-600 hover:bg-green-700'
                    : 'bg-red-600 hover:bg-red-700'
                }`}
              >
                {actionType === 'approve' ? 'Approve' : 'Reject'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
