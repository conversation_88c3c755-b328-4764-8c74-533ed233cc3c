{"name": "@types/mssql", "version": "9.1.7", "description": "TypeScript definitions for mssql", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/mssql", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/elhaard"}, {"name": "<PERSON>", "githubUsername": "pkeuter", "url": "https://github.com/pkeuter"}, {"name": "<PERSON>", "githubUsername": "woodenconsulting", "url": "https://github.com/woodenconsulting"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/cahilfoley"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "a<PERSON><PERSON><PERSON>", "url": "https://github.com/achrinza"}, {"name": "<PERSON>", "githubUsername": "d<PERSON><PERSON>", "url": "https://github.com/dhensby"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/mssql"}, "scripts": {}, "dependencies": {"@types/node": "*", "tedious": "*", "tarn": "^3.0.1"}, "peerDependencies": {}, "typesPublisherContentHash": "bdfa41874fe8e7b51a564593e26d556f50f4dfd624e17c9cba87e878e1b61064", "typeScriptVersion": "5.0"}