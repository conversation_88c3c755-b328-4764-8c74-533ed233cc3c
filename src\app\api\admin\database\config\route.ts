import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import fs from 'fs'
import path from 'path'
import { z } from 'zod'

const databaseConfigSchema = z.object({
  host: z.string().min(1, 'Host is required'),
  port: z.number().min(1).max(65535),
  database: z.string().min(1, 'Database name is required'),
  username: z.string().min(1, 'Username is required'),
  password: z.string().min(1, 'Password is required'),
  trustServerCertificate: z.boolean().default(true),
  encrypt: z.boolean().default(false),
  connectionTimeout: z.number().min(1000).max(60000).default(30000)
})

// GET /api/admin/database/config - Get current database configuration
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is final approver (admin)
    if (!session.user.permissions?.canManageDepartments) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Read current configuration from environment
    const currentConfig = {
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '1433'),
      database: process.env.DB_NAME || 'e_checksheet',
      username: process.env.DB_USER || 'sa',
      // Don't return password for security
      password: '***hidden***',
      trustServerCertificate: process.env.DB_TRUST_CERT === 'true',
      encrypt: process.env.DB_ENCRYPT === 'true',
      connectionTimeout: parseInt(process.env.DB_TIMEOUT || '30000')
    }

    return NextResponse.json(currentConfig)
  } catch (error) {
    console.error('Error fetching database config:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// POST /api/admin/database/config - Update database configuration
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is final approver (admin)
    if (!session.user.permissions?.canManageDepartments) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const body = await request.json()
    const validatedData = databaseConfigSchema.parse(body)

    // Create new DATABASE_URL
    const databaseUrl = `sqlserver://${validatedData.host}:${validatedData.port};database=${validatedData.database};user=${validatedData.username};password=${validatedData.password};trustServerCertificate=${validatedData.trustServerCertificate};encrypt=${validatedData.encrypt};connectionTimeout=${validatedData.connectionTimeout}`

    // Read current .env.local file
    const envPath = path.join(process.cwd(), '.env.local')
    let envContent = ''
    
    try {
      envContent = fs.readFileSync(envPath, 'utf8')
    } catch (error) {
      // File doesn't exist, create new content
      envContent = ''
    }

    // Update or add DATABASE_URL
    const envLines = envContent.split('\n')
    let databaseUrlUpdated = false

    const updatedLines = envLines.map(line => {
      if (line.startsWith('DATABASE_URL=')) {
        databaseUrlUpdated = true
        return `DATABASE_URL="${databaseUrl}"`
      }
      return line
    })

    // If DATABASE_URL wasn't found, add it
    if (!databaseUrlUpdated) {
      updatedLines.push(`DATABASE_URL="${databaseUrl}"`)
    }

    // Add individual config variables for easier access
    const configVars = [
      `DB_HOST=${validatedData.host}`,
      `DB_PORT=${validatedData.port}`,
      `DB_NAME=${validatedData.database}`,
      `DB_USER=${validatedData.username}`,
      `DB_PASSWORD=${validatedData.password}`,
      `DB_TRUST_CERT=${validatedData.trustServerCertificate}`,
      `DB_ENCRYPT=${validatedData.encrypt}`,
      `DB_TIMEOUT=${validatedData.connectionTimeout}`
    ]

    // Remove existing config vars and add new ones
    const filteredLines = updatedLines.filter(line => 
      !line.startsWith('DB_HOST=') &&
      !line.startsWith('DB_PORT=') &&
      !line.startsWith('DB_NAME=') &&
      !line.startsWith('DB_USER=') &&
      !line.startsWith('DB_PASSWORD=') &&
      !line.startsWith('DB_TRUST_CERT=') &&
      !line.startsWith('DB_ENCRYPT=') &&
      !line.startsWith('DB_TIMEOUT=')
    )

    const finalContent = [...filteredLines, ...configVars].join('\n')

    // Write updated .env.local file
    fs.writeFileSync(envPath, finalContent)

    return NextResponse.json({ 
      message: 'Database configuration updated successfully',
      note: 'Application restart required for changes to take effect'
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error updating database config:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
