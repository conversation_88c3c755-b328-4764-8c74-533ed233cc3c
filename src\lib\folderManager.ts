import { prisma } from './prisma'

export class FolderManager {
  // Auto-organize checksheet into appropriate folder based on status
  static async organizeChecksheet(checksheetId: string, status: string, departmentId: string) {
    try {
      // Find the appropriate status folder for this department
      const statusFolderName = this.getStatusFolderName(status)
      
      const folder = await prisma.folder.findFirst({
        where: {
          name: {
            contains: statusFolderName
          },
          departmentId: departmentId,
          isActive: true
        }
      })

      if (folder) {
        // Move checksheet to the appropriate folder
        await prisma.checksheet.update({
          where: { id: checksheetId },
          data: { folderId: folder.id }
        })

        console.log(`Checksheet ${checksheetId} moved to folder: ${folder.name}`)
        return folder
      }

      return null
    } catch (error) {
      console.error('Error organizing checksheet:', error)
      return null
    }
  }

  // Get folder name based on checksheet status
  private static getStatusFolderName(status: string): string {
    switch (status) {
      case 'DRAFT':
        return 'In Progress'
      case 'SUBMITTED':
        return 'Pending Level 1 Approval'
      case 'APPROVED_LEVEL_1':
        return 'Pending Final Approval'
      case 'APPROVED_FINAL':
        return 'Approved'
      case 'REJECTED':
        return 'Rejected'
      default:
        return 'In Progress'
    }
  }

  // Create default folders for a department
  static async createDefaultFolders(departmentId: string, createdById: string) {
    try {
      const department = await prisma.department.findUnique({
        where: { id: departmentId }
      })

      if (!department) {
        throw new Error('Department not found')
      }

      const statuses = [
        { name: 'In Progress', description: 'Checksheets đang thực hiện' },
        { name: 'Pending Level 1 Approval', description: 'Checksheets chờ duyệt cấp 1' },
        { name: 'Pending Final Approval', description: 'Checksheets chờ duyệt cuối' },
        { name: 'Approved', description: 'Checksheets đã được duyệt' },
        { name: 'Rejected', description: 'Checksheets bị từ chối' }
      ]

      const folders = []
      
      for (const status of statuses) {
        const existingFolder = await prisma.folder.findFirst({
          where: {
            name: `${department.name} - ${status.name}`,
            departmentId: departmentId
          }
        })

        if (!existingFolder) {
          const folder = await prisma.folder.create({
            data: {
              name: `${department.name} - ${status.name}`,
              description: `${status.description} - ${department.name}`,
              departmentId: departmentId,
              createdById: createdById
            }
          })
          folders.push(folder)
        }
      }

      return folders
    } catch (error) {
      console.error('Error creating default folders:', error)
      throw error
    }
  }

  // Get status folder for a department
  static async getStatusFolder(departmentId: string, status: string) {
    const statusFolderName = this.getStatusFolderName(status)
    
    return await prisma.folder.findFirst({
      where: {
        name: {
          contains: statusFolderName
        },
        departmentId: departmentId,
        isActive: true
      }
    })
  }

  // Move all checksheets of a specific status to appropriate folders
  static async organizeAllChecksheets() {
    try {
      const checksheets = await prisma.checksheet.findMany({
        include: {
          department: true
        }
      })

      let organized = 0
      
      for (const checksheet of checksheets) {
        const folder = await this.organizeChecksheet(
          checksheet.id, 
          checksheet.status, 
          checksheet.departmentId
        )
        
        if (folder) {
          organized++
        }
      }

      console.log(`Organized ${organized} checksheets into status folders`)
      return organized
    } catch (error) {
      console.error('Error organizing all checksheets:', error)
      throw error
    }
  }
}
