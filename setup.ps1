Write-Host "========================================" -ForegroundColor Green
Write-Host "E-Checksheet Management System Setup" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

Write-Host ""
Write-Host "1. Installing dependencies..." -ForegroundColor Yellow
npm install --legacy-peer-deps

if ($LASTEXITCODE -ne 0) {
    Write-Host "Error installing dependencies!" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "2. Installing additional dependencies..." -ForegroundColor Yellow
npm install mssql @types/mssql xlsx react-data-grid @types/xlsx class-variance-authority clsx nodemailer@^6.9.0 @types/nodemailer tsx --save-dev --legacy-peer-deps

if ($LASTEXITCODE -ne 0) {
    Write-Host "Error installing additional dependencies!" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "3. Generating Prisma client..." -ForegroundColor Yellow
npm run db:generate

if ($LASTEXITCODE -ne 0) {
    Write-Host "Error generating Prisma client!" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "4. Setting up database..." -ForegroundColor Yellow
npm run db:push

if ($LASTEXITCODE -ne 0) {
    Write-Host "Error setting up database!" -ForegroundColor Red
    Write-Host "Please check your database connection in .env.local" -ForegroundColor Yellow
    exit 1
}

Write-Host ""
Write-Host "5. Seeding initial data..." -ForegroundColor Yellow
npm run db:seed

if ($LASTEXITCODE -ne 0) {
    Write-Host "Error seeding database!" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "Setup completed successfully!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "You can now start the application with:" -ForegroundColor Cyan
Write-Host "npm run dev" -ForegroundColor White
Write-Host ""
Write-Host "Default admin login:" -ForegroundColor Cyan
Write-Host "Username: admin" -ForegroundColor White
Write-Host "Password: admin123" -ForegroundColor White
Write-Host ""
Write-Host "Application will be available at:" -ForegroundColor Cyan
Write-Host "http://localhost:3000" -ForegroundColor White
Write-Host "========================================" -ForegroundColor Green

Write-Host ""
Write-Host "Press any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
