import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const fieldSchema = z.object({
  id: z.string(),
  type: z.enum(['text', 'textarea', 'number', 'date', 'select', 'checkbox', 'radio']),
  label: z.string(),
  required: z.boolean().default(false),
  placeholder: z.string().optional(),
  options: z.array(z.string()).optional(),
  validation: z.object({
    min: z.number().optional(),
    max: z.number().optional(),
    pattern: z.string().optional()
  }).optional()
})

const updateTemplateSchema = z.object({
  name: z.string().min(1).optional(),
  description: z.string().optional(),
  version: z.string().optional(),
  fields: z.array(fieldSchema).optional(),
  folderId: z.string().optional()
})

// GET /api/templates/[id] - Get template by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const template = await prisma.checksheetTemplate.findUnique({
      where: { id: params.id },
      include: {
        department: true,
        createdBy: {
          select: {
            id: true,
            username: true,
            fullName: true
          }
        },
        folder: {
          select: {
            id: true,
            name: true
          }
        },
        checksheets: {
          select: {
            id: true,
            title: true,
            status: true,
            createdAt: true,
            createdBy: {
              select: {
                id: true,
                username: true,
                fullName: true
              }
            }
          },
          orderBy: { createdAt: 'desc' },
          take: 10
        },
        _count: {
          select: {
            checksheets: true
          }
        }
      }
    })

    if (!template) {
      return NextResponse.json({ error: 'Template not found' }, { status: 404 })
    }

    // Check if user can access this template
    if (template.departmentId !== session.user.departmentId && 
        !session.user.permissions?.canViewAllChecksheets) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    return NextResponse.json(template)
  } catch (error) {
    console.error('Error fetching template:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// PUT /api/templates/[id] - Update template
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = updateTemplateSchema.parse(body)

    // Check if template exists
    const existingTemplate = await prisma.checksheetTemplate.findUnique({
      where: { id: params.id }
    })

    if (!existingTemplate) {
      return NextResponse.json({ error: 'Template not found' }, { status: 404 })
    }

    // Check if user can edit this template
    const canEdit = existingTemplate.createdById === session.user.id || 
                   session.user.permissions?.canViewAllChecksheets

    if (!canEdit) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Check if folder exists and user has access
    if (validatedData.folderId) {
      const folder = await prisma.folder.findUnique({
        where: { id: validatedData.folderId }
      })

      if (!folder) {
        return NextResponse.json({ error: 'Folder not found' }, { status: 404 })
      }

      if (folder.departmentId !== session.user.departmentId && 
          !session.user.permissions?.canViewAllChecksheets) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
      }
    }

    // Update template
    const template = await prisma.checksheetTemplate.update({
      where: { id: params.id },
      data: validatedData,
      include: {
        department: true,
        createdBy: {
          select: {
            id: true,
            username: true,
            fullName: true
          }
        },
        folder: {
          select: {
            id: true,
            name: true
          }
        },
        _count: {
          select: {
            checksheets: true
          }
        }
      }
    })

    return NextResponse.json(template)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error updating template:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// DELETE /api/templates/[id] - Delete template
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if template exists
    const template = await prisma.checksheetTemplate.findUnique({
      where: { id: params.id },
      include: {
        _count: {
          select: {
            checksheets: true
          }
        }
      }
    })

    if (!template) {
      return NextResponse.json({ error: 'Template not found' }, { status: 404 })
    }

    // Check if user can delete this template
    const canDelete = template.createdById === session.user.id || 
                     session.user.permissions?.canViewAllChecksheets

    if (!canDelete) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Check if template is being used
    if (template._count.checksheets > 0) {
      return NextResponse.json(
        { error: 'Cannot delete template that is being used by checksheets' },
        { status: 400 }
      )
    }

    // Soft delete template
    await prisma.checksheetTemplate.update({
      where: { id: params.id },
      data: { isActive: false }
    })

    return NextResponse.json({ message: 'Template deleted successfully' })
  } catch (error) {
    console.error('Error deleting template:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
