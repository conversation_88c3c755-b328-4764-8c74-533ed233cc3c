'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Save, Send, FileText } from 'lucide-react'

interface TemplateField {
  id: string
  type: 'text' | 'textarea' | 'number' | 'date' | 'select' | 'checkbox' | 'radio'
  label: string
  required: boolean
  placeholder?: string
  options?: string[]
  validation?: {
    min?: number
    max?: number
    pattern?: string
  }
}

interface ChecksheetData {
  id?: string
  title: string
  description?: string
  data: Record<string, any>
  status: string
  template: {
    id: string
    name: string
    version: string
    fields: TemplateField[]
  }
}

interface ChecksheetFormProps {
  checksheet?: ChecksheetData
  template?: {
    id: string
    name: string
    version: string
    fields: TemplateField[]
  }
  onSave: (data: any) => Promise<void>
  onSubmit?: (data: any) => Promise<void>
  onCancel: () => void
  isLoading?: boolean
  readOnly?: boolean
}

export default function ChecksheetForm({
  checksheet,
  template,
  onSave,
  onSubmit,
  onCancel,
  isLoading = false,
  readOnly = false
}: ChecksheetFormProps) {
  const [isSaving, setIsSaving] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const currentTemplate = checksheet?.template || template
  if (!currentTemplate) {
    return <div>No template data available</div>
  }

  // Create dynamic schema based on template fields
  const createSchema = () => {
    const schemaFields: Record<string, any> = {
      title: z.string().min(1, 'Title is required'),
      description: z.string().optional()
    }

    currentTemplate.fields.forEach((field) => {
      let fieldSchema: any

      switch (field.type) {
        case 'text':
        case 'textarea':
          fieldSchema = z.string()
          if (field.validation?.pattern) {
            fieldSchema = fieldSchema.regex(new RegExp(field.validation.pattern))
          }
          break
        case 'number':
          fieldSchema = z.number()
          if (field.validation?.min !== undefined) {
            fieldSchema = fieldSchema.min(field.validation.min)
          }
          if (field.validation?.max !== undefined) {
            fieldSchema = fieldSchema.max(field.validation.max)
          }
          break
        case 'date':
          fieldSchema = z.string()
          break
        case 'select':
        case 'radio':
          fieldSchema = z.string()
          break
        case 'checkbox':
          fieldSchema = z.boolean()
          break
        default:
          fieldSchema = z.string()
      }

      if (field.required) {
        schemaFields[field.id] = fieldSchema
      } else {
        schemaFields[field.id] = fieldSchema.optional()
      }
    })

    return z.object(schemaFields)
  }

  const schema = createSchema()
  type FormData = z.infer<typeof schema>

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch
  } = useForm<FormData>({
    resolver: zodResolver(schema),
    defaultValues: {
      title: checksheet?.title || '',
      description: checksheet?.description || '',
      ...checksheet?.data
    }
  })

  const handleSave = async (data: FormData) => {
    setIsSaving(true)
    try {
      const { title, description, ...fieldData } = data
      await onSave({
        title,
        description,
        data: fieldData
      })
    } finally {
      setIsSaving(false)
    }
  }

  const handleSubmitForApproval = async (data: FormData) => {
    if (!onSubmit) return
    
    setIsSubmitting(true)
    try {
      const { title, description, ...fieldData } = data
      await onSubmit({
        title,
        description,
        data: fieldData
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const renderField = (field: TemplateField) => {
    const fieldError = errors[field.id as keyof FormData]
    const commonProps = {
      ...register(field.id as keyof FormData),
      disabled: readOnly,
      className: `w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 ${
        readOnly ? 'bg-gray-100' : ''
      }`
    }

    switch (field.type) {
      case 'text':
        return (
          <input
            type="text"
            placeholder={field.placeholder}
            {...commonProps}
          />
        )

      case 'textarea':
        return (
          <textarea
            rows={4}
            placeholder={field.placeholder}
            {...commonProps}
          />
        )

      case 'number':
        return (
          <input
            type="number"
            placeholder={field.placeholder}
            min={field.validation?.min}
            max={field.validation?.max}
            {...commonProps}
          />
        )

      case 'date':
        return (
          <input
            type="date"
            {...commonProps}
          />
        )

      case 'select':
        return (
          <select {...commonProps}>
            <option value="">Select an option</option>
            {field.options?.map((option) => (
              <option key={option} value={option}>
                {option}
              </option>
            ))}
          </select>
        )

      case 'radio':
        return (
          <div className="space-y-2">
            {field.options?.map((option) => (
              <label key={option} className="flex items-center">
                <input
                  type="radio"
                  value={option}
                  {...register(field.id as keyof FormData)}
                  disabled={readOnly}
                  className="mr-2"
                />
                {option}
              </label>
            ))}
          </div>
        )

      case 'checkbox':
        return (
          <label className="flex items-center">
            <input
              type="checkbox"
              {...register(field.id as keyof FormData)}
              disabled={readOnly}
              className="mr-2"
            />
            {field.label}
          </label>
        )

      default:
        return null
    }
  }

  const canEdit = !readOnly && checksheet?.status === 'DRAFT'
  const canSubmit = canEdit && onSubmit

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold">
            {checksheet ? 'Edit Checksheet' : 'New Checksheet'}
          </h2>
          <p className="text-gray-600">
            Template: {currentTemplate.name} (v{currentTemplate.version})
          </p>
        </div>
        {checksheet && (
          <div className="flex items-center gap-2">
            <span className={`px-3 py-1 rounded-full text-sm font-medium ${
              checksheet.status === 'DRAFT' ? 'bg-gray-100 text-gray-800' :
              checksheet.status === 'SUBMITTED' ? 'bg-yellow-100 text-yellow-800' :
              checksheet.status === 'APPROVED_LEVEL_1' ? 'bg-blue-100 text-blue-800' :
              checksheet.status === 'APPROVED_FINAL' ? 'bg-green-100 text-green-800' :
              'bg-red-100 text-red-800'
            }`}>
              {checksheet.status.replace('_', ' ')}
            </span>
          </div>
        )}
      </div>

      <form className="space-y-6">
        {/* Basic Info */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Title *
            </label>
            <input
              {...register('title')}
              type="text"
              disabled={readOnly}
              className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 ${
                readOnly ? 'bg-gray-100' : ''
              }`}
              placeholder="Enter checksheet title"
            />
            {errors.title && (
              <p className="mt-1 text-sm text-red-600">{errors.title.message}</p>
            )}
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Description
          </label>
          <textarea
            {...register('description')}
            rows={3}
            disabled={readOnly}
            className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 ${
              readOnly ? 'bg-gray-100' : ''
            }`}
            placeholder="Enter description"
          />
        </div>

        {/* Dynamic Fields */}
        <div className="space-y-6">
          <h3 className="text-lg font-medium border-b pb-2">Form Fields</h3>
          {currentTemplate.fields.map((field) => (
            <div key={field.id}>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {field.label}
                {field.required && <span className="text-red-500 ml-1">*</span>}
              </label>
              {renderField(field)}
              {errors[field.id as keyof FormData] && (
                <p className="mt-1 text-sm text-red-600">
                  {errors[field.id as keyof FormData]?.message}
                </p>
              )}
            </div>
          ))}
        </div>

        {/* Actions */}
        <div className="flex justify-end gap-4 pt-6 border-t">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
          >
            Cancel
          </button>
          
          {canEdit && (
            <button
              type="button"
              onClick={handleSubmit(handleSave)}
              disabled={isSaving || isLoading}
              className="flex items-center gap-2 px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 disabled:opacity-50"
            >
              <Save className="w-4 h-4" />
              {isSaving ? 'Saving...' : 'Save Draft'}
            </button>
          )}

          {canSubmit && (
            <button
              type="button"
              onClick={handleSubmit(handleSubmitForApproval)}
              disabled={isSubmitting || isLoading}
              className="flex items-center gap-2 px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50"
            >
              <Send className="w-4 h-4" />
              {isSubmitting ? 'Submitting...' : 'Submit for Approval'}
            </button>
          )}
        </div>
      </form>
    </div>
  )
}
