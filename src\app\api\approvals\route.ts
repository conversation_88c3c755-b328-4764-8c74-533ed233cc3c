import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// GET /api/approvals - Get pending approvals for current user
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user can approve
    if (!session.user.permissions?.canApproveLevel1 && !session.user.permissions?.canApproveFinal) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const status = searchParams.get('status') || 'PENDING'

    const skip = (page - 1) * limit

    // Build where clause based on user permissions
    let where: any = {
      approverId: session.user.id,
      status
    }

    // If user can approve level 1, show level 1 approvals
    // If user can approve final, show level 2 approvals
    if (session.user.permissions?.canApproveFinal && !session.user.permissions?.canApproveLevel1) {
      where.level = 2
    } else if (session.user.permissions?.canApproveLevel1 && !session.user.permissions?.canApproveFinal) {
      where.level = 1
    }
    // If user has both permissions, show all their approvals

    const [approvals, total] = await Promise.all([
      prisma.approval.findMany({
        where,
        skip,
        take: limit,
        include: {
          checksheet: {
            include: {
              template: {
                select: {
                  id: true,
                  name: true,
                  version: true
                }
              },
              department: true,
              createdBy: {
                select: {
                  id: true,
                  username: true,
                  fullName: true
                }
              },
              folder: {
                select: {
                  id: true,
                  name: true
                }
              }
            }
          },
          approver: {
            select: {
              id: true,
              username: true,
              fullName: true
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.approval.count({ where })
    ])

    return NextResponse.json({
      approvals,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('Error fetching approvals:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
