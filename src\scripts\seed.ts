import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting database seeding...')

  // Create roles
  console.log('Creating roles...')
  const executorRole = await prisma.role.upsert({
    where: { name: 'executor' },
    update: {},
    create: {
      name: 'executor',
      description: 'Người thực hiện checksheet',
      permissions: {
        canCreateChecksheet: true,
        canEditOwnChecksheet: true,
        canViewOwnChecksheet: true,
        canCreateTemplate: true,
        canEditOwnTemplate: true
      }
    }
  })

  const approver1Role = await prisma.role.upsert({
    where: { name: 'approver1' },
    update: {},
    create: {
      name: 'approver1',
      description: 'Người duyệt cấp 1',
      permissions: {
        canCreateChecksheet: true,
        canEditOwnChecksheet: true,
        canViewOwnChecksheet: true,
        canCreateTemplate: true,
        canEditOwnTemplate: true,
        canApproveLevel1: true,
        canViewDepartmentChecksheets: true
      }
    }
  })

  const finalApproverRole = await prisma.role.upsert({
    where: { name: 'final_approver' },
    update: {},
    create: {
      name: 'final_approver',
      description: 'Người duyệt cuối',
      permissions: {
        canCreateChecksheet: true,
        canEditOwnChecksheet: true,
        canViewOwnChecksheet: true,
        canCreateTemplate: true,
        canEditOwnTemplate: true,
        canApproveLevel1: true,
        canApproveFinal: true,
        canViewAllChecksheets: true,
        canCreateUser: true,
        canManageRoles: true,
        canManageDepartments: true
      }
    }
  })

  // Create departments
  console.log('Creating departments...')
  const itDepartment = await prisma.department.upsert({
    where: { name: 'IT Department' },
    update: {},
    create: {
      name: 'IT Department',
      description: 'Information Technology Department'
    }
  })

  const hrDepartment = await prisma.department.upsert({
    where: { name: 'HR Department' },
    update: {},
    create: {
      name: 'HR Department',
      description: 'Human Resources Department'
    }
  })

  const financeDepartment = await prisma.department.upsert({
    where: { name: 'Finance Department' },
    update: {},
    create: {
      name: 'Finance Department',
      description: 'Finance Department'
    }
  })

  const operationsDepartment = await prisma.department.upsert({
    where: { name: 'Operations Department' },
    update: {},
    create: {
      name: 'Operations Department',
      description: 'Operations Department'
    }
  })

  // Create admin user
  console.log('Creating admin user...')
  const hashedPassword = await bcrypt.hash('admin123', 10)
  
  const adminUser = await prisma.user.upsert({
    where: { username: 'admin' },
    update: {},
    create: {
      username: 'admin',
      email: '<EMAIL>',
      password: hashedPassword,
      fullName: 'System Administrator',
      departmentId: itDepartment.id,
      roleId: finalApproverRole.id
    }
  })

  // Create sample users
  console.log('Creating sample users...')
  const users = [
    {
      username: 'john.executor',
      email: '<EMAIL>',
      fullName: 'John Executor',
      departmentId: itDepartment.id,
      roleId: executorRole.id
    },
    {
      username: 'jane.approver',
      email: '<EMAIL>',
      fullName: 'Jane Approver',
      departmentId: itDepartment.id,
      roleId: approver1Role.id
    },
    {
      username: 'bob.hr',
      email: '<EMAIL>',
      fullName: 'Bob HR Manager',
      departmentId: hrDepartment.id,
      roleId: approver1Role.id
    }
  ]

  for (const userData of users) {
    await prisma.user.upsert({
      where: { username: userData.username },
      update: {},
      create: {
        ...userData,
        password: await bcrypt.hash('password123', 10)
      }
    })
  }

  // Create folders
  console.log('Creating folders...')

  // Create template folders for each department
  const templateFolders = [
    {
      name: 'IT Templates',
      description: 'Templates for IT Department',
      departmentId: itDepartment.id,
      createdById: adminUser.id
    },
    {
      name: 'HR Templates',
      description: 'Templates for HR Department',
      departmentId: hrDepartment.id,
      createdById: adminUser.id
    },
    {
      name: 'Finance Templates',
      description: 'Templates for Finance Department',
      departmentId: financeDepartment.id,
      createdById: adminUser.id
    },
    {
      name: 'Operations Templates',
      description: 'Templates for Operations Department',
      departmentId: operationsDepartment.id,
      createdById: adminUser.id
    }
  ]

  // Create status-based folders for each department
  const statusFolders = []
  const departments = [itDepartment, hrDepartment, financeDepartment, operationsDepartment]
  const statuses = [
    { name: 'In Progress', description: 'Checksheets đang thực hiện', status: 'DRAFT' },
    { name: 'Pending Level 1 Approval', description: 'Checksheets chờ duyệt cấp 1', status: 'SUBMITTED' },
    { name: 'Pending Final Approval', description: 'Checksheets chờ duyệt cuối', status: 'APPROVED_LEVEL_1' },
    { name: 'Approved', description: 'Checksheets đã được duyệt', status: 'APPROVED_FINAL' },
    { name: 'Rejected', description: 'Checksheets bị từ chối', status: 'REJECTED' }
  ]

  departments.forEach(dept => {
    statuses.forEach(status => {
      statusFolders.push({
        name: `${dept.name} - ${status.name}`,
        description: `${status.description} - ${dept.name}`,
        departmentId: dept.id,
        createdById: adminUser.id
      })
    })
  })

  const folders = [...templateFolders, ...statusFolders]

  for (const folderData of folders) {
    await prisma.folder.upsert({
      where: { 
        name_departmentId: {
          name: folderData.name,
          departmentId: folderData.departmentId
        }
      },
      update: {},
      create: folderData
    })
  }

  // Create sample templates
  console.log('Creating sample templates...')
  const itFolder = await prisma.folder.findFirst({
    where: { name: 'IT Templates', departmentId: itDepartment.id }
  })

  if (itFolder) {
    await prisma.checksheetTemplate.upsert({
      where: { 
        name_departmentId: {
          name: 'Server Maintenance Checklist',
          departmentId: itDepartment.id
        }
      },
      update: {},
      create: {
        name: 'Server Maintenance Checklist',
        description: 'Monthly server maintenance checklist',
        version: '1.0',
        departmentId: itDepartment.id,
        createdById: adminUser.id,
        folderId: itFolder.id,
        fields: [
          {
            id: 'server_name',
            type: 'text',
            label: 'Server Name',
            required: true,
            placeholder: 'Enter server name'
          },
          {
            id: 'maintenance_date',
            type: 'date',
            label: 'Maintenance Date',
            required: true
          },
          {
            id: 'backup_verified',
            type: 'checkbox',
            label: 'Backup verified',
            required: true
          },
          {
            id: 'disk_space_check',
            type: 'select',
            label: 'Disk Space Status',
            required: true,
            options: ['Good', 'Warning', 'Critical']
          },
          {
            id: 'notes',
            type: 'textarea',
            label: 'Additional Notes',
            required: false,
            placeholder: 'Enter any additional notes'
          }
        ]
      }
    })
  }

  console.log('✅ Database seeding completed!')
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
