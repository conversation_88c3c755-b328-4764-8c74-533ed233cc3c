{"name": "e_checksheet", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:seed": "tsx src/scripts/seed.ts", "db:studio": "prisma studio", "postinstall": "prisma generate"}, "keywords": [], "author": "", "license": "ISC", "description": "E-Checksheet Management System", "dependencies": {"@hookform/resolvers": "^5.1.1", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.11.1", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^24.0.10", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.21", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "eslint": "^9.30.1", "eslint-config-next": "^15.3.5", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.525.0", "mssql": "^11.0.1", "next": "^15.3.5", "next-auth": "^4.24.11", "postcss": "^8.5.6", "prisma": "^6.11.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.60.0", "tailwindcss": "^4.1.11", "typescript": "^5.8.3", "zod": "^3.25.75"}, "devDependencies": {"tsx": "^4.20.3"}}