import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { canApproveChecksheet } from '@/lib/permissions'
import { z } from 'zod'

const approvalActionSchema = z.object({
  action: z.enum(['approve', 'reject']),
  comments: z.string().optional()
})

// PUT /api/approvals/[id] - Approve or reject checksheet
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { action, comments } = approvalActionSchema.parse(body)

    // Get approval record
    const approval = await prisma.approval.findUnique({
      where: { id: params.id },
      include: {
        checksheet: true
      }
    })

    if (!approval) {
      return NextResponse.json({ error: 'Approval not found' }, { status: 404 })
    }

    // Check if user can approve this checksheet
    if (approval.approverId !== session.user.id) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Check if approval is still pending
    if (approval.status !== 'PENDING') {
      return NextResponse.json(
        { error: 'Approval has already been processed' },
        { status: 400 }
      )
    }

    // Check if user has permission for this approval level
    const hasPermission = canApproveChecksheet(
      session.user.permissions,
      approval.level,
      approval.checksheet.status
    )

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Process approval/rejection
    const now = new Date()
    let newChecksheetStatus = approval.checksheet.status

    if (action === 'approve') {
      if (approval.level === 1) {
        newChecksheetStatus = 'APPROVED_LEVEL_1'
      } else if (approval.level === 2) {
        newChecksheetStatus = 'APPROVED_FINAL'
      }
    } else {
      newChecksheetStatus = 'REJECTED'
    }

    // Update approval and checksheet status in transaction
    const result = await prisma.$transaction(async (tx) => {
      // Update approval record
      const updatedApproval = await tx.approval.update({
        where: { id: params.id },
        data: {
          status: action === 'approve' ? 'APPROVED' : 'REJECTED',
          comments,
          approvedAt: now
        }
      })

      // Update checksheet status
      const updatedChecksheet = await tx.checksheet.update({
        where: { id: approval.checksheetId },
        data: { status: newChecksheetStatus }
      })

      // If rejected, update all other pending approvals for this checksheet
      if (action === 'reject') {
        await tx.approval.updateMany({
          where: {
            checksheetId: approval.checksheetId,
            status: 'PENDING'
          },
          data: { status: 'REJECTED' }
        })
      }

      return { approval: updatedApproval, checksheet: updatedChecksheet }
    })

    // Get updated approval with related data
    const finalApproval = await prisma.approval.findUnique({
      where: { id: params.id },
      include: {
        checksheet: {
          include: {
            template: {
              select: {
                id: true,
                name: true,
                version: true
              }
            },
            department: true,
            createdBy: {
              select: {
                id: true,
                username: true,
                fullName: true
              }
            },
            folder: {
              select: {
                id: true,
                name: true
              }
            },
            approvals: {
              include: {
                approver: {
                  select: {
                    id: true,
                    username: true,
                    fullName: true
                  }
                }
              },
              orderBy: { level: 'asc' }
            }
          }
        },
        approver: {
          select: {
            id: true,
            username: true,
            fullName: true
          }
        }
      }
    })

    return NextResponse.json({
      message: `Checksheet ${action}d successfully`,
      approval: finalApproval
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error processing approval:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
