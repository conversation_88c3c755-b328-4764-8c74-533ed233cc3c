import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { FolderManager } from '@/lib/folderManager'
import { NotificationService } from '@/lib/notificationService'

// POST /api/checksheets/[id]/submit - Submit checksheet for approval
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if checksheet exists
    const checksheet = await prisma.checksheet.findUnique({
      where: { id: params.id },
      include: {
        department: true
      }
    })

    if (!checksheet) {
      return NextResponse.json({ error: 'Checksheet not found' }, { status: 404 })
    }

    // Check if user can submit this checksheet
    if (checksheet.createdById !== session.user.id) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Can only submit draft checksheets
    if (checksheet.status !== 'DRAFT') {
      return NextResponse.json(
        { error: 'Can only submit draft checksheets' },
        { status: 400 }
      )
    }

    // Find approvers for this department
    const approvers = await prisma.user.findMany({
      where: {
        departmentId: checksheet.departmentId,
        isActive: true,
        role: {
          name: {
            in: ['approver1', 'final_approver']
          }
        }
      },
      include: {
        role: true
      }
    })

    const approver1 = approvers.find(u => u.role.name === 'approver1')
    const finalApprover = approvers.find(u => u.role.name === 'final_approver')

    if (!approver1 && !finalApprover) {
      return NextResponse.json(
        { error: 'No approvers found for this department' },
        { status: 400 }
      )
    }

    // Create approval records
    const approvalData = []
    
    if (approver1) {
      approvalData.push({
        checksheetId: params.id,
        approverId: approver1.id,
        level: 1,
        status: 'PENDING'
      })
    }

    if (finalApprover) {
      approvalData.push({
        checksheetId: params.id,
        approverId: finalApprover.id,
        level: 2,
        status: 'PENDING'
      })
    }

    // Update checksheet status and create approvals in transaction
    const result = await prisma.$transaction([
      prisma.checksheet.update({
        where: { id: params.id },
        data: {
          status: 'SUBMITTED',
          submittedAt: new Date()
        }
      }),
      prisma.approval.createMany({
        data: approvalData
      })
    ])

    // Auto-organize checksheet into appropriate folder
    await FolderManager.organizeChecksheet(params.id, 'SUBMITTED', checksheet.departmentId)

    // Send notifications
    await NotificationService.notifyChecksheetSubmitted(params.id, session.user.id)

    // Get updated checksheet with approvals
    const updatedChecksheet = await prisma.checksheet.findUnique({
      where: { id: params.id },
      include: {
        template: {
          select: {
            id: true,
            name: true,
            version: true
          }
        },
        department: true,
        createdBy: {
          select: {
            id: true,
            username: true,
            fullName: true
          }
        },
        folder: {
          select: {
            id: true,
            name: true
          }
        },
        approvals: {
          include: {
            approver: {
              select: {
                id: true,
                username: true,
                fullName: true
              }
            }
          },
          orderBy: { level: 'asc' }
        }
      }
    })

    return NextResponse.json({
      message: 'Checksheet submitted successfully',
      checksheet: updatedChecksheet
    })
  } catch (error) {
    console.error('Error submitting checksheet:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
