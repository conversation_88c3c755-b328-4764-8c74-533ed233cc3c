import { withAuth } from 'next-auth/middleware'
import { NextResponse } from 'next/server'

export default withAuth(
  function middleware(req) {
    const token = req.nextauth.token
    const { pathname } = req.nextUrl

    // Allow access to auth pages
    if (pathname.startsWith('/auth/')) {
      return NextResponse.next()
    }

    // Redirect to login if not authenticated
    if (!token) {
      return NextResponse.redirect(new URL('/auth/signin', req.url))
    }

    // Role-based access control
    const userRole = token.role as string
    const permissions = token.permissions as any

    // Admin routes - only final_approver can access
    if (pathname.startsWith('/admin/')) {
      if (userRole !== 'final_approver') {
        return NextResponse.redirect(new URL('/unauthorized', req.url))
      }
    }

    // Approval routes - approver1 and final_approver can access
    if (pathname.startsWith('/approvals/')) {
      if (!permissions?.canApproveLevel1 && !permissions?.canApproveFinal) {
        return NextResponse.redirect(new URL('/unauthorized', req.url))
      }
    }

    return NextResponse.next()
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        const { pathname } = req.nextUrl
        
        // Allow access to public routes
        if (pathname === '/' || pathname.startsWith('/auth/')) {
          return true
        }

        // Require authentication for all other routes
        return !!token
      }
    }
  }
)

export const config = {
  matcher: [
    '/((?!api|_next/static|_next/image|favicon.ico|public).*)'
  ]
}
