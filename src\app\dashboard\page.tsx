'use client'

import { useEffect, useState } from 'react'
import { useSession } from 'next-auth/react'
import DashboardLayout from '@/components/layout/DashboardLayout'
import { 
  FileText, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  Users,
  TrendingUp,
  Calendar
} from 'lucide-react'

interface DashboardStats {
  overview: {
    totalChecksheets: number
    draftChecksheets: number
    submittedChecksheets: number
    approvedLevel1Checksheets: number
    approvedFinalChecksheets: number
    rejectedChecksheets: number
    pendingApprovals: number
    completionRate: number
    approvalRate: number
  }
  charts: {
    checksheetsByStatus: Array<{ status: string; count: number }>
    checksheetsByDepartment: Array<{ departmentId: string; departmentName: string; count: number }>
  }
  recentActivity: Array<{
    id: string
    title: string
    status: string
    createdAt: string
    template: { name: string; version: string }
    department: { name: string }
    createdBy: { fullName: string }
  }>
}

export default function Dashboard() {
  const { data: session } = useSession()
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    fetchStats()
  }, [])

  const fetchStats = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/dashboard/stats')
      if (!response.ok) {
        throw new Error('Failed to fetch stats')
      }
      const data = await response.json()
      setStats(data)
    } catch (error) {
      setError('Failed to load dashboard data')
      console.error('Error fetching stats:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <XCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Error Loading Dashboard</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={fetchStats}
            className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700"
          >
            Try Again
          </button>
        </div>
      </div>
    )
  }

  if (!stats) return null

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'DRAFT':
        return 'text-gray-600'
      case 'SUBMITTED':
        return 'text-yellow-600'
      case 'APPROVED_LEVEL_1':
        return 'text-blue-600'
      case 'APPROVED_FINAL':
        return 'text-green-600'
      case 'REJECTED':
        return 'text-red-600'
      default:
        return 'text-gray-600'
    }
  }

  const formatStatus = (status: string) => {
    return status.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())
  }

  return (
    <DashboardLayout>
      <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600">
            Welcome back, {session?.user?.fullName}
          </p>
        </div>

        {/* Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <FileText className="w-6 h-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Checksheets</p>
                <p className="text-2xl font-bold text-gray-900">{stats.overview.totalChecksheets}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <Clock className="w-6 h-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Pending Approvals</p>
                <p className="text-2xl font-bold text-gray-900">{stats.overview.pendingApprovals}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <CheckCircle className="w-6 h-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Completion Rate</p>
                <p className="text-2xl font-bold text-gray-900">{stats.overview.completionRate}%</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <TrendingUp className="w-6 h-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Approval Rate</p>
                <p className="text-2xl font-bold text-gray-900">{stats.overview.approvalRate}%</p>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Status Distribution */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Checksheets by Status</h3>
            <div className="space-y-3">
              {stats.charts.checksheetsByStatus.map((item) => (
                <div key={item.status} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className={`w-3 h-3 rounded-full mr-3 ${
                      item.status === 'DRAFT' ? 'bg-gray-400' :
                      item.status === 'SUBMITTED' ? 'bg-yellow-400' :
                      item.status === 'APPROVED_LEVEL_1' ? 'bg-blue-400' :
                      item.status === 'APPROVED_FINAL' ? 'bg-green-400' :
                      'bg-red-400'
                    }`}></div>
                    <span className="text-sm text-gray-600">{formatStatus(item.status)}</span>
                  </div>
                  <span className="text-sm font-medium text-gray-900">{item.count}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Department Distribution */}
          {stats.charts.checksheetsByDepartment.length > 0 && (
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Checksheets by Department</h3>
              <div className="space-y-3">
                {stats.charts.checksheetsByDepartment.map((item) => (
                  <div key={item.departmentId} className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Users className="w-4 h-4 text-gray-400 mr-3" />
                      <span className="text-sm text-gray-600">{item.departmentName}</span>
                    </div>
                    <span className="text-sm font-medium text-gray-900">{item.count}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Recent Activity */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Recent Activity</h3>
          </div>
          <div className="divide-y divide-gray-200">
            {stats.recentActivity.map((checksheet) => (
              <div key={checksheet.id} className="px-6 py-4 hover:bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center">
                      <FileText className="w-5 h-5 text-gray-400 mr-3" />
                      <div>
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {checksheet.title}
                        </p>
                        <p className="text-sm text-gray-500">
                          {checksheet.template.name} (v{checksheet.template.version}) • {checksheet.department.name}
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-4">
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                      checksheet.status === 'DRAFT' ? 'bg-gray-100 text-gray-800' :
                      checksheet.status === 'SUBMITTED' ? 'bg-yellow-100 text-yellow-800' :
                      checksheet.status === 'APPROVED_LEVEL_1' ? 'bg-blue-100 text-blue-800' :
                      checksheet.status === 'APPROVED_FINAL' ? 'bg-green-100 text-green-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {formatStatus(checksheet.status)}
                    </span>
                    <div className="text-right">
                      <p className="text-sm text-gray-500">{checksheet.createdBy.fullName}</p>
                      <p className="text-xs text-gray-400">
                        {new Date(checksheet.createdAt).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
