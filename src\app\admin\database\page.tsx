'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import DashboardLayout from '@/components/layout/DashboardLayout'
import { But<PERSON> } from '@/components/ui/Button'
import <PERSON><PERSON> from '@/components/ui/Alert'
import { Database, TestTube, Save, RefreshCw } from 'lucide-react'

const databaseConfigSchema = z.object({
  host: z.string().min(1, 'Host is required'),
  port: z.number().min(1).max(65535),
  database: z.string().min(1, 'Database name is required'),
  username: z.string().min(1, 'Username is required'),
  password: z.string().min(1, 'Password is required'),
  trustServerCertificate: z.boolean(),
  encrypt: z.boolean(),
  connectionTimeout: z.number().min(1000).max(60000)
})

type DatabaseConfig = z.infer<typeof databaseConfigSchema>

interface TestResult {
  success: boolean
  message: string
  serverInfo?: any
  errorCode?: string
  details?: any
}

export default function DatabaseConfigPage() {
  const [loading, setLoading] = useState(false)
  const [testing, setTesting] = useState(false)
  const [alert, setAlert] = useState<{ type: 'success' | 'error' | 'info'; message: string } | null>(null)
  const [testResult, setTestResult] = useState<TestResult | null>(null)

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    getValues
  } = useForm<DatabaseConfig>({
    resolver: zodResolver(databaseConfigSchema),
    defaultValues: {
      host: 'localhost',
      port: 1433,
      database: 'e_checksheet',
      username: 'sa',
      password: '',
      trustServerCertificate: true,
      encrypt: false,
      connectionTimeout: 30000
    }
  })

  useEffect(() => {
    loadCurrentConfig()
  }, [])

  const loadCurrentConfig = async () => {
    try {
      const response = await fetch('/api/admin/database/config')
      if (response.ok) {
        const config = await response.json()
        reset({
          ...config,
          password: '' // Don't populate password field
        })
      }
    } catch (error) {
      console.error('Error loading config:', error)
    }
  }

  const testConnection = async () => {
    setTesting(true)
    setTestResult(null)
    setAlert(null)

    try {
      const formData = getValues()
      const response = await fetch('/api/admin/database/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      })

      const result = await response.json()
      setTestResult(result)

      if (result.success) {
        setAlert({
          type: 'success',
          message: 'Database connection test successful!'
        })
      } else {
        setAlert({
          type: 'error',
          message: result.message || 'Connection test failed'
        })
      }
    } catch (error) {
      setAlert({
        type: 'error',
        message: 'Failed to test connection'
      })
    } finally {
      setTesting(false)
    }
  }

  const saveConfig = async (data: DatabaseConfig) => {
    setLoading(true)
    setAlert(null)

    try {
      const response = await fetch('/api/admin/database/config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      })

      if (response.ok) {
        const result = await response.json()
        setAlert({
          type: 'success',
          message: result.message
        })
      } else {
        const error = await response.json()
        setAlert({
          type: 'error',
          message: error.error || 'Failed to save configuration'
        })
      }
    } catch (error) {
      setAlert({
        type: 'error',
        message: 'Failed to save configuration'
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <DashboardLayout>
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-lg shadow-lg p-6">
            <div className="flex items-center mb-6">
              <Database className="w-8 h-8 text-primary-600 mr-3" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Database Configuration</h1>
                <p className="text-gray-600">Configure SQL Server connection settings</p>
              </div>
            </div>

            {alert && (
              <div className="mb-6">
                <Alert
                  type={alert.type}
                  message={alert.message}
                  onClose={() => setAlert(null)}
                />
              </div>
            )}

            <form onSubmit={handleSubmit(saveConfig)} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Server Host *
                  </label>
                  <input
                    {...register('host')}
                    type="text"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder="localhost or IP address"
                  />
                  {errors.host && (
                    <p className="mt-1 text-sm text-red-600">{errors.host.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Port *
                  </label>
                  <input
                    {...register('port', { valueAsNumber: true })}
                    type="number"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder="1433"
                  />
                  {errors.port && (
                    <p className="mt-1 text-sm text-red-600">{errors.port.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Database Name *
                  </label>
                  <input
                    {...register('database')}
                    type="text"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder="e_checksheet"
                  />
                  {errors.database && (
                    <p className="mt-1 text-sm text-red-600">{errors.database.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Username *
                  </label>
                  <input
                    {...register('username')}
                    type="text"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder="sa"
                  />
                  {errors.username && (
                    <p className="mt-1 text-sm text-red-600">{errors.username.message}</p>
                  )}
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Password *
                  </label>
                  <input
                    {...register('password')}
                    type="password"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder="Enter database password"
                  />
                  {errors.password && (
                    <p className="mt-1 text-sm text-red-600">{errors.password.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Connection Timeout (ms)
                  </label>
                  <input
                    {...register('connectionTimeout', { valueAsNumber: true })}
                    type="number"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder="30000"
                  />
                  {errors.connectionTimeout && (
                    <p className="mt-1 text-sm text-red-600">{errors.connectionTimeout.message}</p>
                  )}
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">Security Options</h3>
                
                <div className="flex items-center">
                  <input
                    {...register('trustServerCertificate')}
                    type="checkbox"
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <label className="ml-2 block text-sm text-gray-900">
                    Trust Server Certificate
                  </label>
                </div>

                <div className="flex items-center">
                  <input
                    {...register('encrypt')}
                    type="checkbox"
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <label className="ml-2 block text-sm text-gray-900">
                    Encrypt Connection
                  </label>
                </div>
              </div>

              {/* Test Result */}
              {testResult && (
                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="text-lg font-medium text-gray-900 mb-3">Connection Test Result</h3>
                  
                  {testResult.success ? (
                    <div className="space-y-2">
                      <p className="text-green-600 font-medium">✅ {testResult.message}</p>
                      {testResult.serverInfo && (
                        <div className="text-sm text-gray-600">
                          <p><strong>Server:</strong> {testResult.serverInfo.serverName}</p>
                          <p><strong>Database:</strong> {testResult.serverInfo.currentDatabase}</p>
                          <p><strong>User:</strong> {testResult.serverInfo.currentUser}</p>
                          <p><strong>Time:</strong> {new Date(testResult.serverInfo.currentTime).toLocaleString()}</p>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="space-y-2">
                      <p className="text-red-600 font-medium">❌ {testResult.message}</p>
                      {testResult.errorCode && (
                        <p className="text-sm text-gray-600">Error Code: {testResult.errorCode}</p>
                      )}
                    </div>
                  )}
                </div>
              )}

              <div className="flex justify-end gap-4 pt-6 border-t">
                <Button
                  type="button"
                  variant="outline"
                  onClick={testConnection}
                  loading={testing}
                  className="flex items-center gap-2"
                >
                  <TestTube className="w-4 h-4" />
                  Test Connection
                </Button>
                
                <Button
                  type="submit"
                  loading={loading}
                  className="flex items-center gap-2"
                >
                  <Save className="w-4 h-4" />
                  Save Configuration
                </Button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
