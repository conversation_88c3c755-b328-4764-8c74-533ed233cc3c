import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import sql from 'mssql'
import { z } from 'zod'

const testConnectionSchema = z.object({
  host: z.string().min(1, 'Host is required'),
  port: z.number().min(1).max(65535),
  database: z.string().min(1, 'Database name is required'),
  username: z.string().min(1, 'Username is required'),
  password: z.string().min(1, 'Password is required'),
  trustServerCertificate: z.boolean().default(true),
  encrypt: z.boolean().default(false),
  connectionTimeout: z.number().min(1000).max(60000).default(30000)
})

// POST /api/admin/database/test - Test database connection
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is final approver (admin)
    if (!session.user.permissions?.canManageDepartments) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const body = await request.json()
    const validatedData = testConnectionSchema.parse(body)

    // Create SQL Server configuration
    const config: sql.config = {
      server: validatedData.host,
      port: validatedData.port,
      database: validatedData.database,
      user: validatedData.username,
      password: validatedData.password,
      options: {
        encrypt: validatedData.encrypt,
        trustServerCertificate: validatedData.trustServerCertificate
      },
      connectionTimeout: validatedData.connectionTimeout,
      requestTimeout: 30000
    }

    let pool: sql.ConnectionPool | null = null
    
    try {
      // Test connection
      pool = new sql.ConnectionPool(config)
      await pool.connect()

      // Test basic query
      const result = await pool.request().query('SELECT 1 as test')
      
      // Get server info
      const serverInfo = await pool.request().query(`
        SELECT 
          @@VERSION as version,
          @@SERVERNAME as serverName,
          DB_NAME() as currentDatabase,
          SYSTEM_USER as currentUser,
          GETDATE() as currentTime
      `)

      await pool.close()

      return NextResponse.json({
        success: true,
        message: 'Database connection successful',
        serverInfo: serverInfo.recordset[0],
        testResult: result.recordset[0]
      })
    } catch (connectionError: any) {
      if (pool) {
        try {
          await pool.close()
        } catch (closeError) {
          console.error('Error closing pool:', closeError)
        }
      }

      let errorMessage = 'Unknown database error'
      let errorCode = 'UNKNOWN_ERROR'

      if (connectionError.code) {
        switch (connectionError.code) {
          case 'ELOGIN':
            errorMessage = 'Login failed. Please check username and password.'
            errorCode = 'LOGIN_FAILED'
            break
          case 'ECONNREFUSED':
            errorMessage = 'Connection refused. Please check host and port.'
            errorCode = 'CONNECTION_REFUSED'
            break
          case 'ETIMEOUT':
            errorMessage = 'Connection timeout. Please check network connectivity.'
            errorCode = 'CONNECTION_TIMEOUT'
            break
          case 'ENOTFOUND':
            errorMessage = 'Server not found. Please check the host address.'
            errorCode = 'SERVER_NOT_FOUND'
            break
          case 'EINSTLOOKUP':
            errorMessage = 'Instance lookup failed. Please check server name.'
            errorCode = 'INSTANCE_LOOKUP_FAILED'
            break
          default:
            errorMessage = connectionError.message || 'Database connection failed'
            errorCode = connectionError.code
        }
      } else {
        errorMessage = connectionError.message || 'Database connection failed'
      }

      return NextResponse.json({
        success: false,
        message: errorMessage,
        errorCode,
        details: {
          originalError: connectionError.message,
          code: connectionError.code,
          number: connectionError.number
        }
      }, { status: 400 })
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error testing database connection:', error)
    return NextResponse.json({ 
      success: false,
      message: 'Internal server error',
      error: 'Failed to test database connection'
    }, { status: 500 })
  }
}
