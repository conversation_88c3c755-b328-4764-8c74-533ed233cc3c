import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const createRoleSchema = z.object({
  name: z.string().min(1, 'Role name is required'),
  description: z.string().optional(),
  permissions: z.object({
    canCreateChecksheet: z.boolean().optional(),
    canEditOwnChecksheet: z.boolean().optional(),
    canViewOwnChecksheet: z.boolean().optional(),
    canCreateTemplate: z.boolean().optional(),
    canEditOwnTemplate: z.boolean().optional(),
    canApproveLevel1: z.boolean().optional(),
    canApproveFinal: z.boolean().optional(),
    canViewDepartmentChecksheets: z.boolean().optional(),
    canViewAllChecksheets: z.boolean().optional(),
    canCreateUser: z.boolean().optional(),
    canManageRoles: z.boolean().optional(),
    canManageDepartments: z.boolean().optional()
  })
})

// GET /api/roles - Get all roles
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const roles = await prisma.role.findMany({
      include: {
        _count: {
          select: {
            users: true
          }
        }
      },
      orderBy: { name: 'asc' }
    })

    return NextResponse.json(roles)
  } catch (error) {
    console.error('Error fetching roles:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// POST /api/roles - Create new role (only final_approver)
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has permission to manage roles
    if (!session.user.permissions?.canManageRoles) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const body = await request.json()
    const validatedData = createRoleSchema.parse(body)

    // Check if role name already exists
    const existingRole = await prisma.role.findUnique({
      where: { name: validatedData.name }
    })

    if (existingRole) {
      return NextResponse.json(
        { error: 'Role name already exists' },
        { status: 400 }
      )
    }

    // Create role
    const role = await prisma.role.create({
      data: validatedData,
      include: {
        _count: {
          select: {
            users: true
          }
        }
      }
    })

    return NextResponse.json(role, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error creating role:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
