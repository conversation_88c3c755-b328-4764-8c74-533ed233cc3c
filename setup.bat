@echo off
echo ========================================
echo E-Checksheet Management System Setup
echo ========================================

echo.
echo 1. Installing dependencies...
call npm install

echo.
echo 2. Installing additional dependencies...
call npm install mssql @types/mssql xlsx react-data-grid @types/xlsx class-variance-authority clsx nodemailer @types/nodemailer tsx --save-dev

echo.
echo 3. Generating Prisma client...
call npm run db:generate

echo.
echo 4. Setting up database...
call npm run db:push

echo.
echo 5. Seeding initial data...
call npm run db:seed

echo.
echo ========================================
echo Setup completed successfully!
echo ========================================
echo.
echo You can now start the application with:
echo npm run dev
echo.
echo Default admin login:
echo Username: admin
echo Password: admin123
echo.
echo Application will be available at:
echo http://localhost:3000
echo ========================================

pause
