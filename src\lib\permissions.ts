import { UserPermissions } from '@/types/auth'

export const checkPermission = (
  permissions: UserPermissions,
  requiredPermission: keyof UserPermissions
): boolean => {
  return permissions[requiredPermission] === true
}

export const hasAnyPermission = (
  permissions: UserPermissions,
  requiredPermissions: (keyof UserPermissions)[]
): boolean => {
  return requiredPermissions.some(permission => 
    permissions[permission] === true
  )
}

export const hasAllPermissions = (
  permissions: UserPermissions,
  requiredPermissions: (keyof UserPermissions)[]
): boolean => {
  return requiredPermissions.every(permission => 
    permissions[permission] === true
  )
}

export const canAccessChecksheet = (
  permissions: UserPermissions,
  checksheetOwnerId: string,
  currentUserId: string,
  checksheetDepartmentId: string,
  currentUserDepartmentId: string
): boolean => {
  // Can view all checksheets (final_approver)
  if (permissions.canViewAllChecksheets) {
    return true
  }

  // Can view department checksheets (approver1)
  if (permissions.canViewDepartmentChecksheets && 
      checksheetDepartmentId === currentUserDepartmentId) {
    return true
  }

  // Can view own checksheets
  if (permissions.canViewOwnChecksheet && checksheetOwnerId === currentUserId) {
    return true
  }

  return false
}

export const canEditChecksheet = (
  permissions: UserPermissions,
  checksheetOwnerId: string,
  currentUserId: string,
  checksheetStatus: string
): boolean => {
  // Can only edit own checksheets
  if (!permissions.canEditOwnChecksheet || checksheetOwnerId !== currentUserId) {
    return false
  }

  // Can only edit draft checksheets
  return checksheetStatus === 'DRAFT'
}

export const canApproveChecksheet = (
  permissions: UserPermissions,
  approvalLevel: number,
  checksheetStatus: string
): boolean => {
  if (approvalLevel === 1) {
    return permissions.canApproveLevel1 === true && 
           checksheetStatus === 'SUBMITTED'
  }

  if (approvalLevel === 2) {
    return permissions.canApproveFinal === true && 
           checksheetStatus === 'APPROVED_LEVEL_1'
  }

  return false
}

export const getDefaultPermissions = (roleName: string): UserPermissions => {
  switch (roleName) {
    case 'executor':
      return {
        canCreateChecksheet: true,
        canEditOwnChecksheet: true,
        canViewOwnChecksheet: true,
        canCreateTemplate: true,
        canEditOwnTemplate: true
      }
    
    case 'approver1':
      return {
        canCreateChecksheet: true,
        canEditOwnChecksheet: true,
        canViewOwnChecksheet: true,
        canCreateTemplate: true,
        canEditOwnTemplate: true,
        canApproveLevel1: true,
        canViewDepartmentChecksheets: true
      }
    
    case 'final_approver':
      return {
        canCreateChecksheet: true,
        canEditOwnChecksheet: true,
        canViewOwnChecksheet: true,
        canCreateTemplate: true,
        canEditOwnTemplate: true,
        canApproveLevel1: true,
        canApproveFinal: true,
        canViewAllChecksheets: true,
        canCreateUser: true,
        canManageRoles: true,
        canManageDepartments: true
      }
    
    default:
      return {}
  }
}
