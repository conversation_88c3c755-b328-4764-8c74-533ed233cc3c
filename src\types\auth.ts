import { DefaultSession } from 'next-auth'

declare module 'next-auth' {
  interface Session {
    user: {
      id: string
      username: string
      fullName: string
      role: string
      roleId: string
      department: string
      departmentId: string
      permissions: {
        canCreateChecksheet?: boolean
        canEditOwnChecksheet?: boolean
        canViewOwnChecksheet?: boolean
        canCreateTemplate?: boolean
        canEditOwnTemplate?: boolean
        canApproveLevel1?: boolean
        canApproveFinal?: boolean
        canViewDepartmentChecksheets?: boolean
        canViewAllChecksheets?: boolean
        canCreateUser?: boolean
        canManageRoles?: boolean
        canManageDepartments?: boolean
      }
    } & DefaultSession['user']
  }

  interface User {
    id: string
    username: string
    fullName: string
    role: string
    roleId: string
    department: string
    departmentId: string
    permissions: any
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    id: string
    username: string
    fullName: string
    role: string
    roleId: string
    department: string
    departmentId: string
    permissions: any
  }
}

export interface UserPermissions {
  canCreateChecksheet?: boolean
  canEditOwnChecksheet?: boolean
  canViewOwnChecksheet?: boolean
  canCreateTemplate?: boolean
  canEditOwnTemplate?: boolean
  canApproveLevel1?: boolean
  canApproveFinal?: boolean
  canViewDepartmentChecksheets?: boolean
  canViewAllChecksheets?: boolean
  canCreateUser?: boolean
  canManageRoles?: boolean
  canManageDepartments?: boolean
}

export interface AuthUser {
  id: string
  username: string
  email: string
  fullName: string
  role: string
  roleId: string
  department: string
  departmentId: string
  permissions: UserPermissions
}
