import { prisma } from './prisma'
import nodemailer from 'nodemailer'

export interface NotificationData {
  userId: string
  title: string
  message: string
  type: 'INFO' | 'SUCCESS' | 'WARNING' | 'ERROR' | 'APPROVAL_REQUEST' | 'APPROVAL_APPROVED' | 'APPROVAL_REJECTED' | 'CHECKSHEET_SUBMITTED' | 'CHECKSHEET_UPDATED' | 'TEMPLATE_CREATED' | 'USER_CREATED'
  checksheetId?: string
  templateId?: string
  metadata?: any
}

export interface EmailData {
  to: string
  subject: string
  html: string
  text?: string
}

export class NotificationService {
  private static emailTransporter = nodemailer.createTransporter({
    host: process.env.SMTP_HOST || 'localhost',
    port: parseInt(process.env.SMTP_PORT || '587'),
    secure: process.env.SMTP_SECURE === 'true',
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS
    }
  })

  // Create in-app notification
  static async createNotification(data: NotificationData) {
    try {
      const notification = await prisma.notification.create({
        data: {
          userId: data.userId,
          title: data.title,
          message: data.message,
          type: data.type,
          checksheetId: data.checksheetId,
          templateId: data.templateId,
          metadata: data.metadata
        },
        include: {
          user: {
            select: {
              id: true,
              username: true,
              fullName: true,
              email: true
            }
          },
          checksheet: {
            select: {
              id: true,
              title: true,
              status: true
            }
          },
          template: {
            select: {
              id: true,
              name: true,
              version: true
            }
          }
        }
      })

      console.log(`Notification created for user ${data.userId}: ${data.title}`)
      return notification
    } catch (error) {
      console.error('Error creating notification:', error)
      throw error
    }
  }

  // Send email notification
  static async sendEmail(data: EmailData) {
    try {
      if (!process.env.SMTP_HOST || !process.env.SMTP_USER) {
        console.log('Email not configured, skipping email send')
        return null
      }

      const info = await this.emailTransporter.sendMail({
        from: process.env.SMTP_FROM || process.env.SMTP_USER,
        to: data.to,
        subject: data.subject,
        html: data.html,
        text: data.text
      })

      console.log(`Email sent to ${data.to}: ${data.subject}`)
      return info
    } catch (error) {
      console.error('Error sending email:', error)
      throw error
    }
  }

  // Send both in-app and email notification
  static async sendNotification(notificationData: NotificationData, emailData?: EmailData) {
    try {
      // Create in-app notification
      const notification = await this.createNotification(notificationData)

      // Send email if email data provided
      if (emailData) {
        await this.sendEmail(emailData)
      }

      return notification
    } catch (error) {
      console.error('Error sending notification:', error)
      throw error
    }
  }

  // Notify checksheet submission
  static async notifyChecksheetSubmitted(checksheetId: string, submitterId: string) {
    try {
      const checksheet = await prisma.checksheet.findUnique({
        where: { id: checksheetId },
        include: {
          createdBy: true,
          department: true,
          template: true
        }
      })

      if (!checksheet) return

      // Find approvers for this department
      const approvers = await prisma.user.findMany({
        where: {
          departmentId: checksheet.departmentId,
          isActive: true,
          role: {
            name: {
              in: ['approver1', 'final_approver']
            }
          }
        }
      })

      // Notify each approver
      for (const approver of approvers) {
        await this.sendNotification(
          {
            userId: approver.id,
            title: 'New Checksheet Submitted',
            message: `${checksheet.createdBy.fullName} has submitted checksheet "${checksheet.title}" for approval.`,
            type: 'APPROVAL_REQUEST',
            checksheetId: checksheet.id,
            metadata: {
              submitterName: checksheet.createdBy.fullName,
              checksheetTitle: checksheet.title,
              templateName: checksheet.template.name
            }
          },
          {
            to: approver.email,
            subject: `New Checksheet Submitted: ${checksheet.title}`,
            html: this.generateChecksheetSubmittedEmail(checksheet, approver)
          }
        )
      }
    } catch (error) {
      console.error('Error notifying checksheet submission:', error)
    }
  }

  // Notify approval decision
  static async notifyApprovalDecision(approvalId: string, decision: 'APPROVED' | 'REJECTED', comments?: string) {
    try {
      const approval = await prisma.approval.findUnique({
        where: { id: approvalId },
        include: {
          checksheet: {
            include: {
              createdBy: true,
              template: true
            }
          },
          approver: true
        }
      })

      if (!approval) return

      const checksheet = approval.checksheet
      const isApproved = decision === 'APPROVED'

      // Notify checksheet creator
      await this.sendNotification(
        {
          userId: checksheet.createdById,
          title: `Checksheet ${isApproved ? 'Approved' : 'Rejected'}`,
          message: `Your checksheet "${checksheet.title}" has been ${isApproved ? 'approved' : 'rejected'} by ${approval.approver.fullName}.${comments ? ` Comments: ${comments}` : ''}`,
          type: isApproved ? 'APPROVAL_APPROVED' : 'APPROVAL_REJECTED',
          checksheetId: checksheet.id,
          metadata: {
            approverName: approval.approver.fullName,
            decision,
            comments,
            level: approval.level
          }
        },
        {
          to: checksheet.createdBy.email,
          subject: `Checksheet ${isApproved ? 'Approved' : 'Rejected'}: ${checksheet.title}`,
          html: this.generateApprovalDecisionEmail(checksheet, approval.approver, decision, comments)
        }
      )

      // If approved at level 1, notify final approvers
      if (isApproved && approval.level === 1) {
        const finalApprovers = await prisma.user.findMany({
          where: {
            departmentId: checksheet.departmentId,
            isActive: true,
            role: {
              name: 'final_approver'
            }
          }
        })

        for (const finalApprover of finalApprovers) {
          await this.sendNotification(
            {
              userId: finalApprover.id,
              title: 'Checksheet Ready for Final Approval',
              message: `Checksheet "${checksheet.title}" has been approved at level 1 and is ready for your final approval.`,
              type: 'APPROVAL_REQUEST',
              checksheetId: checksheet.id,
              metadata: {
                submitterName: checksheet.createdBy.fullName,
                checksheetTitle: checksheet.title,
                templateName: checksheet.template.name,
                level: 2
              }
            },
            {
              to: finalApprover.email,
              subject: `Final Approval Required: ${checksheet.title}`,
              html: this.generateFinalApprovalEmail(checksheet, finalApprover)
            }
          )
        }
      }
    } catch (error) {
      console.error('Error notifying approval decision:', error)
    }
  }

  // Generate email templates
  private static generateChecksheetSubmittedEmail(checksheet: any, approver: any): string {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2563eb;">New Checksheet Submitted</h2>
        <p>Dear ${approver.fullName},</p>
        <p>A new checksheet has been submitted for your approval:</p>
        <div style="background: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin: 0 0 10px 0;">${checksheet.title}</h3>
          <p><strong>Template:</strong> ${checksheet.template.name}</p>
          <p><strong>Submitted by:</strong> ${checksheet.createdBy.fullName}</p>
          <p><strong>Department:</strong> ${checksheet.department.name}</p>
          <p><strong>Submitted at:</strong> ${new Date(checksheet.submittedAt).toLocaleString()}</p>
        </div>
        <p>Please log in to the E-Checksheet system to review and approve this checksheet.</p>
        <a href="${process.env.NEXTAUTH_URL}/checksheets/${checksheet.id}" 
           style="background: #2563eb; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block;">
          Review Checksheet
        </a>
      </div>
    `
  }

  private static generateApprovalDecisionEmail(checksheet: any, approver: any, decision: string, comments?: string): string {
    const isApproved = decision === 'APPROVED'
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: ${isApproved ? '#059669' : '#dc2626'};">Checksheet ${isApproved ? 'Approved' : 'Rejected'}</h2>
        <p>Dear ${checksheet.createdBy.fullName},</p>
        <p>Your checksheet has been ${isApproved ? 'approved' : 'rejected'}:</p>
        <div style="background: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin: 0 0 10px 0;">${checksheet.title}</h3>
          <p><strong>Reviewed by:</strong> ${approver.fullName}</p>
          <p><strong>Decision:</strong> <span style="color: ${isApproved ? '#059669' : '#dc2626'}; font-weight: bold;">${decision}</span></p>
          ${comments ? `<p><strong>Comments:</strong> ${comments}</p>` : ''}
        </div>
        <a href="${process.env.NEXTAUTH_URL}/checksheets/${checksheet.id}" 
           style="background: #2563eb; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block;">
          View Checksheet
        </a>
      </div>
    `
  }

  private static generateFinalApprovalEmail(checksheet: any, approver: any): string {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2563eb;">Final Approval Required</h2>
        <p>Dear ${approver.fullName},</p>
        <p>A checksheet has been approved at level 1 and requires your final approval:</p>
        <div style="background: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin: 0 0 10px 0;">${checksheet.title}</h3>
          <p><strong>Template:</strong> ${checksheet.template.name}</p>
          <p><strong>Submitted by:</strong> ${checksheet.createdBy.fullName}</p>
          <p><strong>Department:</strong> ${checksheet.department.name}</p>
        </div>
        <p>Please log in to the E-Checksheet system to provide your final approval.</p>
        <a href="${process.env.NEXTAUTH_URL}/checksheets/${checksheet.id}" 
           style="background: #2563eb; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block;">
          Review for Final Approval
        </a>
      </div>
    `
  }

  // Mark notification as read
  static async markAsRead(notificationId: string, userId: string) {
    try {
      return await prisma.notification.update({
        where: {
          id: notificationId,
          userId: userId // Ensure user can only mark their own notifications
        },
        data: {
          isRead: true
        }
      })
    } catch (error) {
      console.error('Error marking notification as read:', error)
      throw error
    }
  }

  // Get user notifications
  static async getUserNotifications(userId: string, page = 1, limit = 20, unreadOnly = false) {
    try {
      const skip = (page - 1) * limit
      const where: any = { userId }
      
      if (unreadOnly) {
        where.isRead = false
      }

      const [notifications, total] = await Promise.all([
        prisma.notification.findMany({
          where,
          skip,
          take: limit,
          include: {
            checksheet: {
              select: {
                id: true,
                title: true,
                status: true
              }
            },
            template: {
              select: {
                id: true,
                name: true,
                version: true
              }
            }
          },
          orderBy: { createdAt: 'desc' }
        }),
        prisma.notification.count({ where })
      ])

      return {
        notifications,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    } catch (error) {
      console.error('Error getting user notifications:', error)
      throw error
    }
  }
}
