'use client'

import { useState } from 'react'
import DashboardLayout from '@/components/layout/DashboardLayout'
import ExcelEditor from '@/components/excel/ExcelEditor'
import { Button } from '@/components/ui/Button'
import Alert from '@/components/ui/Alert'
import Modal from '@/components/ui/Modal'
import { FileSpreadsheet, Save, Eye } from 'lucide-react'

interface ExcelTemplate {
  id?: string
  name: string
  description?: string
  data: any[][]
  createdAt?: string
  updatedAt?: string
}

export default function ExcelTemplatePage() {
  const [currentTemplate, setCurrentTemplate] = useState<ExcelTemplate>({
    name: 'New Excel Template',
    description: '',
    data: []
  })
  const [isPreviewMode, setIsPreviewMode] = useState(false)
  const [isSaveModalOpen, setIsSaveModalOpen] = useState(false)
  const [alert, setAlert] = useState<{ type: 'success' | 'error'; message: string } | null>(null)
  const [saving, setSaving] = useState(false)

  const handleSaveTemplate = async (data: any[][]) => {
    setCurrentTemplate(prev => ({ ...prev, data }))
    setIsSaveModalOpen(true)
  }

  const handleExport = (data: any[][]) => {
    setAlert({
      type: 'success',
      message: 'Excel file exported successfully!'
    })
  }

  const confirmSave = async () => {
    setSaving(true)
    try {
      // Here you would typically save to your API
      const templateData = {
        ...currentTemplate,
        fields: convertExcelDataToFields(currentTemplate.data)
      }

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      setAlert({
        type: 'success',
        message: 'Excel template saved successfully!'
      })
      setIsSaveModalOpen(false)
    } catch (error) {
      setAlert({
        type: 'error',
        message: 'Failed to save template'
      })
    } finally {
      setSaving(false)
    }
  }

  const convertExcelDataToFields = (data: any[][]): any[] => {
    // Convert Excel data to checksheet template fields
    const fields: any[] = []
    
    data.forEach((row, rowIndex) => {
      row.forEach((cell, colIndex) => {
        if (cell.value && cell.value.toString().trim()) {
          fields.push({
            id: `excel_${rowIndex}_${colIndex}`,
            type: 'text',
            label: cell.value.toString(),
            required: false,
            row: rowIndex,
            col: colIndex,
            style: cell.style
          })
        }
      })
    })

    return fields
  }

  return (
    <DashboardLayout>
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <FileSpreadsheet className="w-8 h-8 text-primary-600 mr-3" />
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">Excel Template Builder</h1>
                  <p className="text-gray-600">Create checksheet templates using Excel-like interface</p>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <Button
                  variant="outline"
                  onClick={() => setIsPreviewMode(!isPreviewMode)}
                  className="flex items-center gap-2"
                >
                  <Eye className="w-4 h-4" />
                  {isPreviewMode ? 'Edit Mode' : 'Preview Mode'}
                </Button>
              </div>
            </div>
          </div>

          {/* Alert */}
          {alert && (
            <div className="mb-6">
              <Alert
                type={alert.type}
                message={alert.message}
                onClose={() => setAlert(null)}
              />
            </div>
          )}

          {/* Template Info */}
          <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Template Information</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Template Name *
                </label>
                <input
                  type="text"
                  value={currentTemplate.name}
                  onChange={(e) => setCurrentTemplate(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  placeholder="Enter template name"
                  disabled={isPreviewMode}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <input
                  type="text"
                  value={currentTemplate.description}
                  onChange={(e) => setCurrentTemplate(prev => ({ ...prev, description: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  placeholder="Enter template description"
                  disabled={isPreviewMode}
                />
              </div>
            </div>
          </div>

          {/* Excel Editor */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-medium text-gray-900">Excel Template Editor</h2>
              <div className="text-sm text-gray-600">
                {isPreviewMode ? 'Preview Mode - Read Only' : 'Edit Mode'}
              </div>
            </div>
            
            <ExcelEditor
              initialData={currentTemplate.data}
              onSave={handleSaveTemplate}
              onExport={handleExport}
              readOnly={isPreviewMode}
            />
          </div>

          {/* Instructions */}
          <div className="bg-blue-50 rounded-lg p-6 mt-6">
            <h3 className="text-lg font-medium text-blue-900 mb-3">How to use Excel Template Builder</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-800">
              <div>
                <h4 className="font-medium mb-2">Basic Features:</h4>
                <ul className="space-y-1">
                  <li>• Click on cells to select and edit</li>
                  <li>• Use Shift+Click to select ranges</li>
                  <li>• Enter formulas starting with = (e.g., =SUM(A1:A5))</li>
                  <li>• Use formatting buttons for styling</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium mb-2">Advanced Features:</h4>
                <ul className="space-y-1">
                  <li>• Import existing Excel files</li>
                  <li>• Export to Excel format</li>
                  <li>• Add/remove rows and columns</li>
                  <li>• Apply text formatting and alignment</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Save Modal */}
      <Modal
        isOpen={isSaveModalOpen}
        onClose={() => setIsSaveModalOpen(false)}
        title="Save Excel Template"
        size="md"
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Template Name
            </label>
            <input
              type="text"
              value={currentTemplate.name}
              onChange={(e) => setCurrentTemplate(prev => ({ ...prev, name: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              placeholder="Enter template name"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              value={currentTemplate.description}
              onChange={(e) => setCurrentTemplate(prev => ({ ...prev, description: e.target.value }))}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              placeholder="Enter template description"
            />
          </div>

          <div className="bg-gray-50 rounded-lg p-3">
            <p className="text-sm text-gray-600">
              This template will be converted to a checksheet template with {convertExcelDataToFields(currentTemplate.data).length} fields.
            </p>
          </div>

          <div className="flex justify-end gap-3 pt-4">
            <Button
              variant="outline"
              onClick={() => setIsSaveModalOpen(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={confirmSave}
              loading={saving}
              className="flex items-center gap-2"
            >
              <Save className="w-4 h-4" />
              Save Template
            </Button>
          </div>
        </div>
      </Modal>
    </DashboardLayout>
  )
}
