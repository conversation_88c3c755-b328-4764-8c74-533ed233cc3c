// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlserver"
  url      = env("DATABASE_URL")
}

model User {
  id          String   @id @default(cuid())
  username    String   @unique
  email       String   @unique
  password    String
  fullName    String
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relationships
  departmentId String
  department   Department @relation(fields: [departmentId], references: [id])
  
  roleId       String
  role         Role @relation(fields: [roleId], references: [id])
  
  // Checksheets created by this user
  checksheets  Checksheet[]
  
  // Templates created by this user
  templates    ChecksheetTemplate[]
  
  // Folders created by this user
  folders      Folder[]
  
  // Approvals given by this user
  approvals    Approval[]

  // Notifications for this user
  notifications Notification[]

  // Audit logs for this user
  auditLogs    AuditLog[]

  @@map("users")
}

model Role {
  id          String   @id @default(cuid())
  name        String   @unique // executor, approver1, final_approver
  description String?
  permissions Json     // JSON object containing permissions
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relationships
  users       User[]
  
  @@map("roles")
}

model Department {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relationships
  users       User[]
  folders     Folder[]
  templates   ChecksheetTemplate[]
  checksheets Checksheet[]
  
  @@map("departments")
}

model Folder {
  id          String   @id @default(cuid())
  name        String
  description String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relationships
  departmentId String
  department   Department @relation(fields: [departmentId], references: [id])
  
  createdById  String
  createdBy    User @relation(fields: [createdById], references: [id])
  
  // Parent-child relationship for nested folders
  parentId     String?
  parent       Folder? @relation("FolderHierarchy", fields: [parentId], references: [id])
  children     Folder[] @relation("FolderHierarchy")
  
  // Templates in this folder
  templates    ChecksheetTemplate[]
  
  // Checksheets in this folder
  checksheets  Checksheet[]
  
  @@map("folders")
}

model ChecksheetTemplate {
  id          String   @id @default(cuid())
  name        String
  description String?
  version     String   @default("1.0")
  fields      Json     // JSON array of form fields configuration
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relationships
  departmentId String
  department   Department @relation(fields: [departmentId], references: [id])
  
  createdById  String
  createdBy    User @relation(fields: [createdById], references: [id])
  
  folderId     String?
  folder       Folder? @relation(fields: [folderId], references: [id])
  
  // Checksheets created from this template
  checksheets  Checksheet[]

  // Notifications related to this template
  notifications Notification[]

  @@map("checksheet_templates")
}

model Checksheet {
  id          String   @id @default(cuid())
  title       String
  description String?
  data        Json     // JSON object containing form data
  status      ChecksheetStatus @default(DRAFT)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  submittedAt DateTime?

  // Relationships
  templateId   String
  template     ChecksheetTemplate @relation(fields: [templateId], references: [id])

  departmentId String
  department   Department @relation(fields: [departmentId], references: [id])

  createdById  String
  createdBy    User @relation(fields: [createdById], references: [id])

  folderId     String?
  folder       Folder? @relation(fields: [folderId], references: [id])

  // Approval workflow
  approvals    Approval[]

  // Notifications related to this checksheet
  notifications Notification[]

  @@map("checksheets")
}

model Approval {
  id          String   @id @default(cuid())
  level       Int      // 1 for approver1, 2 for final_approver
  status      ApprovalStatus @default(PENDING)
  comments    String?
  approvedAt  DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relationships
  checksheetId String
  checksheet   Checksheet @relation(fields: [checksheetId], references: [id])

  approverId   String
  approver     User @relation(fields: [approverId], references: [id])

  @@unique([checksheetId, level])
  @@map("approvals")
}

// Enums
enum ChecksheetStatus {
  DRAFT
  SUBMITTED
  APPROVED_LEVEL_1
  APPROVED_FINAL
  REJECTED
}

enum ApprovalStatus {
  PENDING
  APPROVED
  REJECTED
}

model Notification {
  id          String   @id @default(cuid())
  title       String
  message     String
  type        NotificationType
  isRead      Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relationships
  userId      String
  user        User @relation(fields: [userId], references: [id])

  // Optional reference to related entities
  checksheetId String?
  checksheet   Checksheet? @relation(fields: [checksheetId], references: [id])

  templateId   String?
  template     ChecksheetTemplate? @relation(fields: [templateId], references: [id])

  // Metadata
  metadata    Json?    // Additional data like action details

  @@map("notifications")
}

model AuditLog {
  id          String   @id @default(cuid())
  action      String   // CREATE, UPDATE, DELETE, LOGIN, LOGOUT, etc.
  entity      String   // checksheet, template, user, etc.
  entityId    String?  // ID of the affected entity
  oldValues   Json?    // Previous values (for updates)
  newValues   Json?    // New values (for creates/updates)
  ipAddress   String?
  userAgent   String?
  createdAt   DateTime @default(now())

  // Relationships
  userId      String
  user        User @relation(fields: [userId], references: [id])

  @@map("audit_logs")
}

enum NotificationType {
  INFO
  SUCCESS
  WARNING
  ERROR
  APPROVAL_REQUEST
  APPROVAL_APPROVED
  APPROVAL_REJECTED
  CHECKSHEET_SUBMITTED
  CHECKSHEET_UPDATED
  TEMPLATE_CREATED
  USER_CREATED
}
