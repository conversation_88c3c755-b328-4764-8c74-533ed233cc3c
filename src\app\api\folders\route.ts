import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const createFolderSchema = z.object({
  name: z.string().min(1, 'Folder name is required'),
  description: z.string().optional(),
  departmentId: z.string().min(1, 'Department is required'),
  parentId: z.string().optional()
})

// GET /api/folders - Get folders with hierarchy
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const departmentId = searchParams.get('departmentId')
    const parentId = searchParams.get('parentId')
    const includeChildren = searchParams.get('includeChildren') === 'true'

    // Build where clause based on user permissions
    let where: any = { isActive: true }

    if (departmentId) {
      where.departmentId = departmentId
    } else if (!session.user.permissions?.canViewAllChecksheets) {
      // If user can't view all, limit to their department
      where.departmentId = session.user.departmentId
    }

    if (parentId !== null) {
      where.parentId = parentId || null
    }

    const folders = await prisma.folder.findMany({
      where,
      include: {
        department: true,
        createdBy: {
          select: {
            id: true,
            username: true,
            fullName: true
          }
        },
        parent: {
          select: {
            id: true,
            name: true
          }
        },
        children: includeChildren ? {
          where: { isActive: true },
          select: {
            id: true,
            name: true,
            description: true,
            createdAt: true
          }
        } : false,
        _count: {
          select: {
            templates: true,
            checksheets: true,
            children: true
          }
        }
      },
      orderBy: { name: 'asc' }
    })

    return NextResponse.json(folders)
  } catch (error) {
    console.error('Error fetching folders:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// POST /api/folders - Create new folder
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = createFolderSchema.parse(body)

    // Check if user can create folders in this department
    if (validatedData.departmentId !== session.user.departmentId && 
        !session.user.permissions?.canViewAllChecksheets) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Check if parent folder exists and belongs to same department
    if (validatedData.parentId) {
      const parentFolder = await prisma.folder.findUnique({
        where: { id: validatedData.parentId }
      })

      if (!parentFolder || parentFolder.departmentId !== validatedData.departmentId) {
        return NextResponse.json(
          { error: 'Invalid parent folder' },
          { status: 400 }
        )
      }
    }

    // Check if folder name already exists in the same parent/department
    const existingFolder = await prisma.folder.findFirst({
      where: {
        name: validatedData.name,
        departmentId: validatedData.departmentId,
        parentId: validatedData.parentId || null,
        isActive: true
      }
    })

    if (existingFolder) {
      return NextResponse.json(
        { error: 'Folder name already exists in this location' },
        { status: 400 }
      )
    }

    // Create folder
    const folder = await prisma.folder.create({
      data: {
        ...validatedData,
        createdById: session.user.id
      },
      include: {
        department: true,
        createdBy: {
          select: {
            id: true,
            username: true,
            fullName: true
          }
        },
        parent: {
          select: {
            id: true,
            name: true
          }
        },
        _count: {
          select: {
            templates: true,
            checksheets: true,
            children: true
          }
        }
      }
    })

    return NextResponse.json(folder, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error creating folder:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
