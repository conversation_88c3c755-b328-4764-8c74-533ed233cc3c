{"version": 3, "file": "styles.css", "names": [], "sources": ["../src/style/layers.css", "../src/utils/renderMeasuringCells_si403r.css", "../src/style/cell_1uej27p.css", "../src/cellRenderers/renderCheckbox_kgmenj.css", "../src/cellRenderers/renderToggleGroup_12hocar.css", "../src/renderHeaderCell_1ksod9.css", "../src/Cell_g221z0.css", "../src/EditCell_s6zkrx.css", "../src/HeaderCell_b3eyzp.css", "../src/style/row_1bp673d.css", "../src/HeaderRow_15snfl4.css", "../src/sortStatus_1gpu3qo.css", "../src/style/core_5bowll.css", "../src/SummaryCell_1tan0iy.css", "../src/SummaryRow_jxoqkm.css", "../src/GroupRow_pjqmfy.css", "../src/editors/textEditor_1rjgdky.css"], "sourcesContent": ["@layer rdg {\n  @layer Defaults,\n    FocusSink,\n    CheckboxInput,\n    CheckboxIcon,\n    CheckboxLabel,\n    Cell,\n    HeaderCell,\n    SummaryCell,\n    EditCell,\n    Row,\n    HeaderRow,\n    SummaryRow,\n    GroupedRow,\n    Root;\n}\n", ".mlln6zg7-0-0-beta-56 {\n  @layer rdg.MeasuringCell {\n    contain: strict;\n    grid-row: 1;\n    visibility: hidden;\n  }\n}\n\n", ".cj343x07-0-0-beta-56 {\n  @layer rdg.Cell {\n    /* max-content does not work with size containment\n     * dynamically switching between different containment styles incurs a heavy relayout penalty\n     * Chromium bug: at odd zoom levels or subpixel positioning,\n     * layout/paint/style containment can make cell borders disappear\n     *   https://issues.chromium.org/issues/40840864\n     */\n    position: relative; /* needed for absolute positioning to work */\n    padding-block: 0;\n    padding-inline: 8px;\n    border-inline-end: 1px solid var(--rdg-border-color);\n    border-block-end: 1px solid var(--rdg-border-color);\n    grid-row-start: var(--rdg-grid-row-start);\n    align-content: center;\n    background-color: inherit;\n\n    white-space: nowrap;\n    overflow: clip;\n    text-overflow: ellipsis;\n    outline: none;\n\n    &[aria-selected='true'] {\n      outline: 2px solid var(--rdg-selection-color);\n      outline-offset: -2px;\n    }\n  }\n}\n\n.csofj7r7-0-0-beta-56 {\n  @layer rdg.Cell {\n    position: sticky;\n    /* Should have a higher value than 0 to show up above unfrozen cells */\n    z-index: 1;\n\n    /* Add box-shadow on the last frozen cell */\n    &:nth-last-child(1 of &) {\n      box-shadow: var(--rdg-cell-frozen-box-shadow);\n    }\n  }\n}\n\n.ch2wcw87-0-0-beta-56 {\n  @layer rdg.DragHandle {\n    --rdg-drag-handle-size: 8px;\n    z-index: 0;\n    cursor: move;\n    inline-size: var(--rdg-drag-handle-size);\n    block-size: var(--rdg-drag-handle-size);\n    background-color: var(--rdg-selection-color);\n    place-self: end;\n\n    &:hover {\n      --rdg-drag-handle-size: 16px;\n      border: 2px solid var(--rdg-selection-color);\n      background-color: var(--rdg-background-color);\n    }\n  }\n}\n\n.c1wvphzh7-0-0-beta-56 {\n  @layer rdg.DragHandle {\n    z-index: 1;\n    position: sticky;\n  }\n}\n\n", ".c1bn88vv7-0-0-beta-56 {\n  @layer rdg.CheckboxInput {\n    display: block;\n    margin: auto;\n    inline-size: 20px;\n    block-size: 20px;\n\n    &:focus-visible {\n      outline: 2px solid var(--rdg-checkbox-focus-color);\n      outline-offset: -3px;\n    }\n\n    &:enabled {\n      cursor: pointer;\n    }\n  }\n}\n\n", ".g1s9ylgp7-0-0-beta-56 {\n  @layer rdg.GroupCellContent {\n    outline: none;\n  }\n}\n\n.cz54e4y7-0-0-beta-56 {\n  @layer rdg.GroupCellCaret {\n    margin-inline-start: 4px;\n    stroke: currentColor;\n    stroke-width: 1.5px;\n    fill: transparent;\n    vertical-align: middle;\n\n    > path {\n      transition: d 0.1s;\n    }\n  }\n}\n\n", ".h44jtk67-0-0-beta-56 {\n  @layer rdg.SortableHeaderCell {\n    display: flex;\n  }\n}\n\n.hcgkhxz7-0-0-beta-56 {\n  @layer rdg.SortableHeaderCellName {\n    flex-grow: 1;\n    overflow: clip;\n    text-overflow: ellipsis;\n  }\n}\n\n", ".c6ra8a37-0-0-beta-56 {\n  @layer rdg.Cell {\n    background-color: #ccccff;\n  }\n}\n\n", ".cis5rrm7-0-0-beta-56 {\n  @layer rdg.EditCell {\n    padding: 0;\n  }\n}\n\n", ".c6l2wv17-0-0-beta-56 {\n  @layer rdg.HeaderCell {\n    cursor: pointer;\n  }\n}\n\n.c1kqdw7y7-0-0-beta-56 {\n  @layer rdg.HeaderCell {\n    touch-action: none;\n  }\n}\n\n.r1y6ywlx7-0-0-beta-56 {\n  @layer rdg.HeaderCell {\n    cursor: col-resize;\n    position: absolute;\n    inset-block-start: 0;\n    inset-inline-end: 0;\n    inset-block-end: 0;\n    inline-size: 10px;\n  }\n}\n\n.c1bezg5o7-0-0-beta-56 {\n  @layer rdg.HeaderCell {\n    background-color: var(--rdg-header-draggable-background-color);\n  }\n}\n\n.c1vc96037-0-0-beta-56 {\n  @layer rdg.Header<PERSON>ell {\n    background-color: var(--rdg-header-draggable-background-color);\n  }\n}\n\n.d8rwc9w7-0-0-beta-56 {\n  @layer rdg.HeaderCell {\n    border-radius: 4px;\n    width: fit-content;\n    outline: 2px solid hsl(207, 100%, 50%);\n    outline-offset: -2px;\n  }\n}\n\n", ".r1upfr807-0-0-beta-56 {\n  @layer rdg.Row {\n    display: contents;\n    background-color: var(--rdg-background-color);\n\n    &:hover {\n      background-color: var(--rdg-row-hover-background-color);\n    }\n\n    &[aria-selected='true'] {\n      background-color: var(--rdg-row-selected-background-color);\n\n      &:hover {\n        background-color: var(--rdg-row-selected-hover-background-color);\n      }\n    }\n  }\n}\n\n.r190mhd37-0-0-beta-56 {\n  @layer rdg.FocusSink {\n    outline: 2px solid var(--rdg-selection-color);\n    outline-offset: -2px;\n  }\n}\n\n.r139qu9m7-0-0-beta-56 {\n  @layer rdg.FocusSink {\n    &::before {\n      content: '';\n      display: inline-block;\n      block-size: 100%;\n      position: sticky;\n      inset-inline-start: 0;\n      border-inline-start: 2px solid var(--rdg-selection-color);\n    }\n  }\n}\n\n", ".h10tskcx7-0-0-beta-56 {\n  @layer rdg.HeaderRow {\n    display: contents;\n    background-color: var(--rdg-header-background-color);\n    font-weight: bold;\n\n    & > .cj343x07-0-0-beta-56 {\n      /* Should have a higher value than 1 to show up above regular cells and the focus sink */\n      z-index: 2;\n      position: sticky;\n    }\n\n    & > .csofj7r7-0-0-beta-56 {\n      z-index: 3;\n    }\n  }\n}\n\n", ".a3ejtar7-0-0-beta-56 {\n  @layer rdg.SortIcon {\n    fill: currentColor;\n\n    > path {\n      transition: d 0.1s;\n    }\n  }\n}\n\n", ".rnvodz57-0-0-beta-56 {\n  @layer rdg.Defaults {\n    *,\n    *::before,\n    *::after {\n      box-sizing: inherit;\n    }\n  }\n\n  @layer rdg.Root {\n    --rdg-color: #000;   --rdg-border-color: #ddd;   --rdg-summary-border-color: #aaa;   --rdg-background-color: hsl(0deg 0% 100%);   --rdg-header-background-color: hsl(0deg 0% 97.5%);   --rdg-header-draggable-background-color: hsl(0deg 0% 90.5%);   --rdg-row-hover-background-color: hsl(0deg 0% 96%);   --rdg-row-selected-background-color: hsl(207deg 76% 92%);   --rdg-row-selected-hover-background-color: hsl(207deg 76% 88%);   --rdg-checkbox-focus-color: hsl(207deg 100% 69%);\n    --rdg-selection-color: hsl(207, 75%, 66%);\n    --rdg-font-size: 14px;\n    --rdg-cell-frozen-box-shadow: 2px 0 5px -2px rgba(136, 136, 136, 0.3);\n\n    &:dir(rtl) {\n      --rdg-cell-frozen-box-shadow: -2px 0 5px -2px rgba(136, 136, 136, 0.3);\n    }\n\n    display: grid;\n\n    color-scheme: var(--rdg-color-scheme, light dark);\n    accent-color: light-dark(hsl(207deg 100% 29%), hsl(207deg 100% 79%));\n\n    /* https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Positioning/Understanding_z_index/The_stacking_context */\n    /* We set a stacking context so internal elements don't render on top of external elements. */\n    /* size containment is not used as it could break \"width: min-content\" for example, and the grid would infinitely resize on Chromium browsers */\n    contain: content;\n    content-visibility: auto;\n    block-size: 350px;\n    border: 1px solid var(--rdg-border-color);\n    box-sizing: border-box;\n    overflow: auto;\n    background-color: var(--rdg-background-color);\n    color: var(--rdg-color);\n    font-size: var(--rdg-font-size);\n\n    /* needed on Firefox to fix scrollbars */\n    &::before {\n      content: '';\n      grid-column: 1/-1;\n      grid-row: 1/-1;\n    }\n\n    &.rdg-dark {\n      --rdg-color-scheme: dark;\n      --rdg-color: #ddd;   --rdg-border-color: #444;   --rdg-summary-border-color: #555;   --rdg-background-color: hsl(0deg 0% 13%);   --rdg-header-background-color: hsl(0deg 0% 10.5%);   --rdg-header-draggable-background-color: hsl(0deg 0% 17.5%);   --rdg-row-hover-background-color: hsl(0deg 0% 9%);   --rdg-row-selected-background-color: hsl(207deg 76% 42%);   --rdg-row-selected-hover-background-color: hsl(207deg 76% 38%);   --rdg-checkbox-focus-color: hsl(207deg 100% 89%);\n    }\n\n    &.rdg-light {\n      --rdg-color-scheme: light;\n    }\n\n    @media (prefers-color-scheme: dark) {\n      &:not(.rdg-light) {\n        --rdg-color: #ddd;   --rdg-border-color: #444;   --rdg-summary-border-color: #555;   --rdg-background-color: hsl(0deg 0% 13%);   --rdg-header-background-color: hsl(0deg 0% 10.5%);   --rdg-header-draggable-background-color: hsl(0deg 0% 17.5%);   --rdg-row-hover-background-color: hsl(0deg 0% 9%);   --rdg-row-selected-background-color: hsl(207deg 76% 42%);   --rdg-row-selected-hover-background-color: hsl(207deg 76% 38%);   --rdg-checkbox-focus-color: hsl(207deg 100% 89%);\n      }\n    }\n\n    > :nth-last-child(1 of .rdg-top-summary-row) {\n      > .cj343x07-0-0-beta-56 {\n        border-block-end: 2px solid var(--rdg-summary-border-color);\n      }\n    }\n\n    > :nth-child(1 of .rdg-bottom-summary-row) {\n      > .cj343x07-0-0-beta-56 {\n        border-block-start: 2px solid var(--rdg-summary-border-color);\n      }\n    }\n  }\n}\n\n.vlqv91k7-0-0-beta-56 {\n  @layer rdg.Root {\n    user-select: none;\n\n    & .r1upfr807-0-0-beta-56 {\n      cursor: move;\n    }\n  }\n}\n\n.f1lsfrzw7-0-0-beta-56 {\n  @layer rdg.FocusSink {\n    grid-column: 1/-1;\n    pointer-events: none;\n    /* Should have a higher value than 1 to show up above regular frozen cells */\n    z-index: 1;\n  }\n}\n\n.f1cte0lg7-0-0-beta-56 {\n  @layer rdg.FocusSink {\n    /* Should have a higher value than 3 to show up above header and summary rows */\n    z-index: 3;\n  }\n}\n\n", ".s8wc6fl7-0-0-beta-56 {\n  @layer rdg.SummaryCell {\n    inset-block-start: var(--rdg-summary-row-top);\n    inset-block-end: var(--rdg-summary-row-bottom);\n  }\n}\n\n", ".skuhp557-0-0-beta-56 {\n  @layer rdg.SummaryRow {\n    > .cj343x07-0-0-beta-56 {\n      position: sticky;\n    }\n  }\n}\n\n.tf8l5ub7-0-0-beta-56 {\n  @layer rdg.SummaryRow {\n    > .cj343x07-0-0-beta-56 {\n      z-index: 2;\n    }\n\n    > .csofj7r7-0-0-beta-56 {\n      z-index: 3;\n    }\n  }\n}\n\n", ".g1yxluv37-0-0-beta-56 {\n  @layer rdg.GroupedRow {\n    &:not([aria-selected='true']) {\n      background-color: var(--rdg-header-background-color);\n    }\n\n    > .cj343x07-0-0-beta-56:not(:last-child, .csofj7r7-0-0-beta-56),\n    > :nth-last-child(n + 2 of .csofj7r7-0-0-beta-56) {\n      border-inline-end: none;\n    }\n  }\n}\n\n", ".t7vyx3i7-0-0-beta-56 {\n  @layer rdg.TextEditor {\n    appearance: none;\n\n    box-sizing: border-box;\n    inline-size: 100%;\n    block-size: 100%;\n    padding-block: 0;\n    padding-inline: 6px;\n    border: 2px solid #ccc;\n    vertical-align: top;\n    color: var(--rdg-color);\n    background-color: var(--rdg-background-color);\n\n    font-family: inherit;\n    font-size: var(--rdg-font-size);\n\n    &:focus {\n      border-color: var(--rdg-selection-color);\n      outline: none;\n    }\n\n    &::placeholder {\n      color: #999;\n      opacity: 1;\n    }\n  }\n}\n\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;ACfA;AACA;AACA;AACA;AACA;AACA;AACA;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;;ACjEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;AChBA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;AClBA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;ACZA;AACA;AACA;AACA;AACA;;;ACJA;AACA;AACA;AACA;AACA;;;ACJA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AC1CA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;ACrCA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;AChBA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;ACRA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;;ACjGA;AACA;AACA;AACA;AACA;AACA;;;ACLA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;AClBA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;;ACXA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA"}