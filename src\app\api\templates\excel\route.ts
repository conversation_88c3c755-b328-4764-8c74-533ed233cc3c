import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const excelTemplateSchema = z.object({
  name: z.string().min(1, 'Template name is required'),
  description: z.string().optional(),
  excelData: z.array(z.array(z.any())),
  folderId: z.string().optional()
})

// POST /api/templates/excel - Create Excel-based template
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user can create templates
    if (!session.user.permissions?.canCreateTemplate) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const body = await request.json()
    const validatedData = excelTemplateSchema.parse(body)

    // Convert Excel data to template fields
    const fields = convertExcelDataToFields(validatedData.excelData)

    // Check if folder exists and user has access
    if (validatedData.folderId) {
      const folder = await prisma.folder.findUnique({
        where: { id: validatedData.folderId }
      })

      if (!folder) {
        return NextResponse.json({ error: 'Folder not found' }, { status: 404 })
      }

      if (folder.departmentId !== session.user.departmentId && 
          !session.user.permissions?.canViewAllChecksheets) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
      }
    }

    // Create template with Excel metadata
    const template = await prisma.checksheetTemplate.create({
      data: {
        name: validatedData.name,
        description: validatedData.description,
        version: '1.0',
        fields: {
          excelData: validatedData.excelData,
          fields: fields,
          type: 'excel'
        },
        departmentId: session.user.departmentId,
        createdById: session.user.id,
        folderId: validatedData.folderId
      },
      include: {
        department: true,
        createdBy: {
          select: {
            id: true,
            username: true,
            fullName: true
          }
        },
        folder: {
          select: {
            id: true,
            name: true
          }
        },
        _count: {
          select: {
            checksheets: true
          }
        }
      }
    })

    return NextResponse.json(template, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error creating Excel template:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

function convertExcelDataToFields(excelData: any[][]): any[] {
  const fields: any[] = []
  
  excelData.forEach((row, rowIndex) => {
    row.forEach((cell, colIndex) => {
      if (cell && cell.value && cell.value.toString().trim()) {
        const cellValue = cell.value.toString().trim()
        
        // Skip empty cells and cells that look like headers
        if (cellValue.length === 0) return

        // Determine field type based on cell content and position
        let fieldType = 'text'
        let fieldOptions: string[] | undefined

        // Check if it looks like a dropdown (contains options separated by |)
        if (cellValue.includes('|')) {
          fieldType = 'select'
          fieldOptions = cellValue.split('|').map(opt => opt.trim())
        }
        // Check if it's a yes/no question
        else if (cellValue.toLowerCase().includes('yes/no') || 
                 cellValue.toLowerCase().includes('true/false')) {
          fieldType = 'radio'
          fieldOptions = ['Yes', 'No']
        }
        // Check if it looks like a date field
        else if (cellValue.toLowerCase().includes('date') || 
                 cellValue.toLowerCase().includes('time')) {
          fieldType = 'date'
        }
        // Check if it looks like a number field
        else if (cellValue.toLowerCase().includes('number') || 
                 cellValue.toLowerCase().includes('quantity') ||
                 cellValue.toLowerCase().includes('amount')) {
          fieldType = 'number'
        }
        // Check if it looks like a long text field
        else if (cellValue.toLowerCase().includes('description') || 
                 cellValue.toLowerCase().includes('comment') ||
                 cellValue.toLowerCase().includes('note')) {
          fieldType = 'textarea'
        }

        fields.push({
          id: `excel_${rowIndex}_${colIndex}`,
          type: fieldType,
          label: cellValue,
          required: cellValue.includes('*') || cellValue.toLowerCase().includes('required'),
          placeholder: `Enter ${cellValue.toLowerCase()}`,
          options: fieldOptions,
          validation: {
            min: fieldType === 'number' ? 0 : undefined,
            max: fieldType === 'number' ? 999999 : undefined
          },
          excelPosition: {
            row: rowIndex,
            col: colIndex
          },
          style: cell.style || {}
        })
      }
    })
  })

  return fields
}

// GET /api/templates/excel - Get Excel templates
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')

    const skip = (page - 1) * limit

    // Build where clause based on user permissions
    let where: any = { 
      isActive: true,
      fields: {
        path: ['type'],
        equals: 'excel'
      }
    }

    if (!session.user.permissions?.canViewAllChecksheets) {
      where.departmentId = session.user.departmentId
    }

    const [templates, total] = await Promise.all([
      prisma.checksheetTemplate.findMany({
        where,
        skip,
        take: limit,
        include: {
          department: true,
          createdBy: {
            select: {
              id: true,
              username: true,
              fullName: true
            }
          },
          folder: {
            select: {
              id: true,
              name: true
            }
          },
          _count: {
            select: {
              checksheets: true
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.checksheetTemplate.count({ where })
    ])

    return NextResponse.json({
      templates,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('Error fetching Excel templates:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
