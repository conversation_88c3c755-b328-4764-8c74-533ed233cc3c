import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const updateFolderSchema = z.object({
  name: z.string().min(1).optional(),
  description: z.string().optional(),
  parentId: z.string().optional()
})

// GET /api/folders/[id] - Get folder by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const folder = await prisma.folder.findUnique({
      where: { id: params.id },
      include: {
        department: true,
        createdBy: {
          select: {
            id: true,
            username: true,
            fullName: true
          }
        },
        parent: {
          select: {
            id: true,
            name: true
          }
        },
        children: {
          where: { isActive: true },
          select: {
            id: true,
            name: true,
            description: true,
            createdAt: true
          }
        },
        templates: {
          where: { isActive: true },
          select: {
            id: true,
            name: true,
            description: true,
            version: true,
            createdAt: true
          }
        },
        checksheets: {
          select: {
            id: true,
            title: true,
            status: true,
            createdAt: true,
            createdBy: {
              select: {
                id: true,
                username: true,
                fullName: true
              }
            }
          }
        },
        _count: {
          select: {
            templates: true,
            checksheets: true,
            children: true
          }
        }
      }
    })

    if (!folder) {
      return NextResponse.json({ error: 'Folder not found' }, { status: 404 })
    }

    // Check if user can access this folder
    if (folder.departmentId !== session.user.departmentId && 
        !session.user.permissions?.canViewAllChecksheets) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    return NextResponse.json(folder)
  } catch (error) {
    console.error('Error fetching folder:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// PUT /api/folders/[id] - Update folder
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = updateFolderSchema.parse(body)

    // Check if folder exists
    const existingFolder = await prisma.folder.findUnique({
      where: { id: params.id }
    })

    if (!existingFolder) {
      return NextResponse.json({ error: 'Folder not found' }, { status: 404 })
    }

    // Check if user can edit this folder
    if (existingFolder.departmentId !== session.user.departmentId && 
        !session.user.permissions?.canViewAllChecksheets) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Check if new parent folder is valid
    if (validatedData.parentId) {
      const parentFolder = await prisma.folder.findUnique({
        where: { id: validatedData.parentId }
      })

      if (!parentFolder || parentFolder.departmentId !== existingFolder.departmentId) {
        return NextResponse.json(
          { error: 'Invalid parent folder' },
          { status: 400 }
        )
      }

      // Prevent circular reference
      if (validatedData.parentId === params.id) {
        return NextResponse.json(
          { error: 'Cannot set folder as its own parent' },
          { status: 400 }
        )
      }
    }

    // Check for name conflicts if name is being updated
    if (validatedData.name) {
      const conflictFolder = await prisma.folder.findFirst({
        where: {
          name: validatedData.name,
          departmentId: existingFolder.departmentId,
          parentId: validatedData.parentId || existingFolder.parentId,
          isActive: true,
          id: { not: params.id }
        }
      })

      if (conflictFolder) {
        return NextResponse.json(
          { error: 'Folder name already exists in this location' },
          { status: 400 }
        )
      }
    }

    // Update folder
    const folder = await prisma.folder.update({
      where: { id: params.id },
      data: validatedData,
      include: {
        department: true,
        createdBy: {
          select: {
            id: true,
            username: true,
            fullName: true
          }
        },
        parent: {
          select: {
            id: true,
            name: true
          }
        },
        _count: {
          select: {
            templates: true,
            checksheets: true,
            children: true
          }
        }
      }
    })

    return NextResponse.json(folder)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error updating folder:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// DELETE /api/folders/[id] - Delete folder
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if folder exists
    const folder = await prisma.folder.findUnique({
      where: { id: params.id },
      include: {
        _count: {
          select: {
            templates: true,
            checksheets: true,
            children: true
          }
        }
      }
    })

    if (!folder) {
      return NextResponse.json({ error: 'Folder not found' }, { status: 404 })
    }

    // Check if user can delete this folder
    if (folder.departmentId !== session.user.departmentId && 
        !session.user.permissions?.canViewAllChecksheets) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Check if folder has content
    if (folder._count.templates > 0 || folder._count.checksheets > 0 || folder._count.children > 0) {
      return NextResponse.json(
        { error: 'Cannot delete folder with content. Please move or delete all content first.' },
        { status: 400 }
      )
    }

    // Soft delete folder
    await prisma.folder.update({
      where: { id: params.id },
      data: { isActive: false }
    })

    return NextResponse.json({ message: 'Folder deleted successfully' })
  } catch (error) {
    console.error('Error deleting folder:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
