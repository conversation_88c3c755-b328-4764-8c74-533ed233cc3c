'use client'

import { useState, useCallback, useRef, useEffect } from 'react'
import { 
  Download, 
  Upload, 
  Save, 
  Plus, 
  Trash2, 
  Bold, 
  Italic, 
  Underline,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Palette
} from 'lucide-react'
import * as XLSX from 'xlsx'

interface CellData {
  value: any
  formula?: string
  style?: {
    fontWeight?: 'bold' | 'normal'
    fontStyle?: 'italic' | 'normal'
    textDecoration?: 'underline' | 'none'
    textAlign?: 'left' | 'center' | 'right'
    backgroundColor?: string
    color?: string
    fontSize?: number
    border?: string
  }
}

interface ExcelEditorProps {
  initialData?: CellData[][]
  onSave?: (data: CellData[][]) => void
  onExport?: (data: CellData[][]) => void
  readOnly?: boolean
}

export default function ExcelEditor({ 
  initialData, 
  onSave, 
  onExport, 
  readOnly = false 
}: ExcelEditorProps) {
  const [data, setData] = useState<CellData[][]>(() => {
    if (initialData) return initialData
    
    // Initialize with empty 20x10 grid
    const rows = 20
    const cols = 10
    return Array(rows).fill(null).map(() => 
      Array(cols).fill(null).map(() => ({ value: '', style: {} }))
    )
  })

  const [selectedCell, setSelectedCell] = useState<{ row: number; col: number } | null>(null)
  const [selectedRange, setSelectedRange] = useState<{
    startRow: number
    startCol: number
    endRow: number
    endCol: number
  } | null>(null)
  const [isSelecting, setIsSelecting] = useState(false)
  const [formulaBarValue, setFormulaBarValue] = useState('')
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Column headers (A, B, C, ...)
  const getColumnHeader = (index: number): string => {
    let result = ''
    let num = index
    while (num >= 0) {
      result = String.fromCharCode(65 + (num % 26)) + result
      num = Math.floor(num / 26) - 1
    }
    return result
  }

  // Update formula bar when cell selection changes
  useEffect(() => {
    if (selectedCell) {
      const cell = data[selectedCell.row]?.[selectedCell.col]
      setFormulaBarValue(cell?.formula || cell?.value || '')
    }
  }, [selectedCell, data])

  const handleCellClick = (row: number, col: number, event: React.MouseEvent) => {
    if (readOnly) return

    if (event.shiftKey && selectedCell) {
      // Range selection
      setSelectedRange({
        startRow: Math.min(selectedCell.row, row),
        startCol: Math.min(selectedCell.col, col),
        endRow: Math.max(selectedCell.row, row),
        endCol: Math.max(selectedCell.col, col)
      })
    } else {
      setSelectedCell({ row, col })
      setSelectedRange(null)
    }
  }

  const handleCellChange = (row: number, col: number, value: string) => {
    if (readOnly) return

    setData(prevData => {
      const newData = [...prevData]
      if (!newData[row]) {
        newData[row] = []
      }
      if (!newData[row][col]) {
        newData[row][col] = { value: '', style: {} }
      }

      // Check if it's a formula
      if (value.startsWith('=')) {
        newData[row][col] = {
          ...newData[row][col],
          formula: value,
          value: evaluateFormula(value, newData)
        }
      } else {
        newData[row][col] = {
          ...newData[row][col],
          value,
          formula: undefined
        }
      }

      return newData
    })
  }

  const evaluateFormula = (formula: string, gridData: CellData[][]): any => {
    try {
      // Simple formula evaluation (SUM, AVERAGE, etc.)
      const cleanFormula = formula.substring(1) // Remove '='
      
      // Handle SUM function
      if (cleanFormula.startsWith('SUM(')) {
        const range = cleanFormula.match(/SUM\(([A-Z]+\d+):([A-Z]+\d+)\)/)?.[1]
        if (range) {
          // Parse range and calculate sum
          // This is a simplified implementation
          return 'SUM_RESULT'
        }
      }
      
      // Handle simple arithmetic
      if (/^[\d+\-*/\s().]+$/.test(cleanFormula)) {
        return eval(cleanFormula)
      }
      
      return formula
    } catch (error) {
      return '#ERROR'
    }
  }

  const handleFormulaBarChange = (value: string) => {
    setFormulaBarValue(value)
    if (selectedCell) {
      handleCellChange(selectedCell.row, selectedCell.col, value)
    }
  }

  const applyStyle = (styleProperty: string, value: any) => {
    if (readOnly || !selectedCell) return

    setData(prevData => {
      const newData = [...prevData]
      const { row, col } = selectedCell

      if (!newData[row]) newData[row] = []
      if (!newData[row][col]) newData[row][col] = { value: '', style: {} }

      newData[row][col] = {
        ...newData[row][col],
        style: {
          ...newData[row][col].style,
          [styleProperty]: value
        }
      }

      return newData
    })
  }

  const addRow = () => {
    if (readOnly) return
    setData(prevData => [
      ...prevData,
      Array(prevData[0]?.length || 10).fill(null).map(() => ({ value: '', style: {} }))
    ])
  }

  const addColumn = () => {
    if (readOnly) return
    setData(prevData => 
      prevData.map(row => [...row, { value: '', style: {} }])
    )
  }

  const deleteRow = (rowIndex: number) => {
    if (readOnly) return
    setData(prevData => prevData.filter((_, index) => index !== rowIndex))
  }

  const deleteColumn = (colIndex: number) => {
    if (readOnly) return
    setData(prevData => 
      prevData.map(row => row.filter((_, index) => index !== colIndex))
    )
  }

  const exportToExcel = () => {
    const worksheet = XLSX.utils.aoa_to_sheet(
      data.map(row => row.map(cell => cell.value))
    )
    const workbook = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')
    XLSX.writeFile(workbook, 'checksheet.xlsx')
    
    if (onExport) {
      onExport(data)
    }
  }

  const importFromExcel = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      const data = new Uint8Array(e.target?.result as ArrayBuffer)
      const workbook = XLSX.read(data, { type: 'array' })
      const worksheet = workbook.Sheets[workbook.SheetNames[0]]
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) as any[][]

      const newData = jsonData.map(row => 
        row.map((cell: any) => ({ 
          value: cell || '', 
          style: {} 
        }))
      )

      setData(newData)
    }
    reader.readAsArrayBuffer(file)
  }

  const getCellStyle = (row: number, col: number): React.CSSProperties => {
    const cell = data[row]?.[col]
    const style = cell?.style || {}
    
    return {
      fontWeight: style.fontWeight || 'normal',
      fontStyle: style.fontStyle || 'normal',
      textDecoration: style.textDecoration || 'none',
      textAlign: style.textAlign || 'left',
      backgroundColor: style.backgroundColor || 'white',
      color: style.color || 'black',
      fontSize: style.fontSize || 14,
      border: style.border || '1px solid #e5e7eb'
    }
  }

  const isCellSelected = (row: number, col: number): boolean => {
    if (selectedRange) {
      return row >= selectedRange.startRow && 
             row <= selectedRange.endRow && 
             col >= selectedRange.startCol && 
             col <= selectedRange.endCol
    }
    return selectedCell?.row === row && selectedCell?.col === col
  }

  return (
    <div className="bg-white rounded-lg shadow-lg p-4">
      {/* Toolbar */}
      <div className="flex items-center gap-2 mb-4 p-2 border-b">
        <input
          ref={fileInputRef}
          type="file"
          accept=".xlsx,.xls"
          onChange={importFromExcel}
          className="hidden"
        />
        
        <button
          onClick={() => fileInputRef.current?.click()}
          className="flex items-center gap-1 px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded text-sm"
          disabled={readOnly}
        >
          <Upload className="w-4 h-4" />
          Import
        </button>
        
        <button
          onClick={exportToExcel}
          className="flex items-center gap-1 px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded text-sm"
        >
          <Download className="w-4 h-4" />
          Export
        </button>
        
        {onSave && (
          <button
            onClick={() => onSave(data)}
            className="flex items-center gap-1 px-3 py-1 bg-primary-600 text-white hover:bg-primary-700 rounded text-sm"
            disabled={readOnly}
          >
            <Save className="w-4 h-4" />
            Save
          </button>
        )}

        <div className="w-px h-6 bg-gray-300 mx-2" />

        {/* Formatting buttons */}
        <button
          onClick={() => applyStyle('fontWeight', 'bold')}
          className="p-1 hover:bg-gray-200 rounded"
          disabled={readOnly}
        >
          <Bold className="w-4 h-4" />
        </button>
        
        <button
          onClick={() => applyStyle('fontStyle', 'italic')}
          className="p-1 hover:bg-gray-200 rounded"
          disabled={readOnly}
        >
          <Italic className="w-4 h-4" />
        </button>
        
        <button
          onClick={() => applyStyle('textDecoration', 'underline')}
          className="p-1 hover:bg-gray-200 rounded"
          disabled={readOnly}
        >
          <Underline className="w-4 h-4" />
        </button>

        <div className="w-px h-6 bg-gray-300 mx-2" />

        <button
          onClick={() => applyStyle('textAlign', 'left')}
          className="p-1 hover:bg-gray-200 rounded"
          disabled={readOnly}
        >
          <AlignLeft className="w-4 h-4" />
        </button>
        
        <button
          onClick={() => applyStyle('textAlign', 'center')}
          className="p-1 hover:bg-gray-200 rounded"
          disabled={readOnly}
        >
          <AlignCenter className="w-4 h-4" />
        </button>
        
        <button
          onClick={() => applyStyle('textAlign', 'right')}
          className="p-1 hover:bg-gray-200 rounded"
          disabled={readOnly}
        >
          <AlignRight className="w-4 h-4" />
        </button>

        <div className="w-px h-6 bg-gray-300 mx-2" />

        <button
          onClick={addRow}
          className="flex items-center gap-1 px-2 py-1 bg-green-100 hover:bg-green-200 rounded text-sm"
          disabled={readOnly}
        >
          <Plus className="w-4 h-4" />
          Row
        </button>
        
        <button
          onClick={addColumn}
          className="flex items-center gap-1 px-2 py-1 bg-green-100 hover:bg-green-200 rounded text-sm"
          disabled={readOnly}
        >
          <Plus className="w-4 h-4" />
          Column
        </button>
      </div>

      {/* Formula Bar */}
      <div className="flex items-center gap-2 mb-4">
        <span className="text-sm font-medium w-16">
          {selectedCell ? `${getColumnHeader(selectedCell.col)}${selectedCell.row + 1}` : ''}
        </span>
        <input
          type="text"
          value={formulaBarValue}
          onChange={(e) => handleFormulaBarChange(e.target.value)}
          className="flex-1 px-3 py-1 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
          placeholder="Enter value or formula (=SUM(A1:A5))"
          disabled={readOnly}
        />
      </div>

      {/* Spreadsheet Grid */}
      <div className="overflow-auto border border-gray-300 rounded">
        <table className="min-w-full">
          <thead>
            <tr>
              <th className="w-12 h-8 bg-gray-100 border border-gray-300 text-xs"></th>
              {data[0]?.map((_, colIndex) => (
                <th
                  key={colIndex}
                  className="min-w-24 h-8 bg-gray-100 border border-gray-300 text-xs font-medium relative group"
                >
                  {getColumnHeader(colIndex)}
                  {!readOnly && (
                    <button
                      onClick={() => deleteColumn(colIndex)}
                      className="absolute top-0 right-0 w-4 h-4 bg-red-500 text-white text-xs opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      ×
                    </button>
                  )}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {data.map((row, rowIndex) => (
              <tr key={rowIndex}>
                <td className="w-12 h-8 bg-gray-100 border border-gray-300 text-xs text-center relative group">
                  {rowIndex + 1}
                  {!readOnly && (
                    <button
                      onClick={() => deleteRow(rowIndex)}
                      className="absolute top-0 right-0 w-4 h-4 bg-red-500 text-white text-xs opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      ×
                    </button>
                  )}
                </td>
                {row.map((cell, colIndex) => (
                  <td
                    key={colIndex}
                    className={`min-w-24 h-8 border border-gray-300 p-0 ${
                      isCellSelected(rowIndex, colIndex) ? 'bg-blue-100' : ''
                    }`}
                    onClick={(e) => handleCellClick(rowIndex, colIndex, e)}
                    style={getCellStyle(rowIndex, colIndex)}
                  >
                    <input
                      type="text"
                      value={cell.value || ''}
                      onChange={(e) => handleCellChange(rowIndex, colIndex, e.target.value)}
                      className="w-full h-full px-1 bg-transparent border-none outline-none text-xs"
                      disabled={readOnly}
                    />
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}
