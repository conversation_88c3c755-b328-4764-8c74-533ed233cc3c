{"version": 3, "file": "index.js", "names": ["column: CalculatedColumn<R, SR>", "lastFrozenColumnIndex: number", "args: ColSpanArgs<R, SR>", "event: React.SyntheticEvent", "element: Maybe<Element>", "behavior: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "event: E", "e: React.KeyboardEvent", "event: React.KeyboardEvent<HTMLDivElement>", "isUserHandlingPaste: boolean", "direction: Maybe<Direction>", "CalculatedColumn", "measuringCellClassname", "renderMeasuringCells", "viewportColumns", "R", "SR", "map", "key", "idx", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "gridColumnStart", "row", "column: CalculatedColumn<R, SR>", "row: R", "moveNext: boolean", "moveNext", "parent: CalculatedColumnParent<R, SR>", "cell", "cellClassname", "cellFrozen", "cellFrozenClassname", "cellDragHandle", "cellDragHandleFrozenClassname", "cellDragHandleClassname", "rowIdx: number", "column: CalculatedColumnOrColumnGroup<R, SR>", "rowSpan: number", "column: CalculatedColumn<R, SR>", "keyGetter: Maybe<(row: NoInfer<R>) => K>", "width: number", "column: CalculatedColumnOrColumnGroup<R, SR>", "rowIdx: number", "RenderCheckboxProps", "checkbox", "checkboxClassname", "renderCheckbox", "onChange", "indeterminate", "props", "handleChange", "e", "React", "ChangeEvent", "HTMLInputElement", "target", "checked", "nativeEvent", "MouseEvent", "shift<PERSON>ey", "el", "RenderGroupCellProps", "groupCellContent", "groupCellContentClassname", "caret", "caretClassname", "renderToggleGroup", "props", "R", "SR", "ToggleGroup", "groupKey", "isExpanded", "tabIndex", "toggleGroup", "handleKeyDown", "key", "React", "KeyboardEvent", "HTMLSpanElement", "d", "props: RenderCellProps<R, SR>", "renderCheckbox", "props: RenderHeaderCellProps<unknown>", "props: RenderCellProps<unknown>", "props: RenderGroupCellProps<unknown>", "SelectColumn: Column<any, any>", "RenderHeaderCellProps", "useDefaultRenderers", "headerSortCellClassname", "headerSortName", "headerSortNameClassname", "renderHeaderCell", "column", "sortDirection", "priority", "R", "SR", "sortable", "name", "SharedHeaderCellProps", "Pick", "SortableHeaderCellProps", "children", "React", "ReactNode", "SortableHeaderCell", "renderSortStatus", "defaultRenderCell", "lastFrozenColumnIndex", "headerRowsCount", "columns: MutableCalculatedColumn<R, SR>[]", "rawColumns: readonly ColumnOrColumnGroup<R, SR>[]", "level: number", "parent?: MutableCalculatedColumnParent<R, SR>", "rawColumns", "calculatedColumnParent: MutableCalculatedColumnParent<R, SR>", "column: MutableCalculatedColumn<R, SR>", "colSpanColumns: CalculatedColumn<R, SR>[]", "columnMetrics", "totalFrozenColumnWidth", "templateColumns: string[]", "layoutCssVars: Record<string, string>", "colOverscanStartIdx", "colOverscanEndIdx", "column: MutableCalculatedColumn<R, SR> | MutableCalculatedColumnParent<R, SR>", "index: number", "columns: readonly CalculatedColumn<R, SR>[]", "viewportColumns: readonly CalculatedColumn<R, SR>[]", "templateColumns: readonly string[]", "gridRef: React.RefObject<HTMLDivElement | null>", "gridWidth: number", "columnWidths: ColumnWidths", "onColumnWidthsChange: (columnWidths: ColumnWidths) => void", "onColumnResize: DataGridProps<R, SR>['onColumnResize']", "setColumnResizing: (isColumnResizing: boolean) => void", "columnsCanFlex: boolean", "columnsToMeasure: string[]", "column: CalculatedColumn<R, SR>", "nextWidth: ResizedWidth", "key: string", "offsetHeight", "clientHeight", "fn: T", "isSelected: boolean", "event: React.FocusEvent<HTMLDivElement>", "startIdx", "colIdx: number", "colSpan: number | undefined", "row", "viewportColumns: CalculatedColumn<R, SR>[]", "rowIdx: number", "offset: number", "totalRowHeight", "gridTemplateRows", "row", "memo", "MouseEvent", "useRovingTabIndex", "createCellEvent", "getCellClassname", "getCellStyle", "isCellEditableUtil", "CellMouseEventHandler", "CellRendererProps", "cellDraggedOver", "cellDraggedOverClassname", "Cell", "column", "colSpan", "isCellSelected", "isDraggedOver", "row", "rowIdx", "className", "onMouseDown", "onCellMouseDown", "onClick", "onCellClick", "onDoubleClick", "onCellDoubleClick", "onContextMenu", "onCellContextMenu", "onRowChange", "selectCell", "style", "props", "R", "SR", "tabIndex", "childTabIndex", "onFocus", "cellClass", "isEditable", "selectCellWrapper", "enableEditor", "idx", "handleMouseEvent", "event", "React", "HTMLDivElement", "<PERSON><PERSON><PERSON><PERSON>", "eventHandled", "cellEvent", "isGridDefaultPrevented", "handleMouseDown", "handleClick", "handleDoubleClick", "handleContextMenu", "handleRowChange", "newRow", "undefined", "renderCell", "isCellEditable", "CellComponent", "JSX", "Element", "defaultRenderCell", "key", "Key", "useLayoutEffect", "useRef", "useLatestFunc", "createCellEvent", "getCellClassname", "getCellStyle", "onEditorNavigation", "CellKeyboardEvent", "CellRendererProps", "EditCellKeyDownArgs", "Maybe", "Omit", "RenderEditCellProps", "global", "scheduler", "Scheduler", "postTask", "callback", "options", "priority", "signal", "AbortSignal", "delay", "Promise", "canUsePostTask", "cellEditing", "SharedCellRendererProps", "Pick", "R", "SR", "EditCellProps", "rowIdx", "onRowChange", "row", "commitChanges", "shouldFocusCell", "closeEditor", "navigate", "event", "React", "KeyboardEvent", "HTMLDivElement", "onKeyDown", "args", "EditCell", "column", "colSpan", "captureEventRef", "MouseEvent", "undefined", "abortControllerRef", "AbortController", "frameRequestRef", "commitOnOutsideClick", "editorOptions", "commitOnOutsideMouseDown", "onClose", "onWindowCaptureMouseDown", "current", "abortController", "catch", "requestAnimationFrame", "onWindowMouseDown", "addEventListener", "capture", "removeEventListener", "cancelTask", "abort", "cancelAnimationFrame", "handleKeyDown", "cellEvent", "mode", "isGridDefaultPrevented", "key", "onEditorRowChange", "commitChangesAndFocus", "cellClass", "className", "displayCellContent", "idx", "renderEditCell", "renderCell", "isCellEditable", "tabIndex", "useRef", "useState", "flushSync", "useRovingTabIndex", "clampCol<PERSON><PERSON><PERSON><PERSON><PERSON>", "getCellClassname", "getCellStyle", "getHeaderCellRowSpan", "getHeaderCellStyle", "getLeftRightKey", "isCtrlKeyHeldDown", "stopPropagation", "CalculatedColumn", "SortColumn", "HeaderRowProps", "cellSortableClassname", "cellResizable", "cellResizableClassname", "resizeHandleClassname", "cellDraggableClassname", "cellDragging", "cellDraggingClassname", "cellOver", "cellOverClassname", "dragImageClassname", "SharedHeaderRowProps", "Pick", "R", "SR", "React", "Key", "HeaderCellProps", "column", "colSpan", "rowIdx", "isCellSelected", "draggedColumnKey", "setDraggedColumnKey", "<PERSON><PERSON><PERSON><PERSON>", "onColumnResize", "onColumnResizeEnd", "onColumnsReorder", "sortColumns", "onSortColumnsChange", "selectCell", "direction", "isOver", "setIsOver", "dragImageRef", "HTMLDivElement", "isDragging", "key", "rowSpan", "tabIndex", "childTabIndex", "onFocus", "sortIndex", "findIndex", "sort", "column<PERSON>ey", "sortColumn", "undefined", "sortDirection", "priority", "length", "ariaSort", "sortable", "resizable", "draggable", "className", "headerCellClass", "onSort", "ctrlClick", "sortDescending<PERSON>irst", "nextSort", "nextSortColumn", "nextSortColumns", "splice", "onMouseDown", "idx", "onClick", "event", "MouseEvent", "HTMLSpanElement", "ctrl<PERSON>ey", "metaKey", "onKeyDown", "KeyboardEvent", "preventDefault", "width", "currentTarget", "getBoundingClientRect", "leftKey", "offset", "newWidth", "onDragStart", "DragEvent", "dataTransfer", "setDragImage", "current", "dropEffect", "onDragEnd", "onDragOver", "onDrop", "onDragEnter", "isEventPertinent", "onDragLeave", "dragTargetProps", "ComponentProps", "dropTargetProps", "style", "CSSProperties", "content", "renderHeaderCell", "ResizeHandleProps", "ResizeHandle", "resizingOffsetRef", "isRtl", "onPointerDown", "PointerEvent", "pointerType", "buttons", "pointerId", "setPointerCapture", "headerCell", "parentElement", "right", "left", "clientX", "onPointerMove", "onLostPointerCapture", "onDoubleClick", "relatedTarget", "HTMLElement", "contains", "row", "rowClassname", "rowSelected", "rowSelectedClassname", "rowSelectedWithFrozenCell", "topSummaryRowClassname", "bottomSummaryRowClassname", "memo", "useState", "clsx", "getColSpan", "CalculatedColumn", "Direction", "Maybe", "Position", "ResizedWidth", "DataGridProps", "<PERSON><PERSON><PERSON><PERSON>", "rowSelectedClassname", "SharedDataGridProps", "React", "Key", "Pick", "R", "SR", "K", "HeaderRowProps", "rowIdx", "columns", "onColumnResize", "column", "width", "onColumnResizeEnd", "selectCell", "position", "lastFrozenColumnIndex", "selectedCellIdx", "direction", "headerRowClass", "headerRow", "headerRowClassname", "HeaderRow", "onColumnsReorder", "sortColumns", "onSortColumnsChange", "draggedColumnKey", "setDraggedColumnKey", "cells", "index", "length", "colSpan", "type", "undefined", "push", "key", "idx", "props", "JSX", "Element", "column: CalculatedColumn<R, SR>", "newRow: R", "row", "key: React.Key", "props: RenderRowProps<R, SR>", "RenderSortIconProps", "RenderSortPriorityProps", "RenderSortStatusProps", "arrow", "arrowClassname", "renderSortStatus", "sortDirection", "priority", "renderSortIcon", "renderSortPriority", "undefined", "root", "rootClassname", "viewportDragging", "viewportDraggingClassname", "focusSinkClassname", "focusSinkHeaderAndSummaryClassname", "memo", "useRovingTabIndex", "getCellClassname", "getCellStyle", "CellRendererProps", "summaryCellClassname", "SharedCellRendererProps", "Pick", "R", "SR", "SummaryCellProps", "row", "<PERSON><PERSON>ryCell", "column", "colSpan", "rowIdx", "isCellSelected", "selectCell", "tabIndex", "childTabIndex", "onFocus", "summaryCellClass", "className", "onMouseDown", "idx", "renderSummaryCell", "props", "React", "JSX", "Element", "memo", "clsx", "getColSpan", "getRowStyle", "RenderRowProps", "bottomSummaryRowClassname", "rowClassname", "rowSelectedClassname", "topSummaryRowClassname", "<PERSON><PERSON>ryCell", "SharedRenderRowProps", "Pick", "R", "SR", "SummaryRowProps", "row", "top", "bottom", "lastFrozenColumnIndex", "selectedCellIdx", "isTop", "summaryRow", "topSummaryRow", "summaryRowClassname", "SummaryRow", "rowIdx", "gridRowStart", "viewportColumns", "selectCell", "ariaRowIndex", "cells", "index", "length", "column", "colSpan", "type", "undefined", "isCellSelected", "idx", "push", "key", "React", "CSSProperties", "props", "JSX", "Element", "props: DataGridProps<R, SR, K>", "renderSortStatus", "defaultRenderSortStatus", "renderCheckbox", "defaultRenderCheckbox", "columnWidths: ColumnWidths", "columnWidths", "column: CalculatedColumn<R, SR>", "row", "cell", "args: SelectHeaderRowEvent", "args: SelectRowEvent<R>", "event: KeyboardEvent<HTMLDivElement>", "event: React.FocusEvent<HTMLDivElement>", "event: React.UIEvent<HTMLDivElement>", "scrollTop", "scrollLeft", "rowIdx: number", "row: R", "event: CellClipboardEvent", "event: React.PointerEvent<HTMLDivElement>", "event: React.MouseEvent<HTMLDivElement>", "startRowIdx: number", "endRowIdx: number", "indexes: number[]", "idx: number", "position: Position", "options?: SelectCellOptions", "key: string", "ctrlKey: boolean", "shiftKey: boolean", "cellNavigationMode: CellNavigationMode", "currentRowIdx: number", "style", "dragHandleStyle: React.CSSProperties", "shouldFocusCell: boolean", "shouldFocusCell", "commitChanges: boolean", "rowElements: React.ReactNode[]", "key: K | number", "GroupedColumnHeaderRow", "HeaderRow", "SummaryRow", "gridEl: HTMLDivElement", "p1: Position", "p2: Position", "memo", "useMemo", "clsx", "RowSelectionContext", "RowSelectionContextValue", "getRowStyle", "BaseRenderRowProps", "GroupRow", "SELECT_COLUMN_KEY", "GroupCell", "rowClassname", "rowSelectedClassname", "groupRow", "groupRowClassname", "GroupRowRendererProps", "R", "SR", "row", "groupBy", "toggleGroup", "expandedGroupId", "GroupedRow", "className", "rowIdx", "viewportColumns", "selectedCellIdx", "isRowSelected", "selectCell", "gridRowStart", "isRowSelectionDisabled", "props", "idx", "key", "level", "handleSelectGroup", "shouldFocusCell", "selectionValue", "setSize", "posInSet", "isExpanded", "map", "column", "id", "groupKey", "childRows", "includes", "React", "JSX", "Element", "columns", "groupBy: string[]", "rows: readonly R[]", "startRowIndex: number", "groups: GroupByDictionary<R>", "rows", "isGroupRow", "flattenedRows: Array<R | GroupRow<R>>", "rows: GroupByDictionary<R> | readonly R[]", "parentId: string | undefined", "level: number", "groupRow: GroupRow<R>", "groupRow", "row: R | GroupRow<R>", "row", "selectedRows", "newSelectedRows: Set<Key>", "args: CellKeyDownArgs<R, SR>", "event: CellKeyboardEvent", "event: CellClipboardEvent", "updatedRows: R[]", "rawIndexes: number[]", "groupId: unknown", "key: Key", "GroupedRow", "groupKey: string", "arr: unknown", "RenderEditCellProps", "textEditorInternalClassname", "textEditorClassname", "autoFocusAndSelect", "input", "HTMLInputElement", "focus", "select", "textEditor", "row", "column", "onRowChange", "onClose", "TRow", "TSummaryRow", "key", "event", "target", "value"], "sources": ["../src/utils/colSpanUtils.ts", "../src/utils/domUtils.ts", "../src/utils/eventUtils.ts", "../src/utils/keyboardUtils.ts", "../src/utils/renderMeasuringCells.tsx", "../src/utils/selectedCellUtils.ts", "../src/style/cell.ts", "../src/utils/styleUtils.ts", "../src/utils/index.ts", "../src/cellRenderers/renderCheckbox.tsx", "../src/cellRenderers/renderToggleGroup.tsx", "../src/cellRenderers/renderValue.tsx", "../src/DataGridDefaultRenderersContext.ts", "../src/cellRenderers/SelectCellFormatter.tsx", "../src/hooks/useRowSelection.ts", "../src/Columns.tsx", "../src/renderHeaderCell.tsx", "../src/hooks/useCalculatedColumns.ts", "../src/hooks/useColumnWidths.ts", "../src/hooks/useGridDimensions.ts", "../src/hooks/useLatestFunc.ts", "../src/hooks/useRovingTabIndex.ts", "../src/hooks/useViewportColumns.ts", "../src/hooks/useViewportRows.ts", "../src/Cell.tsx", "../src/EditCell.tsx", "../src/GroupedColumnHeaderCell.tsx", "../src/HeaderCell.tsx", "../src/style/row.ts", "../src/HeaderRow.tsx", "../src/GroupedColumnHeaderRow.tsx", "../src/Row.tsx", "../src/ScrollToCell.tsx", "../src/sortStatus.tsx", "../src/style/core.ts", "../src/SummaryCell.tsx", "../src/SummaryRow.tsx", "../src/DataGrid.tsx", "../src/GroupCell.tsx", "../src/GroupRow.tsx", "../src/TreeDataGrid.tsx", "../src/editors/textEditor.tsx"], "sourcesContent": ["import type { CalculatedColumn, ColSpanArgs } from '../types';\n\nexport function getColSpan<R, SR>(\n  column: CalculatedColumn<R, SR>,\n  lastFrozenColumnIndex: number,\n  args: ColSpanArgs<R, SR>\n): number | undefined {\n  const colSpan = typeof column.colSpan === 'function' ? column.colSpan(args) : 1;\n  if (\n    Number.isInteger(colSpan) &&\n    colSpan! > 1 &&\n    // ignore colSpan if it spans over both frozen and regular columns\n    (!column.frozen || column.idx + colSpan! - 1 <= lastFrozenColumnIndex)\n  ) {\n    return colSpan!;\n  }\n  return undefined;\n}\n", "import type { Maybe } from '../types';\n\nexport function stopPropagation(event: React.SyntheticEvent) {\n  event.stopPropagation();\n}\n\nexport function scrollIntoView(element: Maybe<Element>, behavior: ScrollBehavior = 'instant') {\n  element?.scrollIntoView({ inline: 'nearest', block: 'nearest', behavior });\n}\n", "import type { CellEvent } from '../types';\n\nexport function createCellEvent<E extends React.SyntheticEvent<HTMLDivElement>>(\n  event: E\n): CellEvent<E> {\n  let defaultPrevented = false;\n  const cellEvent = {\n    ...event,\n    preventGridDefault() {\n      defaultPrevented = true;\n    },\n    isGridDefaultPrevented() {\n      return defaultPrevented;\n    }\n  };\n\n  Object.setPrototypeOf(cellEvent, Object.getPrototypeOf(event));\n\n  return cellEvent;\n}\n", "import type { Direction, Maybe } from '../types';\n\n// https://developer.mozilla.org/en-US/docs/Web/API/KeyboardEvent/key/Key_Values\nconst nonInputKeys = new Set([\n  // Special keys\n  'Unidentified',\n  // Modifier keys\n  'Alt',\n  'AltGraph',\n  'CapsLock',\n  'Control',\n  'Fn',\n  'FnLock',\n  'Meta',\n  'NumLock',\n  'ScrollLock',\n  'Shift',\n  // Whitespace keys\n  'Tab',\n  // Navigation keys\n  'ArrowDown',\n  'ArrowLeft',\n  'ArrowRight',\n  'ArrowUp',\n  'End',\n  'Home',\n  'PageDown',\n  'PageUp',\n  // Editing\n  'Insert',\n  // UI keys\n  'ContextMenu',\n  'Escape',\n  'Pause',\n  'Play',\n  // Device keys\n  'PrintScreen',\n  // Function keys\n  'F1',\n  // 'F2', /!\\ specifically allowed, do not edit\n  'F3',\n  'F4',\n  'F5',\n  'F6',\n  'F7',\n  'F8',\n  'F9',\n  'F10',\n  'F11',\n  'F12'\n]);\n\nexport function isCtrlKeyHeldDown(e: React.KeyboardEvent): boolean {\n  return (e.ctrlKey || e.metaKey) && e.key !== 'Control';\n}\n\n// event.key may differ by keyboard input language, so we use event.keyCode instead\n// event.nativeEvent.code cannot be used either as it would break copy/paste for the DVORAK layout\nconst vKey = 86;\n\nexport function isDefaultCellInput(\n  event: React.KeyboardEvent<HTMLDivElement>,\n  isUserHandlingPaste: boolean\n): boolean {\n  // eslint-disable-next-line @typescript-eslint/no-deprecated\n  if (isCtrlKeyHeldDown(event) && (event.keyCode !== vKey || isUserHandlingPaste)) return false;\n  return !nonInputKeys.has(event.key);\n}\n\n/**\n * By default, the following navigation keys are enabled while an editor is open, under specific conditions:\n * - Tab:\n *   - The editor must be an <input>, a <textarea>, or a <select> element.\n *   - The editor element must be the only immediate child of the editor container/a label.\n */\nexport function onEditorNavigation({ key, target }: React.KeyboardEvent<HTMLDivElement>): boolean {\n  if (\n    key === 'Tab' &&\n    (target instanceof HTMLInputElement ||\n      target instanceof HTMLTextAreaElement ||\n      target instanceof HTMLSelectElement)\n  ) {\n    return (\n      target.closest('.rdg-editor-container')?.querySelectorAll('input, textarea, select')\n        .length === 1\n    );\n  }\n  return false;\n}\n\nexport function getLeftRightKey(direction: Maybe<Direction>) {\n  const isRtl = direction === 'rtl';\n\n  return {\n    leftKey: isRtl ? 'ArrowRight' : 'ArrowLeft',\n    rightKey: isRtl ? 'ArrowLeft' : 'ArrowRight'\n  } as const;\n}\n", "import { css } from '@linaria/core';\n\nimport type { CalculatedColumn } from '../types';\n\nconst measuringCellClassname = css`\n  @layer rdg.MeasuringCell {\n    contain: strict;\n    grid-row: 1;\n    visibility: hidden;\n  }\n`;\n\nexport function renderMeasuringCells<R, SR>(viewportColumns: readonly CalculatedColumn<R, SR>[]) {\n  return viewportColumns.map(({ key, idx, minWidth, maxWidth }) => (\n    <div\n      key={key}\n      className={measuringCellClassname}\n      style={{ gridColumnStart: idx + 1, minWidth, maxWidth }}\n      data-measuring-cell-key={key}\n    />\n  ));\n}\n", "import type {\n  CalculatedColumn,\n  CalculatedColumnParent,\n  CellNavigationMode,\n  Maybe,\n  Position\n} from '../types';\nimport { getColSpan } from './colSpanUtils';\n\ninterface IsSelectedCellEditableOpts<R, SR> {\n  selectedPosition: Position;\n  columns: readonly CalculatedColumn<R, SR>[];\n  rows: readonly R[];\n}\n\nexport function isSelectedCellEditable<R, SR>({\n  selectedPosition,\n  columns,\n  rows\n}: IsSelectedCellEditableOpts<R, SR>): boolean {\n  const column = columns[selectedPosition.idx];\n  const row = rows[selectedPosition.rowIdx];\n  return isCellEditableUtil(column, row);\n}\n\n// https://github.com/vercel/next.js/issues/56480\nexport function isCellEditableUtil<R, SR>(column: CalculatedColumn<R, SR>, row: R): boolean {\n  return (\n    column.renderEditCell != null &&\n    (typeof column.editable === 'function' ? column.editable(row) : column.editable) !== false\n  );\n}\n\ninterface GetNextSelectedCellPositionOpts<R, SR> {\n  moveUp: boolean;\n  moveNext: boolean;\n  cellNavigationMode: CellNavigationMode;\n  columns: readonly CalculatedColumn<R, SR>[];\n  colSpanColumns: readonly CalculatedColumn<R, SR>[];\n  rows: readonly R[];\n  topSummaryRows: Maybe<readonly SR[]>;\n  bottomSummaryRows: Maybe<readonly SR[]>;\n  minRowIdx: number;\n  mainHeaderRowIdx: number;\n  maxRowIdx: number;\n  currentPosition: Position;\n  nextPosition: Position;\n  lastFrozenColumnIndex: number;\n  isCellWithinBounds: (position: Position) => boolean;\n}\n\nfunction getSelectedCellColSpan<R, SR>({\n  rows,\n  topSummaryRows,\n  bottomSummaryRows,\n  rowIdx,\n  mainHeaderRowIdx,\n  lastFrozenColumnIndex,\n  column\n}: Pick<\n  GetNextSelectedCellPositionOpts<R, SR>,\n  'rows' | 'topSummaryRows' | 'bottomSummaryRows' | 'lastFrozenColumnIndex' | 'mainHeaderRowIdx'\n> & {\n  rowIdx: number;\n  column: CalculatedColumn<R, SR>;\n}) {\n  const topSummaryRowsCount = topSummaryRows?.length ?? 0;\n  if (rowIdx === mainHeaderRowIdx) {\n    return getColSpan(column, lastFrozenColumnIndex, { type: 'HEADER' });\n  }\n\n  if (\n    topSummaryRows &&\n    rowIdx > mainHeaderRowIdx &&\n    rowIdx <= topSummaryRowsCount + mainHeaderRowIdx\n  ) {\n    return getColSpan(column, lastFrozenColumnIndex, {\n      type: 'SUMMARY',\n      row: topSummaryRows[rowIdx + topSummaryRowsCount]\n    });\n  }\n\n  if (rowIdx >= 0 && rowIdx < rows.length) {\n    const row = rows[rowIdx];\n    return getColSpan(column, lastFrozenColumnIndex, { type: 'ROW', row });\n  }\n\n  if (bottomSummaryRows) {\n    return getColSpan(column, lastFrozenColumnIndex, {\n      type: 'SUMMARY',\n      row: bottomSummaryRows[rowIdx - rows.length]\n    });\n  }\n\n  return undefined;\n}\n\nexport function getNextSelectedCellPosition<R, SR>({\n  moveUp,\n  moveNext,\n  cellNavigationMode,\n  columns,\n  colSpanColumns,\n  rows,\n  topSummaryRows,\n  bottomSummaryRows,\n  minRowIdx,\n  mainHeaderRowIdx,\n  maxRowIdx,\n  currentPosition: { idx: currentIdx, rowIdx: currentRowIdx },\n  nextPosition,\n  lastFrozenColumnIndex,\n  isCellWithinBounds\n}: GetNextSelectedCellPositionOpts<R, SR>): Position {\n  let { idx: nextIdx, rowIdx: nextRowIdx } = nextPosition;\n  const columnsCount = columns.length;\n\n  const setColSpan = (moveNext: boolean) => {\n    // If a cell within the colspan range is selected then move to the\n    // previous or the next cell depending on the navigation direction\n    for (const column of colSpanColumns) {\n      const colIdx = column.idx;\n      if (colIdx > nextIdx) break;\n      const colSpan = getSelectedCellColSpan({\n        rows,\n        topSummaryRows,\n        bottomSummaryRows,\n        rowIdx: nextRowIdx,\n        mainHeaderRowIdx,\n        lastFrozenColumnIndex,\n        column\n      });\n\n      if (colSpan && nextIdx > colIdx && nextIdx < colSpan + colIdx) {\n        nextIdx = colIdx + (moveNext ? colSpan : 0);\n        break;\n      }\n    }\n  };\n\n  const getParentRowIdx = (parent: CalculatedColumnParent<R, SR>) => {\n    return parent.level + mainHeaderRowIdx;\n  };\n\n  const setHeaderGroupColAndRowSpan = () => {\n    if (moveNext) {\n      // find the parent at the same row level\n      const nextColumn = columns[nextIdx];\n      let parent = nextColumn.parent;\n      while (parent !== undefined) {\n        const parentRowIdx = getParentRowIdx(parent);\n        if (nextRowIdx === parentRowIdx) {\n          nextIdx = parent.idx + parent.colSpan;\n          break;\n        }\n        parent = parent.parent;\n      }\n    } else if (moveUp) {\n      // find the first reachable parent\n      const nextColumn = columns[nextIdx];\n      let parent = nextColumn.parent;\n      let found = false;\n      while (parent !== undefined) {\n        const parentRowIdx = getParentRowIdx(parent);\n        if (nextRowIdx >= parentRowIdx) {\n          nextIdx = parent.idx;\n          nextRowIdx = parentRowIdx;\n          found = true;\n          break;\n        }\n        parent = parent.parent;\n      }\n\n      // keep the current position if there is no parent matching the new row position\n      if (!found) {\n        nextIdx = currentIdx;\n        nextRowIdx = currentRowIdx;\n      }\n    }\n  };\n\n  if (isCellWithinBounds(nextPosition)) {\n    setColSpan(moveNext);\n\n    if (nextRowIdx < mainHeaderRowIdx) {\n      setHeaderGroupColAndRowSpan();\n    }\n  }\n\n  if (cellNavigationMode === 'CHANGE_ROW') {\n    const isAfterLastColumn = nextIdx === columnsCount;\n    const isBeforeFirstColumn = nextIdx === -1;\n\n    if (isAfterLastColumn) {\n      const isLastRow = nextRowIdx === maxRowIdx;\n      if (!isLastRow) {\n        nextIdx = 0;\n        nextRowIdx += 1;\n      }\n    } else if (isBeforeFirstColumn) {\n      const isFirstRow = nextRowIdx === minRowIdx;\n      if (!isFirstRow) {\n        nextRowIdx -= 1;\n        nextIdx = columnsCount - 1;\n      }\n      setColSpan(false);\n    }\n  }\n\n  if (nextRowIdx < mainHeaderRowIdx && nextIdx > -1 && nextIdx < columnsCount) {\n    // Find the last reachable parent for the new rowIdx\n    // This check is needed when navigating to a column\n    // that does not have a parent matching the new rowIdx\n    const nextColumn = columns[nextIdx];\n    let parent = nextColumn.parent;\n    const nextParentRowIdx = nextRowIdx;\n    nextRowIdx = mainHeaderRowIdx;\n    while (parent !== undefined) {\n      const parentRowIdx = getParentRowIdx(parent);\n      if (parentRowIdx >= nextParentRowIdx) {\n        nextRowIdx = parentRowIdx;\n        nextIdx = parent.idx;\n      }\n      parent = parent.parent;\n    }\n  }\n\n  return { idx: nextIdx, rowIdx: nextRowIdx };\n}\n\ninterface CanExitGridOpts {\n  maxColIdx: number;\n  minRowIdx: number;\n  maxRowIdx: number;\n  selectedPosition: Position;\n  shiftKey: boolean;\n}\n\nexport function canExitGrid({\n  maxColIdx,\n  minRowIdx,\n  maxRowIdx,\n  selectedPosition: { rowIdx, idx },\n  shiftKey\n}: CanExitGridOpts): boolean {\n  // Exit the grid if we're at the first or last cell of the grid\n  const atLastCellInRow = idx === maxColIdx;\n  const atFirstCellInRow = idx === 0;\n  const atLastRow = rowIdx === maxRowIdx;\n  const atFirstRow = rowIdx === minRowIdx;\n\n  return shiftKey ? atFirstCellInRow && atFirstRow : atLastCellInRow && atLastRow;\n}\n", "import { css } from '@linaria/core';\n\nexport const cell = css`\n  @layer rdg.Cell {\n    /* max-content does not work with size containment\n     * dynamically switching between different containment styles incurs a heavy relayout penalty\n     * Chromium bug: at odd zoom levels or subpixel positioning,\n     * layout/paint/style containment can make cell borders disappear\n     *   https://issues.chromium.org/issues/40840864\n     */\n    position: relative; /* needed for absolute positioning to work */\n    padding-block: 0;\n    padding-inline: 8px;\n    border-inline-end: 1px solid var(--rdg-border-color);\n    border-block-end: 1px solid var(--rdg-border-color);\n    grid-row-start: var(--rdg-grid-row-start);\n    align-content: center;\n    background-color: inherit;\n\n    white-space: nowrap;\n    overflow: clip;\n    text-overflow: ellipsis;\n    outline: none;\n\n    &[aria-selected='true'] {\n      outline: 2px solid var(--rdg-selection-color);\n      outline-offset: -2px;\n    }\n  }\n`;\n\nexport const cellClassname = `rdg-cell ${cell}`;\n\nexport const cellFrozen = css`\n  @layer rdg.Cell {\n    position: sticky;\n    /* Should have a higher value than 0 to show up above unfrozen cells */\n    z-index: 1;\n\n    /* Add box-shadow on the last frozen cell */\n    &:nth-last-child(1 of &) {\n      box-shadow: var(--rdg-cell-frozen-box-shadow);\n    }\n  }\n`;\n\nexport const cellFrozenClassname = `rdg-cell-frozen ${cellFrozen}`;\n\nconst cellDragHandle = css`\n  @layer rdg.DragHandle {\n    --rdg-drag-handle-size: 8px;\n    z-index: 0;\n    cursor: move;\n    inline-size: var(--rdg-drag-handle-size);\n    block-size: var(--rdg-drag-handle-size);\n    background-color: var(--rdg-selection-color);\n    place-self: end;\n\n    &:hover {\n      --rdg-drag-handle-size: 16px;\n      border: 2px solid var(--rdg-selection-color);\n      background-color: var(--rdg-background-color);\n    }\n  }\n`;\n\nexport const cellDragHandleFrozenClassname = css`\n  @layer rdg.DragHandle {\n    z-index: 1;\n    position: sticky;\n  }\n`;\n\nexport const cellDragHandleClassname = `rdg-cell-drag-handle ${cellDragHandle}`;\n", "import type { CSSProperties } from 'react';\nimport clsx from 'clsx';\n\nimport type { CalculatedColumn, CalculatedColumnOrColumnGroup } from '../types';\nimport { cellClassname, cellFrozenClassname } from '../style/cell';\n\nexport function getRowStyle(rowIdx: number): CSSProperties {\n  return { '--rdg-grid-row-start': rowIdx } as unknown as CSSProperties;\n}\n\nexport function getHeaderCellStyle<R, SR>(\n  column: CalculatedColumnOrColumnGroup<R, SR>,\n  rowIdx: number,\n  rowSpan: number\n): React.CSSProperties {\n  const gridRowEnd = rowIdx + 1;\n  const paddingBlockStart = `calc(${rowSpan - 1} * var(--rdg-header-row-height))`;\n\n  if (column.parent === undefined) {\n    return {\n      insetBlockStart: 0,\n      gridRowStart: 1,\n      gridRowEnd,\n      paddingBlockStart\n    };\n  }\n\n  return {\n    insetBlockStart: `calc(${rowIdx - rowSpan} * var(--rdg-header-row-height))`,\n    gridRowStart: gridRowEnd - rowSpan,\n    gridRowEnd,\n    paddingBlockStart\n  };\n}\n\nexport function getCellStyle<R, SR>(\n  column: CalculatedColumn<R, SR>,\n  colSpan = 1\n): React.CSSProperties {\n  const index = column.idx + 1;\n  return {\n    gridColumnStart: index,\n    gridColumnEnd: index + colSpan,\n    insetInlineStart: column.frozen ? `var(--rdg-frozen-left-${column.idx})` : undefined\n  };\n}\n\nexport function getCellClassname<R, SR>(\n  column: CalculatedColumn<R, SR>,\n  ...extraClasses: Parameters<typeof clsx>\n): string {\n  return clsx(\n    cellClassname,\n    {\n      [cellFrozenClassname]: column.frozen\n    },\n    ...extraClasses\n  );\n}\n", "import type { CalculatedColumn, CalculatedColumnOrColumnGroup, Maybe } from '../types';\n\nexport * from './colSpanUtils';\nexport * from './domUtils';\nexport * from './eventUtils';\nexport * from './keyboardUtils';\nexport * from './renderMeasuringCells';\nexport * from './selectedCellUtils';\nexport * from './styleUtils';\n\nexport const { min, max, floor, sign, abs } = Math;\n\nexport function assertIsValidKeyGetter<R, K extends React.Key>(\n  keyGetter: Maybe<(row: NoInfer<R>) => K>\n): asserts keyGetter is (row: R) => K {\n  if (typeof keyGetter !== 'function') {\n    throw new Error('Please specify the rowKeyGetter prop to use selection');\n  }\n}\n\nexport function clampColumnWidth<R, SR>(\n  width: number,\n  { minWidth, maxWidth }: CalculatedColumn<R, SR>\n): number {\n  width = max(width, minWidth);\n\n  // ignore maxWidth if it less than minWidth\n  if (typeof maxWidth === 'number' && maxWidth >= minWidth) {\n    return min(width, maxWidth);\n  }\n\n  return width;\n}\n\nexport function getHeaderCellRowSpan<R, SR>(\n  column: CalculatedColumnOrColumnGroup<R, SR>,\n  rowIdx: number\n) {\n  return column.parent === undefined ? rowIdx : column.level - column.parent.level;\n}\n", "import { css } from '@linaria/core';\n\nimport type { RenderCheckboxProps } from '../types';\n\nconst checkbox = css`\n  @layer rdg.CheckboxInput {\n    display: block;\n    margin: auto;\n    inline-size: 20px;\n    block-size: 20px;\n\n    &:focus-visible {\n      outline: 2px solid var(--rdg-checkbox-focus-color);\n      outline-offset: -3px;\n    }\n\n    &:enabled {\n      cursor: pointer;\n    }\n  }\n`;\n\nconst checkboxClassname = `rdg-checkbox-input ${checkbox}`;\n\nexport function renderCheckbox({ onChange, indeterminate, ...props }: RenderCheckboxProps) {\n  function handleChange(e: React.ChangeEvent<HTMLInputElement>) {\n    onChange(e.target.checked, (e.nativeEvent as MouseEvent).shiftKey);\n  }\n\n  return (\n    <input\n      ref={(el) => {\n        if (el) {\n          el.indeterminate = indeterminate === true;\n        }\n      }}\n      type=\"checkbox\"\n      className={checkboxClassname}\n      onChange={handleChange}\n      {...props}\n    />\n  );\n}\n", "import { css } from '@linaria/core';\n\nimport type { RenderGroupCellProps } from '../types';\n\nconst groupCellContent = css`\n  @layer rdg.GroupCellContent {\n    outline: none;\n  }\n`;\n\nconst groupCellContentClassname = `rdg-group-cell-content ${groupCellContent}`;\n\nconst caret = css`\n  @layer rdg.GroupCellCaret {\n    margin-inline-start: 4px;\n    stroke: currentColor;\n    stroke-width: 1.5px;\n    fill: transparent;\n    vertical-align: middle;\n\n    > path {\n      transition: d 0.1s;\n    }\n  }\n`;\n\nconst caretClassname = `rdg-caret ${caret}`;\n\nexport function renderToggleGroup<R, SR>(props: RenderGroupCellProps<R, SR>) {\n  return <ToggleGroup {...props} />;\n}\n\nexport function ToggleGroup<R, SR>({\n  groupKey,\n  isExpanded,\n  tabIndex,\n  toggleGroup\n}: RenderGroupCellProps<R, SR>) {\n  function handleKeyDown({ key }: React.KeyboardEvent<HTMLSpanElement>) {\n    if (key === 'Enter') {\n      toggleGroup();\n    }\n  }\n\n  const d = isExpanded ? 'M1 1 L 7 7 L 13 1' : 'M1 7 L 7 1 L 13 7';\n\n  return (\n    <span className={groupCellContentClassname} tabIndex={tabIndex} onKeyDown={handleKeyDown}>\n      {groupKey as string}\n      <svg viewBox=\"0 0 14 8\" width=\"14\" height=\"8\" className={caretClassname} aria-hidden>\n        <path d={d} />\n      </svg>\n    </span>\n  );\n}\n", "import type { RenderCellProps } from '../types';\n\nexport function renderValue<R, SR>(props: RenderCellProps<R, SR>) {\n  try {\n    return props.row[props.column.key as keyof R] as React.ReactNode;\n  } catch {\n    return null;\n  }\n}\n", "import { createContext, useContext } from 'react';\n\nimport type { Maybe, Renderers } from './types';\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport const DataGridDefaultRenderersContext = createContext<Maybe<Renderers<any, any>>>(undefined);\n\nexport function useDefaultRenderers<R, SR>(): Maybe<Renderers<R, SR>> {\n  return useContext(DataGridDefaultRenderersContext);\n}\n", "import type { RenderCheckboxProps } from '../types';\nimport { useDefaultRenderers } from '../DataGridDefaultRenderersContext';\n\ntype SharedInputProps = Pick<\n  RenderCheckboxProps,\n  'disabled' | 'tabIndex' | 'aria-label' | 'aria-labelledby' | 'indeterminate' | 'onChange'\n>;\n\ninterface SelectCellFormatterProps extends SharedInputProps {\n  value: boolean;\n}\n\nexport function SelectCellFormatter({\n  value,\n  tabIndex,\n  indeterminate,\n  disabled,\n  onChange,\n  'aria-label': ariaLabel,\n  'aria-labelledby': ariaLabelledBy\n}: SelectCellFormatterProps) {\n  const renderCheckbox = useDefaultRenderers()!.renderCheckbox!;\n\n  return renderCheckbox({\n    'aria-label': ariaLabel,\n    'aria-labelledby': ariaLabelledBy,\n    tabIndex,\n    indeterminate,\n    disabled,\n    checked: value,\n    onChange\n  });\n}\n", "import { createContext, useContext } from 'react';\n\nimport type { SelectHeaderRowEvent, SelectRowEvent } from '../types';\n\nexport interface RowSelectionContextValue {\n  readonly isRowSelected: boolean;\n  readonly isRowSelectionDisabled: boolean;\n}\n\nexport const RowSelectionContext = createContext<RowSelectionContextValue | undefined>(undefined);\n\nexport const RowSelectionChangeContext = createContext<\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  ((selectRowEvent: SelectRowEvent<any>) => void) | undefined\n>(undefined);\n\nexport function useRowSelection() {\n  const rowSelectionContext = useContext(RowSelectionContext);\n  const rowSelectionChangeContext = useContext(RowSelectionChangeContext);\n\n  if (rowSelectionContext === undefined || rowSelectionChangeContext === undefined) {\n    throw new Error('useRowSelection must be used within renderCell');\n  }\n\n  return {\n    isRowSelectionDisabled: rowSelectionContext.isRowSelectionDisabled,\n    isRowSelected: rowSelectionContext.isRowSelected,\n    onRowSelectionChange: rowSelectionChangeContext\n  };\n}\n\nexport interface HeaderRowSelectionContextValue {\n  readonly isRowSelected: boolean;\n  readonly isIndeterminate: boolean;\n}\n\nexport const HeaderRowSelectionContext = createContext<HeaderRowSelectionContextValue | undefined>(\n  undefined\n);\n\nexport const HeaderRowSelectionChangeContext = createContext<\n  ((selectRowEvent: SelectHeaderRowEvent) => void) | undefined\n>(undefined);\n\nexport function useHeaderRowSelection() {\n  const headerRowSelectionContext = useContext(HeaderRowSelectionContext);\n  const headerRowSelectionChangeContext = useContext(HeaderRowSelectionChangeContext);\n\n  if (headerRowSelectionContext === undefined || headerRowSelectionChangeContext === undefined) {\n    throw new Error('useHeaderRowSelection must be used within renderHeaderCell');\n  }\n\n  return {\n    isIndeterminate: headerRowSelectionContext.isIndeterminate,\n    isRowSelected: headerRowSelectionContext.isRowSelected,\n    onRowSelectionChange: headerRowSelectionChangeContext\n  };\n}\n", "import { useHeaderRowSelection, useRowSelection } from './hooks/useRowSelection';\nimport type { Column, RenderCellProps, RenderGroupCellProps, RenderHeaderCellProps } from './types';\nimport { SelectCellFormatter } from './cellRenderers';\n\nexport const SELECT_COLUMN_KEY = 'rdg-select-column';\n\nfunction HeaderRenderer(props: RenderHeaderCellProps<unknown>) {\n  const { isIndeterminate, isRowSelected, onRowSelectionChange } = useHeaderRowSelection();\n\n  return (\n    <SelectCellFormatter\n      aria-label=\"Select All\"\n      tabIndex={props.tabIndex}\n      indeterminate={isIndeterminate}\n      value={isRowSelected}\n      onChange={(checked) => {\n        onRowSelectionChange({ checked: isIndeterminate ? false : checked });\n      }}\n    />\n  );\n}\n\nfunction SelectFormatter(props: RenderCellProps<unknown>) {\n  const { isRowSelectionDisabled, isRowSelected, onRowSelectionChange } = useRowSelection();\n\n  return (\n    <SelectCellFormatter\n      aria-label=\"Select\"\n      tabIndex={props.tabIndex}\n      disabled={isRowSelectionDisabled}\n      value={isRowSelected}\n      onChange={(checked, isShiftClick) => {\n        onRowSelectionChange({ row: props.row, checked, isShiftClick });\n      }}\n    />\n  );\n}\n\nfunction SelectGroupFormatter(props: RenderGroupCellProps<unknown>) {\n  const { isRowSelected, onRowSelectionChange } = useRowSelection();\n\n  return (\n    <SelectCellFormatter\n      aria-label=\"Select Group\"\n      tabIndex={props.tabIndex}\n      value={isRowSelected}\n      onChange={(checked) => {\n        onRowSelectionChange({ row: props.row, checked, isShiftClick: false });\n      }}\n    />\n  );\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport const SelectColumn: Column<any, any> = {\n  key: SELECT_COLUMN_KEY,\n  name: '',\n  width: 35,\n  minWidth: 35,\n  maxWidth: 35,\n  resizable: false,\n  sortable: false,\n  frozen: true,\n  renderHeaderCell(props) {\n    return <HeaderRenderer {...props} />;\n  },\n  renderCell(props) {\n    return <SelectFormatter {...props} />;\n  },\n  renderGroupCell(props) {\n    return <SelectGroupFormatter {...props} />;\n  }\n};\n", "import { css } from '@linaria/core';\n\nimport type { RenderHeaderCellProps } from './types';\nimport { useDefaultRenderers } from './DataGridDefaultRenderersContext';\n\nconst headerSortCellClassname = css`\n  @layer rdg.SortableHeaderCell {\n    display: flex;\n  }\n`;\n\nconst headerSortName = css`\n  @layer rdg.SortableHeaderCellName {\n    flex-grow: 1;\n    overflow: clip;\n    text-overflow: ellipsis;\n  }\n`;\n\nconst headerSortNameClassname = `rdg-header-sort-name ${headerSortName}`;\n\nexport default function renderHeaderCell<R, SR>({\n  column,\n  sortDirection,\n  priority\n}: RenderHeaderCellProps<R, SR>) {\n  if (!column.sortable) return column.name;\n\n  return (\n    <SortableHeaderCell sortDirection={sortDirection} priority={priority}>\n      {column.name}\n    </SortableHeaderCell>\n  );\n}\n\ntype SharedHeaderCellProps<R, SR> = Pick<\n  RenderHeaderCellProps<R, SR>,\n  'sortDirection' | 'priority'\n>;\n\ninterface SortableHeaderCellProps<R, SR> extends SharedHeaderCellProps<R, SR> {\n  children: React.ReactNode;\n}\n\nfunction SortableHeaderCell<R, SR>({\n  sortDirection,\n  priority,\n  children\n}: SortableHeaderCellProps<R, SR>) {\n  const renderSortStatus = useDefaultRenderers<R, SR>()!.renderSortStatus!;\n\n  return (\n    <span className={headerSortCellClassname}>\n      <span className={headerSortNameClassname}>{children}</span>\n      <span>{renderSortStatus({ sortDirection, priority })}</span>\n    </span>\n  );\n}\n", "import { useMemo } from 'react';\n\nimport { clampColumnWidth, max, min } from '../utils';\nimport type { CalculatedColumn, CalculatedColumnParent, ColumnOrColumnGroup, Omit } from '../types';\nimport { renderValue } from '../cellRenderers';\nimport { SELECT_COLUMN_KEY } from '../Columns';\nimport type { DataGridProps } from '../DataGrid';\nimport renderHeaderCell from '../renderHeaderCell';\n\ntype Mutable<T> = {\n  -readonly [P in keyof T]: T[P] extends ReadonlyArray<infer V> ? Mutable<V>[] : T[P];\n};\n\ninterface WithParent<R, SR> {\n  readonly parent: MutableCalculatedColumnParent<R, SR> | undefined;\n}\n\ntype MutableCalculatedColumnParent<R, SR> = Omit<Mutable<CalculatedColumnParent<R, SR>>, 'parent'> &\n  WithParent<R, SR>;\ntype MutableCalculatedColumn<R, SR> = Omit<Mutable<CalculatedColumn<R, SR>>, 'parent'> &\n  WithParent<R, SR>;\n\ninterface ColumnMetric {\n  width: number;\n  left: number;\n}\n\nconst DEFAULT_COLUMN_WIDTH = 'auto';\nconst DEFAULT_COLUMN_MIN_WIDTH = 50;\n\ninterface CalculatedColumnsArgs<R, SR> {\n  rawColumns: readonly ColumnOrColumnGroup<R, SR>[];\n  defaultColumnOptions: DataGridProps<R, SR>['defaultColumnOptions'];\n  viewportWidth: number;\n  scrollLeft: number;\n  getColumnWidth: (column: CalculatedColumn<R, SR>) => string | number;\n  enableVirtualization: boolean;\n}\n\nexport function useCalculatedColumns<R, SR>({\n  rawColumns,\n  defaultColumnOptions,\n  getColumnWidth,\n  viewportWidth,\n  scrollLeft,\n  enableVirtualization\n}: CalculatedColumnsArgs<R, SR>) {\n  const defaultWidth = defaultColumnOptions?.width ?? DEFAULT_COLUMN_WIDTH;\n  const defaultMinWidth = defaultColumnOptions?.minWidth ?? DEFAULT_COLUMN_MIN_WIDTH;\n  const defaultMaxWidth = defaultColumnOptions?.maxWidth ?? undefined;\n  const defaultRenderCell = defaultColumnOptions?.renderCell ?? renderValue;\n  const defaultRenderHeaderCell = defaultColumnOptions?.renderHeaderCell ?? renderHeaderCell;\n  const defaultSortable = defaultColumnOptions?.sortable ?? false;\n  const defaultResizable = defaultColumnOptions?.resizable ?? false;\n  const defaultDraggable = defaultColumnOptions?.draggable ?? false;\n\n  const { columns, colSpanColumns, lastFrozenColumnIndex, headerRowsCount } = useMemo((): {\n    readonly columns: readonly CalculatedColumn<R, SR>[];\n    readonly colSpanColumns: readonly CalculatedColumn<R, SR>[];\n    readonly lastFrozenColumnIndex: number;\n    readonly headerRowsCount: number;\n  } => {\n    let lastFrozenColumnIndex = -1;\n    let headerRowsCount = 1;\n    const columns: MutableCalculatedColumn<R, SR>[] = [];\n\n    collectColumns(rawColumns, 1);\n\n    function collectColumns(\n      rawColumns: readonly ColumnOrColumnGroup<R, SR>[],\n      level: number,\n      parent?: MutableCalculatedColumnParent<R, SR>\n    ) {\n      for (const rawColumn of rawColumns) {\n        if ('children' in rawColumn) {\n          const calculatedColumnParent: MutableCalculatedColumnParent<R, SR> = {\n            name: rawColumn.name,\n            parent,\n            idx: -1,\n            colSpan: 0,\n            level: 0,\n            headerCellClass: rawColumn.headerCellClass\n          };\n\n          collectColumns(rawColumn.children, level + 1, calculatedColumnParent);\n          continue;\n        }\n\n        const frozen = rawColumn.frozen ?? false;\n\n        const column: MutableCalculatedColumn<R, SR> = {\n          ...rawColumn,\n          parent,\n          idx: 0,\n          level: 0,\n          frozen,\n          width: rawColumn.width ?? defaultWidth,\n          minWidth: rawColumn.minWidth ?? defaultMinWidth,\n          maxWidth: rawColumn.maxWidth ?? defaultMaxWidth,\n          sortable: rawColumn.sortable ?? defaultSortable,\n          resizable: rawColumn.resizable ?? defaultResizable,\n          draggable: rawColumn.draggable ?? defaultDraggable,\n          renderCell: rawColumn.renderCell ?? defaultRenderCell,\n          renderHeaderCell: rawColumn.renderHeaderCell ?? defaultRenderHeaderCell\n        };\n\n        columns.push(column);\n\n        if (frozen) {\n          lastFrozenColumnIndex++;\n        }\n\n        if (level > headerRowsCount) {\n          headerRowsCount = level;\n        }\n      }\n    }\n\n    columns.sort(({ key: aKey, frozen: frozenA }, { key: bKey, frozen: frozenB }) => {\n      // Sort select column first:\n      if (aKey === SELECT_COLUMN_KEY) return -1;\n      if (bKey === SELECT_COLUMN_KEY) return 1;\n\n      // Sort frozen columns second:\n      if (frozenA) {\n        if (frozenB) return 0;\n        return -1;\n      }\n      if (frozenB) return 1;\n\n      // TODO: sort columns to keep them grouped if they have a parent\n\n      // Sort other columns last:\n      return 0;\n    });\n\n    const colSpanColumns: CalculatedColumn<R, SR>[] = [];\n    columns.forEach((column, idx) => {\n      column.idx = idx;\n      updateColumnParent(column, idx, 0);\n\n      if (column.colSpan != null) {\n        colSpanColumns.push(column);\n      }\n    });\n\n    return {\n      columns,\n      colSpanColumns,\n      lastFrozenColumnIndex,\n      headerRowsCount\n    };\n  }, [\n    rawColumns,\n    defaultWidth,\n    defaultMinWidth,\n    defaultMaxWidth,\n    defaultRenderCell,\n    defaultRenderHeaderCell,\n    defaultResizable,\n    defaultSortable,\n    defaultDraggable\n  ]);\n\n  const { templateColumns, layoutCssVars, totalFrozenColumnWidth, columnMetrics } = useMemo((): {\n    templateColumns: readonly string[];\n    layoutCssVars: Readonly<Record<string, string>>;\n    totalFrozenColumnWidth: number;\n    columnMetrics: ReadonlyMap<CalculatedColumn<R, SR>, ColumnMetric>;\n  } => {\n    const columnMetrics = new Map<CalculatedColumn<R, SR>, ColumnMetric>();\n    let left = 0;\n    let totalFrozenColumnWidth = 0;\n    const templateColumns: string[] = [];\n\n    for (const column of columns) {\n      let width = getColumnWidth(column);\n\n      if (typeof width === 'number') {\n        width = clampColumnWidth(width, column);\n      } else {\n        // This is a placeholder width so we can continue to use virtualization.\n        // The actual value is set after the column is rendered\n        width = column.minWidth;\n      }\n      templateColumns.push(`${width}px`);\n      columnMetrics.set(column, { width, left });\n      left += width;\n    }\n\n    if (lastFrozenColumnIndex !== -1) {\n      const columnMetric = columnMetrics.get(columns[lastFrozenColumnIndex])!;\n      totalFrozenColumnWidth = columnMetric.left + columnMetric.width;\n    }\n\n    const layoutCssVars: Record<string, string> = {};\n\n    for (let i = 0; i <= lastFrozenColumnIndex; i++) {\n      const column = columns[i];\n      layoutCssVars[`--rdg-frozen-left-${column.idx}`] = `${columnMetrics.get(column)!.left}px`;\n    }\n\n    return { templateColumns, layoutCssVars, totalFrozenColumnWidth, columnMetrics };\n  }, [getColumnWidth, columns, lastFrozenColumnIndex]);\n\n  const [colOverscanStartIdx, colOverscanEndIdx] = useMemo((): [number, number] => {\n    if (!enableVirtualization) {\n      return [0, columns.length - 1];\n    }\n    // get the viewport's left side and right side positions for non-frozen columns\n    const viewportLeft = scrollLeft + totalFrozenColumnWidth;\n    const viewportRight = scrollLeft + viewportWidth;\n    // get first and last non-frozen column indexes\n    const lastColIdx = columns.length - 1;\n    const firstUnfrozenColumnIdx = min(lastFrozenColumnIndex + 1, lastColIdx);\n\n    // skip rendering non-frozen columns if the frozen columns cover the entire viewport\n    if (viewportLeft >= viewportRight) {\n      return [firstUnfrozenColumnIdx, firstUnfrozenColumnIdx];\n    }\n\n    // get the first visible non-frozen column index\n    let colVisibleStartIdx = firstUnfrozenColumnIdx;\n    while (colVisibleStartIdx < lastColIdx) {\n      const { left, width } = columnMetrics.get(columns[colVisibleStartIdx])!;\n      // if the right side of the columnn is beyond the left side of the available viewport,\n      // then it is the first column that's at least partially visible\n      if (left + width > viewportLeft) {\n        break;\n      }\n      colVisibleStartIdx++;\n    }\n\n    // get the last visible non-frozen column index\n    let colVisibleEndIdx = colVisibleStartIdx;\n    while (colVisibleEndIdx < lastColIdx) {\n      const { left, width } = columnMetrics.get(columns[colVisibleEndIdx])!;\n      // if the right side of the column is beyond or equal to the right side of the available viewport,\n      // then it the last column that's at least partially visible, as the previous column's right side is not beyond the viewport.\n      if (left + width >= viewportRight) {\n        break;\n      }\n      colVisibleEndIdx++;\n    }\n\n    const colOverscanStartIdx = max(firstUnfrozenColumnIdx, colVisibleStartIdx - 1);\n    const colOverscanEndIdx = min(lastColIdx, colVisibleEndIdx + 1);\n\n    return [colOverscanStartIdx, colOverscanEndIdx];\n  }, [\n    columnMetrics,\n    columns,\n    lastFrozenColumnIndex,\n    scrollLeft,\n    totalFrozenColumnWidth,\n    viewportWidth,\n    enableVirtualization\n  ]);\n\n  return {\n    columns,\n    colSpanColumns,\n    colOverscanStartIdx,\n    colOverscanEndIdx,\n    templateColumns,\n    layoutCssVars,\n    headerRowsCount,\n    lastFrozenColumnIndex,\n    totalFrozenColumnWidth\n  };\n}\n\nfunction updateColumnParent<R, SR>(\n  column: MutableCalculatedColumn<R, SR> | MutableCalculatedColumnParent<R, SR>,\n  index: number,\n  level: number\n) {\n  if (level < column.level) {\n    column.level = level;\n  }\n\n  if (column.parent !== undefined) {\n    const { parent } = column;\n    if (parent.idx === -1) {\n      parent.idx = index;\n    }\n    parent.colSpan += 1;\n    updateColumnParent(parent, index, level - 1);\n  }\n}\n", "import { useLayoutEffect, useState } from 'react';\nimport { flushSync } from 'react-dom';\n\nimport type { CalculatedColumn, ColumnWidths, ResizedWidth } from '../types';\nimport type { DataGridProps } from '../DataGrid';\n\nexport function useColumnWidths<R, SR>(\n  columns: readonly CalculatedColumn<R, SR>[],\n  viewportColumns: readonly CalculatedColumn<R, SR>[],\n  templateColumns: readonly string[],\n  gridRef: React.RefObject<HTMLDivElement | null>,\n  gridWidth: number,\n  columnWidths: ColumnWidths,\n  onColumnWidthsChange: (columnWidths: ColumnWidths) => void,\n  onColumnResize: DataGridProps<R, SR>['onColumnResize'],\n  setColumnResizing: (isColumnResizing: boolean) => void\n) {\n  const [columnToAutoResize, setColumnToAutoResize] = useState<{\n    readonly key: string;\n    readonly width: ResizedWidth;\n  } | null>(null);\n  const [columnsToMeasureOnResize, setColumnsToMeasureOnResize] =\n    useState<ReadonlySet<string> | null>(null);\n  const [prevGridWidth, setPreviousGridWidth] = useState(gridWidth);\n  const columnsCanFlex: boolean = columns.length === viewportColumns.length;\n  const ignorePreviouslyMeasuredColumnsOnGridWidthChange =\n    // Allow columns to flex again when...\n    columnsCanFlex &&\n    // there is enough space for columns to flex and the grid was resized\n    gridWidth !== prevGridWidth;\n  const newTemplateColumns = [...templateColumns];\n  const columnsToMeasure: string[] = [];\n\n  for (const { key, idx, width } of viewportColumns) {\n    const columnWidth = columnWidths.get(key);\n    if (key === columnToAutoResize?.key) {\n      newTemplateColumns[idx] =\n        columnToAutoResize.width === 'max-content'\n          ? columnToAutoResize.width\n          : `${columnToAutoResize.width}px`;\n      columnsToMeasure.push(key);\n    } else if (\n      typeof width === 'string' &&\n      // If the column is resized by the user, we don't want to measure it again\n      columnWidth?.type !== 'resized' &&\n      (ignorePreviouslyMeasuredColumnsOnGridWidthChange ||\n        columnsToMeasureOnResize?.has(key) === true ||\n        columnWidth === undefined)\n    ) {\n      newTemplateColumns[idx] = width;\n      columnsToMeasure.push(key);\n    }\n  }\n\n  const gridTemplateColumns = newTemplateColumns.join(' ');\n\n  useLayoutEffect(updateMeasuredAndResizedWidths);\n\n  function updateMeasuredAndResizedWidths() {\n    setPreviousGridWidth(gridWidth);\n    if (columnsToMeasure.length === 0) return;\n\n    const newColumnWidths = new Map(columnWidths);\n    let hasChanges = false;\n\n    for (const key of columnsToMeasure) {\n      const measuredWidth = measureColumnWidth(gridRef, key);\n      hasChanges ||= measuredWidth !== columnWidths.get(key)?.width;\n      if (measuredWidth === undefined) {\n        newColumnWidths.delete(key);\n      } else {\n        newColumnWidths.set(key, { type: 'measured', width: measuredWidth });\n      }\n    }\n\n    if (columnToAutoResize !== null) {\n      const resizingKey = columnToAutoResize.key;\n      const oldWidth = columnWidths.get(resizingKey)?.width;\n      const newWidth = measureColumnWidth(gridRef, resizingKey);\n      if (newWidth !== undefined && oldWidth !== newWidth) {\n        hasChanges = true;\n        newColumnWidths.set(resizingKey, {\n          type: 'resized',\n          width: newWidth\n        });\n      }\n      setColumnToAutoResize(null);\n    }\n\n    if (hasChanges) {\n      onColumnWidthsChange(newColumnWidths);\n    }\n  }\n\n  function handleColumnResize(column: CalculatedColumn<R, SR>, nextWidth: ResizedWidth) {\n    const { key: resizingKey } = column;\n\n    flushSync(() => {\n      if (columnsCanFlex) {\n        // remeasure all the columns that can flex and are not resized by the user\n        const columnsToRemeasure = new Set<string>();\n        for (const { key, width } of viewportColumns) {\n          if (\n            resizingKey !== key &&\n            typeof width === 'string' &&\n            columnWidths.get(key)?.type !== 'resized'\n          ) {\n            columnsToRemeasure.add(key);\n          }\n        }\n\n        setColumnsToMeasureOnResize(columnsToRemeasure);\n      }\n\n      setColumnToAutoResize({\n        key: resizingKey,\n        width: nextWidth\n      });\n\n      setColumnResizing(typeof nextWidth === 'number');\n    });\n\n    setColumnsToMeasureOnResize(null);\n\n    if (onColumnResize) {\n      const previousWidth = columnWidths.get(resizingKey)?.width;\n      const newWidth =\n        typeof nextWidth === 'number' ? nextWidth : measureColumnWidth(gridRef, resizingKey);\n      if (newWidth !== undefined && newWidth !== previousWidth) {\n        onColumnResize(column, newWidth);\n      }\n    }\n  }\n\n  return {\n    gridTemplateColumns,\n    handleColumnResize\n  } as const;\n}\n\nfunction measureColumnWidth(gridRef: React.RefObject<HTMLDivElement | null>, key: string) {\n  const selector = `[data-measuring-cell-key=\"${CSS.escape(key)}\"]`;\n  const measuringCell = gridRef.current?.querySelector(selector);\n  return measuringCell?.getBoundingClientRect().width;\n}\n", "import { useLayoutEffect, useRef, useState } from 'react';\nimport { flushSync } from 'react-dom';\n\nexport function useGridDimensions() {\n  const gridRef = useRef<HTMLDivElement>(null);\n  const [inlineSize, setInlineSize] = useState(1);\n  const [blockSize, setBlockSize] = useState(1);\n  const [horizontalScrollbarHeight, setHorizontalScrollbarHeight] = useState(0);\n\n  useLayoutEffect(() => {\n    const { ResizeObserver } = window;\n\n    // don't break in Node.js (SSR), jsdom, and browsers that don't support ResizeObserver\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    if (ResizeObserver == null) return;\n\n    const { clientWidth, clientHeight, offsetWidth, offsetHeight } = gridRef.current!;\n    const { width, height } = gridRef.current!.getBoundingClientRect();\n    const initialHorizontalScrollbarHeight = offsetHeight - clientHeight;\n    const initialWidth = width - offsetWidth + clientWidth;\n    const initialHeight = height - initialHorizontalScrollbarHeight;\n\n    setInlineSize(initialWidth);\n    setBlockSize(initialHeight);\n    setHorizontalScrollbarHeight(initialHorizontalScrollbarHeight);\n\n    const resizeObserver = new ResizeObserver((entries) => {\n      const size = entries[0].contentBoxSize[0];\n      const { clientHeight, offsetHeight } = gridRef.current!;\n\n      // we use flushSync here to avoid flashing scrollbars\n      flushSync(() => {\n        setInlineSize(size.inlineSize);\n        setBlockSize(size.blockSize);\n        setHorizontalScrollbarHeight(offsetHeight - clientHeight);\n      });\n    });\n    resizeObserver.observe(gridRef.current!);\n\n    return () => {\n      resizeObserver.disconnect();\n    };\n  }, []);\n\n  return [gridRef, inlineSize, blockSize, horizontalScrollbarHeight] as const;\n}\n", "import { useCallback, useLayoutEffect, useRef } from 'react';\n\nimport type { Maybe } from '../types';\n\n// https://reactjs.org/docs/hooks-faq.html#what-can-i-do-if-my-effect-dependencies-change-too-often\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function useLatestFunc<T extends Maybe<(...args: any[]) => any>>(fn: T): T {\n  const ref = useRef(fn);\n\n  useLayoutEffect(() => {\n    ref.current = fn;\n  });\n\n  const callbackFn = useCallback((...args: Parameters<NonNullable<T>>) => {\n    ref.current!(...args);\n  }, []);\n\n  // @ts-expect-error\n  return fn ? callbackFn : fn;\n}\n", "import { useState } from 'react';\n\n// https://www.w3.org/TR/wai-aria-practices-1.1/#kbd_roving_tabindex\nexport function useRovingTabIndex(isSelected: boolean) {\n  // https://www.w3.org/TR/wai-aria-practices-1.1/#gridNav_focus\n  const [isChildFocused, setIsChildFocused] = useState(false);\n\n  if (isChildFocused && !isSelected) {\n    setIsChildFocused(false);\n  }\n\n  function onFocus(event: React.FocusEvent<HTMLDivElement>) {\n    const elementToFocus = event.currentTarget.querySelector<Element & HTMLOrSVGElement>(\n      '[tabindex=\"0\"]'\n    );\n\n    // Focus cell content when available instead of the cell itself\n    if (elementToFocus !== null) {\n      elementToFocus.focus({ preventScroll: true });\n      setIsChildFocused(true);\n    }\n  }\n\n  const isFocusable = isSelected && !isChildFocused;\n\n  return {\n    tabIndex: isFocusable ? 0 : -1,\n    childTabIndex: isSelected ? 0 : -1,\n    onFocus: isSelected ? onFocus : undefined\n  };\n}\n", "import { useMemo } from 'react';\n\nimport { getColSpan } from '../utils';\nimport type { CalculatedColumn, Maybe } from '../types';\n\ninterface ViewportColumnsArgs<R, SR> {\n  columns: readonly CalculatedColumn<R, SR>[];\n  colSpanColumns: readonly CalculatedColumn<R, SR>[];\n  rows: readonly R[];\n  topSummaryRows: Maybe<readonly SR[]>;\n  bottomSummaryRows: Maybe<readonly SR[]>;\n  colOverscanStartIdx: number;\n  colOverscanEndIdx: number;\n  lastFrozenColumnIndex: number;\n  rowOverscanStartIdx: number;\n  rowOverscanEndIdx: number;\n}\n\nexport function useViewportColumns<R, SR>({\n  columns,\n  colSpanColumns,\n  rows,\n  topSummaryRows,\n  bottomSummaryRows,\n  colOverscanStartIdx,\n  colOverscanEndIdx,\n  lastFrozenColumnIndex,\n  rowOverscanStartIdx,\n  rowOverscanEndIdx\n}: ViewportColumnsArgs<R, SR>) {\n  // find the column that spans over a column within the visible columns range and adjust colOverscanStartIdx\n  const startIdx = useMemo(() => {\n    if (colOverscanStartIdx === 0) return 0;\n\n    let startIdx = colOverscanStartIdx;\n\n    const updateStartIdx = (colIdx: number, colSpan: number | undefined) => {\n      if (colSpan !== undefined && colIdx + colSpan > colOverscanStartIdx) {\n        // eslint-disable-next-line react-compiler/react-compiler\n        startIdx = colIdx;\n        return true;\n      }\n      return false;\n    };\n\n    for (const column of colSpanColumns) {\n      // check header row\n      const colIdx = column.idx;\n      if (colIdx >= startIdx) break;\n      if (updateStartIdx(colIdx, getColSpan(column, lastFrozenColumnIndex, { type: 'HEADER' }))) {\n        break;\n      }\n\n      // check viewport rows\n      for (let rowIdx = rowOverscanStartIdx; rowIdx <= rowOverscanEndIdx; rowIdx++) {\n        const row = rows[rowIdx];\n        if (\n          updateStartIdx(colIdx, getColSpan(column, lastFrozenColumnIndex, { type: 'ROW', row }))\n        ) {\n          break;\n        }\n      }\n\n      // check summary rows\n      if (topSummaryRows != null) {\n        for (const row of topSummaryRows) {\n          if (\n            updateStartIdx(\n              colIdx,\n              getColSpan(column, lastFrozenColumnIndex, { type: 'SUMMARY', row })\n            )\n          ) {\n            break;\n          }\n        }\n      }\n\n      if (bottomSummaryRows != null) {\n        for (const row of bottomSummaryRows) {\n          if (\n            updateStartIdx(\n              colIdx,\n              getColSpan(column, lastFrozenColumnIndex, { type: 'SUMMARY', row })\n            )\n          ) {\n            break;\n          }\n        }\n      }\n    }\n\n    return startIdx;\n  }, [\n    rowOverscanStartIdx,\n    rowOverscanEndIdx,\n    rows,\n    topSummaryRows,\n    bottomSummaryRows,\n    colOverscanStartIdx,\n    lastFrozenColumnIndex,\n    colSpanColumns\n  ]);\n\n  return useMemo((): readonly CalculatedColumn<R, SR>[] => {\n    const viewportColumns: CalculatedColumn<R, SR>[] = [];\n    for (let colIdx = 0; colIdx <= colOverscanEndIdx; colIdx++) {\n      const column = columns[colIdx];\n\n      if (colIdx < startIdx && !column.frozen) continue;\n      viewportColumns.push(column);\n    }\n\n    return viewportColumns;\n  }, [startIdx, colOverscanEndIdx, columns]);\n}\n", "import { useMemo } from 'react';\n\nimport { floor, max, min } from '../utils';\n\ninterface ViewportRowsArgs<R> {\n  rows: readonly R[];\n  rowHeight: number | ((row: R) => number);\n  clientHeight: number;\n  scrollTop: number;\n  enableVirtualization: boolean;\n}\n\nexport function useViewportRows<R>({\n  rows,\n  rowHeight,\n  clientHeight,\n  scrollTop,\n  enableVirtualization\n}: ViewportRowsArgs<R>) {\n  const { totalRowHeight, gridTemplateRows, getRowTop, getRowHeight, findRowIdx } = useMemo(() => {\n    if (typeof rowHeight === 'number') {\n      return {\n        totalRowHeight: rowHeight * rows.length,\n        gridTemplateRows: ` repeat(${rows.length}, ${rowHeight}px)`,\n        getRowTop: (rowIdx: number) => rowIdx * rowHeight,\n        getRowHeight: () => rowHeight,\n        findRowIdx: (offset: number) => floor(offset / rowHeight)\n      };\n    }\n\n    let totalRowHeight = 0;\n    let gridTemplateRows = ' ';\n    // Calcule the height of all the rows upfront. This can cause performance issues\n    // and we can consider using a similar approach as react-window\n    // https://github.com/bvaughn/react-window/blob/b0a470cc264e9100afcaa1b78ed59d88f7914ad4/src/VariableSizeList.js#L68\n    const rowPositions = rows.map((row) => {\n      const currentRowHeight = rowHeight(row);\n      const position = { top: totalRowHeight, height: currentRowHeight };\n      gridTemplateRows += `${currentRowHeight}px `;\n      totalRowHeight += currentRowHeight;\n      return position;\n    });\n\n    const validateRowIdx = (rowIdx: number) => {\n      return max(0, min(rows.length - 1, rowIdx));\n    };\n\n    return {\n      totalRowHeight,\n      gridTemplateRows,\n      getRowTop: (rowIdx: number) => rowPositions[validateRowIdx(rowIdx)].top,\n      getRowHeight: (rowIdx: number) => rowPositions[validateRowIdx(rowIdx)].height,\n      findRowIdx(offset: number) {\n        let start = 0;\n        let end = rowPositions.length - 1;\n        while (start <= end) {\n          const middle = start + floor((end - start) / 2);\n          const currentOffset = rowPositions[middle].top;\n\n          if (currentOffset === offset) return middle;\n\n          if (currentOffset < offset) {\n            start = middle + 1;\n          } else if (currentOffset > offset) {\n            end = middle - 1;\n          }\n\n          if (start > end) return end;\n        }\n        return 0;\n      }\n    };\n  }, [rowHeight, rows]);\n\n  let rowOverscanStartIdx = 0;\n  let rowOverscanEndIdx = rows.length - 1;\n\n  if (enableVirtualization) {\n    const overscanThreshold = 4;\n    const rowVisibleStartIdx = findRowIdx(scrollTop);\n    const rowVisibleEndIdx = findRowIdx(scrollTop + clientHeight);\n    rowOverscanStartIdx = max(0, rowVisibleStartIdx - overscanThreshold);\n    rowOverscanEndIdx = min(rows.length - 1, rowVisibleEndIdx + overscanThreshold);\n  }\n\n  return {\n    rowOverscanStartIdx,\n    rowOverscanEndIdx,\n    totalRowHeight,\n    gridTemplateRows,\n    getRowTop,\n    getRowHeight,\n    findRowIdx\n  };\n}\n", "import { memo, type MouseEvent } from 'react';\nimport { css } from '@linaria/core';\n\nimport { useRovingTabIndex } from './hooks';\nimport { createCellEvent, getCellClassname, getCellStyle, isCellEditableUtil } from './utils';\nimport type { CellMouseEventHandler, CellRendererProps } from './types';\n\nconst cellDraggedOver = css`\n  @layer rdg.Cell {\n    background-color: #ccccff;\n  }\n`;\n\nconst cellDraggedOverClassname = `rdg-cell-dragged-over ${cellDraggedOver}`;\n\nfunction Cell<R, SR>({\n  column,\n  colSpan,\n  isCellSelected,\n  isDraggedOver,\n  row,\n  rowIdx,\n  className,\n  onMouseDown,\n  onCellMouseDown,\n  onClick,\n  onCellClick,\n  onDoubleClick,\n  onCellDoubleClick,\n  onContextMenu,\n  onCellContextMenu,\n  onRowChange,\n  selectCell,\n  style,\n  ...props\n}: CellRendererProps<R, SR>) {\n  const { tabIndex, childTabIndex, onFocus } = useRovingTabIndex(isCellSelected);\n\n  const { cellClass } = column;\n  className = getCellClassname(\n    column,\n    {\n      [cellDraggedOverClassname]: isDraggedOver\n    },\n    typeof cellClass === 'function' ? cellClass(row) : cellClass,\n    className\n  );\n  const isEditable = isCellEditableUtil(column, row);\n\n  function selectCellWrapper(enableEditor?: boolean) {\n    selectCell({ rowIdx, idx: column.idx }, { enableEditor });\n  }\n\n  function handleMouseEvent(\n    event: React.MouseEvent<HTMLDivElement>,\n    eventHandler?: CellMouseEventHandler<R, SR>\n  ) {\n    let eventHandled = false;\n    if (eventHandler) {\n      const cellEvent = createCellEvent(event);\n      eventHandler({ rowIdx, row, column, selectCell: selectCellWrapper }, cellEvent);\n      eventHandled = cellEvent.isGridDefaultPrevented();\n    }\n    return eventHandled;\n  }\n\n  function handleMouseDown(event: MouseEvent<HTMLDivElement>) {\n    onMouseDown?.(event);\n    if (!handleMouseEvent(event, onCellMouseDown)) {\n      // select cell if the event is not prevented\n      selectCellWrapper();\n    }\n  }\n\n  function handleClick(event: MouseEvent<HTMLDivElement>) {\n    onClick?.(event);\n    handleMouseEvent(event, onCellClick);\n  }\n\n  function handleDoubleClick(event: MouseEvent<HTMLDivElement>) {\n    onDoubleClick?.(event);\n    if (!handleMouseEvent(event, onCellDoubleClick)) {\n      // go into edit mode if the event is not prevented\n      selectCellWrapper(true);\n    }\n  }\n\n  function handleContextMenu(event: MouseEvent<HTMLDivElement>) {\n    onContextMenu?.(event);\n    handleMouseEvent(event, onCellContextMenu);\n  }\n\n  function handleRowChange(newRow: R) {\n    onRowChange(column, newRow);\n  }\n\n  return (\n    <div\n      role=\"gridcell\"\n      aria-colindex={column.idx + 1} // aria-colindex is 1-based\n      aria-colspan={colSpan}\n      aria-selected={isCellSelected}\n      aria-readonly={!isEditable || undefined}\n      tabIndex={tabIndex}\n      className={className}\n      style={{\n        ...getCellStyle(column, colSpan),\n        ...style\n      }}\n      onClick={handleClick}\n      onMouseDown={handleMouseDown}\n      onDoubleClick={handleDoubleClick}\n      onContextMenu={handleContextMenu}\n      onFocus={onFocus}\n      {...props}\n    >\n      {column.renderCell({\n        column,\n        row,\n        rowIdx,\n        isCellEditable: isEditable,\n        tabIndex: childTabIndex,\n        onRowChange: handleRowChange\n      })}\n    </div>\n  );\n}\n\nconst CellComponent = memo(Cell) as <R, SR>(props: CellRendererProps<R, SR>) => React.JSX.Element;\n\nexport default CellComponent;\n\nexport function defaultRenderCell<R, SR>(key: React.Key, props: CellRendererProps<R, SR>) {\n  return <CellComponent key={key} {...props} />;\n}\n", "import { useLayoutEffect, useRef } from 'react';\nimport { css } from '@linaria/core';\n\nimport { useLatestFunc } from './hooks';\nimport { createCellEvent, getCellClassname, getCellStyle, onEditorNavigation } from './utils';\nimport type {\n  CellKeyboardEvent,\n  CellRendererProps,\n  EditCellKeyDownArgs,\n  Maybe,\n  Omit,\n  RenderEditCellProps\n} from './types';\n\ndeclare global {\n  const scheduler: Scheduler | undefined;\n}\n\ninterface Scheduler {\n  readonly postTask?: (\n    callback: () => void,\n    options?: {\n      priority?: 'user-blocking' | 'user-visible' | 'background';\n      signal?: AbortSignal;\n      delay?: number;\n    }\n  ) => Promise<unknown>;\n}\n\n/*\n * To check for outside `mousedown` events, we listen to all `mousedown` events at their birth,\n * i.e. on the window during the capture phase, and at their death, i.e. on the window during the bubble phase.\n *\n * We schedule a check at the birth of the event, cancel the check when the event reaches the \"inside\" container,\n * and trigger the \"outside\" callback when the event bubbles back up to the window.\n *\n * The event can be `stopPropagation()`ed halfway through, so they may not always bubble back up to the window,\n * so an alternative check must be used. The check must happen after the event can reach the \"inside\" container,\n * and not before it run to completion. `postTask`/`requestAnimationFrame` are the best way we know to achieve this.\n * Usually we want click event handlers from parent components to access the latest commited values,\n * so `mousedown` is used instead of `click`.\n *\n * We must also rely on React's event capturing/bubbling to handle elements rendered in a portal.\n */\n\nconst canUsePostTask = typeof scheduler === 'object' && typeof scheduler.postTask === 'function';\n\nconst cellEditing = css`\n  @layer rdg.EditCell {\n    padding: 0;\n  }\n`;\n\ntype SharedCellRendererProps<R, SR> = Pick<CellRendererProps<R, SR>, 'colSpan'>;\n\ninterface EditCellProps<R, SR>\n  extends Omit<RenderEditCellProps<R, SR>, 'onRowChange' | 'onClose'>,\n    SharedCellRendererProps<R, SR> {\n  rowIdx: number;\n  onRowChange: (row: R, commitChanges: boolean, shouldFocusCell: boolean) => void;\n  closeEditor: (shouldFocusCell: boolean) => void;\n  navigate: (event: React.KeyboardEvent<HTMLDivElement>) => void;\n  onKeyDown: Maybe<(args: EditCellKeyDownArgs<R, SR>, event: CellKeyboardEvent) => void>;\n}\n\nexport default function EditCell<R, SR>({\n  column,\n  colSpan,\n  row,\n  rowIdx,\n  onRowChange,\n  closeEditor,\n  onKeyDown,\n  navigate\n}: EditCellProps<R, SR>) {\n  const captureEventRef = useRef<MouseEvent | undefined>(undefined);\n  const abortControllerRef = useRef<AbortController>(undefined);\n  const frameRequestRef = useRef<number>(undefined);\n  const commitOnOutsideClick = column.editorOptions?.commitOnOutsideClick ?? true;\n\n  // We need to prevent the `useLayoutEffect` from cleaning up between re-renders,\n  // as `onWindowCaptureMouseDown` might otherwise miss valid mousedown events.\n  // To that end we instead access the latest props via useLatestFunc.\n  const commitOnOutsideMouseDown = useLatestFunc(() => {\n    onClose(true, false);\n  });\n\n  useLayoutEffect(() => {\n    if (!commitOnOutsideClick) return;\n\n    function onWindowCaptureMouseDown(event: MouseEvent) {\n      captureEventRef.current = event;\n\n      if (canUsePostTask) {\n        const abortController = new AbortController();\n        const { signal } = abortController;\n        abortControllerRef.current = abortController;\n        // Use postTask to ensure that the event is not called in the middle of a React render\n        // and that it is called before the next paint.\n        scheduler\n          .postTask(commitOnOutsideMouseDown, {\n            priority: 'user-blocking',\n            signal\n          })\n          // ignore abort errors\n          .catch(() => {});\n      } else {\n        frameRequestRef.current = requestAnimationFrame(commitOnOutsideMouseDown);\n      }\n    }\n\n    function onWindowMouseDown(event: MouseEvent) {\n      if (captureEventRef.current === event) {\n        commitOnOutsideMouseDown();\n      }\n    }\n\n    addEventListener('mousedown', onWindowCaptureMouseDown, { capture: true });\n    addEventListener('mousedown', onWindowMouseDown);\n\n    return () => {\n      removeEventListener('mousedown', onWindowCaptureMouseDown, { capture: true });\n      removeEventListener('mousedown', onWindowMouseDown);\n      cancelTask();\n    };\n  }, [commitOnOutsideClick, commitOnOutsideMouseDown]);\n\n  function cancelTask() {\n    captureEventRef.current = undefined;\n    if (abortControllerRef.current !== undefined) {\n      abortControllerRef.current.abort();\n      abortControllerRef.current = undefined;\n    }\n    if (frameRequestRef.current !== undefined) {\n      cancelAnimationFrame(frameRequestRef.current);\n      frameRequestRef.current = undefined;\n    }\n  }\n\n  function handleKeyDown(event: React.KeyboardEvent<HTMLDivElement>) {\n    if (onKeyDown) {\n      const cellEvent = createCellEvent(event);\n      onKeyDown(\n        {\n          mode: 'EDIT',\n          row,\n          column,\n          rowIdx,\n          navigate() {\n            navigate(event);\n          },\n          onClose\n        },\n        cellEvent\n      );\n      if (cellEvent.isGridDefaultPrevented()) return;\n    }\n\n    if (event.key === 'Escape') {\n      // Discard changes\n      onClose();\n    } else if (event.key === 'Enter') {\n      onClose(true);\n    } else if (onEditorNavigation(event)) {\n      navigate(event);\n    }\n  }\n\n  function onClose(commitChanges = false, shouldFocusCell = true) {\n    if (commitChanges) {\n      onRowChange(row, true, shouldFocusCell);\n    } else {\n      closeEditor(shouldFocusCell);\n    }\n  }\n\n  function onEditorRowChange(row: R, commitChangesAndFocus = false) {\n    onRowChange(row, commitChangesAndFocus, commitChangesAndFocus);\n  }\n\n  const { cellClass } = column;\n  const className = getCellClassname(\n    column,\n    'rdg-editor-container',\n    !column.editorOptions?.displayCellContent && cellEditing,\n    typeof cellClass === 'function' ? cellClass(row) : cellClass\n  );\n\n  return (\n    <div\n      role=\"gridcell\"\n      aria-colindex={column.idx + 1} // aria-colindex is 1-based\n      aria-colspan={colSpan}\n      aria-selected\n      className={className}\n      style={getCellStyle(column, colSpan)}\n      onKeyDown={handleKeyDown}\n      onMouseDownCapture={cancelTask}\n    >\n      {column.renderEditCell != null && (\n        <>\n          {column.renderEditCell({\n            column,\n            row,\n            rowIdx,\n            onRowChange: onEditorRowChange,\n            onClose\n          })}\n          {column.editorOptions?.displayCellContent &&\n            column.renderCell({\n              column,\n              row,\n              rowIdx,\n              isCellEditable: true,\n              tabIndex: -1,\n              onRowChange: onEditorRowChange\n            })}\n        </>\n      )}\n    </div>\n  );\n}\n", "import clsx from 'clsx';\n\nimport { useRovingTabIndex } from './hooks';\nimport { getHeaderCellRowSpan, getHeaderCellStyle } from './utils';\nimport type { CalculatedColumnParent } from './types';\nimport type { GroupedColumnHeaderRowProps } from './GroupedColumnHeaderRow';\nimport { cellClassname } from './style/cell';\n\ntype SharedGroupedColumnHeaderRowProps<R, SR> = Pick<\n  GroupedColumnHeaderRowProps<R, SR>,\n  'rowIdx' | 'selectCell'\n>;\n\ninterface GroupedColumnHeaderCellProps<R, SR> extends SharedGroupedColumnHeaderRowProps<R, SR> {\n  column: CalculatedColumnParent<R, SR>;\n  isCellSelected: boolean;\n}\n\nexport default function GroupedColumnHeaderCell<R, SR>({\n  column,\n  rowIdx,\n  isCellSelected,\n  selectCell\n}: GroupedColumnHeaderCellProps<R, SR>) {\n  const { tabIndex, onFocus } = useRovingTabIndex(isCellSelected);\n  const { colSpan } = column;\n  const rowSpan = getHeaderCellRowSpan(column, rowIdx);\n  const index = column.idx + 1;\n\n  function onMouseDown() {\n    selectCell({ idx: column.idx, rowIdx });\n  }\n\n  return (\n    <div\n      role=\"columnheader\"\n      aria-colindex={index}\n      aria-colspan={colSpan}\n      aria-rowspan={rowSpan}\n      aria-selected={isCellSelected}\n      tabIndex={tabIndex}\n      className={clsx(cellClassname, column.headerCellClass)}\n      style={{\n        ...getHeaderCellStyle(column, rowIdx, rowSpan),\n        gridColumnStart: index,\n        gridColumnEnd: index + colSpan\n      }}\n      onFocus={onFocus}\n      onMouseDown={onMouseDown}\n    >\n      {column.name}\n    </div>\n  );\n}\n", "import { useRef, useState } from 'react';\nimport { flushSync } from 'react-dom';\nimport { css } from '@linaria/core';\n\nimport { useRovingTabIndex } from './hooks';\nimport {\n  clampColumnWidth,\n  getCellClassname,\n  getCellStyle,\n  getHeaderCellRowSpan,\n  getHeaderCellStyle,\n  getLeftRightKey,\n  isCtrlKeyHeldDown,\n  stopPropagation\n} from './utils';\nimport type { CalculatedColumn, SortColumn } from './types';\nimport type { HeaderRowProps } from './HeaderRow';\n\nconst cellSortableClassname = css`\n  @layer rdg.HeaderCell {\n    cursor: pointer;\n  }\n`;\n\nconst cellResizable = css`\n  @layer rdg.HeaderCell {\n    touch-action: none;\n  }\n`;\n\nconst cellResizableClassname = `rdg-cell-resizable ${cellResizable}`;\n\nexport const resizeHandleClassname = css`\n  @layer rdg.HeaderCell {\n    cursor: col-resize;\n    position: absolute;\n    inset-block-start: 0;\n    inset-inline-end: 0;\n    inset-block-end: 0;\n    inline-size: 10px;\n  }\n`;\n\nconst cellDraggableClassname = 'rdg-cell-draggable';\n\nconst cellDragging = css`\n  @layer rdg.HeaderCell {\n    background-color: var(--rdg-header-draggable-background-color);\n  }\n`;\n\nconst cellDraggingClassname = `rdg-cell-dragging ${cellDragging}`;\n\nconst cellOver = css`\n  @layer rdg.HeaderCell {\n    background-color: var(--rdg-header-draggable-background-color);\n  }\n`;\n\nconst cellOverClassname = `rdg-cell-drag-over ${cellOver}`;\n\nconst dragImageClassname = css`\n  @layer rdg.HeaderCell {\n    border-radius: 4px;\n    width: fit-content;\n    outline: 2px solid hsl(207, 100%, 50%);\n    outline-offset: -2px;\n  }\n`;\n\ntype SharedHeaderRowProps<R, SR> = Pick<\n  HeaderRowProps<R, SR, React.Key>,\n  | 'sortColumns'\n  | 'onSortColumnsChange'\n  | 'selectCell'\n  | 'onColumnResize'\n  | 'onColumnResizeEnd'\n  | 'direction'\n  | 'onColumnsReorder'\n>;\n\nexport interface HeaderCellProps<R, SR> extends SharedHeaderRowProps<R, SR> {\n  column: CalculatedColumn<R, SR>;\n  colSpan: number | undefined;\n  rowIdx: number;\n  isCellSelected: boolean;\n  draggedColumnKey: string | undefined;\n  setDraggedColumnKey: (draggedColumnKey: string | undefined) => void;\n}\n\nexport default function HeaderCell<R, SR>({\n  column,\n  colSpan,\n  rowIdx,\n  isCellSelected,\n  onColumnResize,\n  onColumnResizeEnd,\n  onColumnsReorder,\n  sortColumns,\n  onSortColumnsChange,\n  selectCell,\n  direction,\n  draggedColumnKey,\n  setDraggedColumnKey\n}: HeaderCellProps<R, SR>) {\n  const [isOver, setIsOver] = useState(false);\n  const dragImageRef = useRef<HTMLDivElement>(null);\n  const isDragging = draggedColumnKey === column.key;\n  const rowSpan = getHeaderCellRowSpan(column, rowIdx);\n  const { tabIndex, childTabIndex, onFocus } = useRovingTabIndex(isCellSelected);\n  const sortIndex = sortColumns?.findIndex((sort) => sort.columnKey === column.key);\n  const sortColumn =\n    sortIndex !== undefined && sortIndex > -1 ? sortColumns![sortIndex] : undefined;\n  const sortDirection = sortColumn?.direction;\n  const priority = sortColumn !== undefined && sortColumns!.length > 1 ? sortIndex! + 1 : undefined;\n  const ariaSort =\n    sortDirection && !priority ? (sortDirection === 'ASC' ? 'ascending' : 'descending') : undefined;\n  const { sortable, resizable, draggable } = column;\n\n  const className = getCellClassname(column, column.headerCellClass, {\n    [cellSortableClassname]: sortable,\n    [cellResizableClassname]: resizable,\n    [cellDraggableClassname]: draggable,\n    [cellDraggingClassname]: isDragging,\n    [cellOverClassname]: isOver\n  });\n\n  function onSort(ctrlClick: boolean) {\n    if (onSortColumnsChange == null) return;\n    const { sortDescendingFirst } = column;\n    if (sortColumn === undefined) {\n      // not currently sorted\n      const nextSort: SortColumn = {\n        columnKey: column.key,\n        direction: sortDescendingFirst ? 'DESC' : 'ASC'\n      };\n      onSortColumnsChange(sortColumns && ctrlClick ? [...sortColumns, nextSort] : [nextSort]);\n    } else {\n      let nextSortColumn: SortColumn | undefined;\n      if (\n        (sortDescendingFirst === true && sortDirection === 'DESC') ||\n        (sortDescendingFirst !== true && sortDirection === 'ASC')\n      ) {\n        nextSortColumn = {\n          columnKey: column.key,\n          direction: sortDirection === 'ASC' ? 'DESC' : 'ASC'\n        };\n      }\n      if (ctrlClick) {\n        const nextSortColumns = [...sortColumns!];\n        if (nextSortColumn) {\n          // swap direction\n          nextSortColumns[sortIndex!] = nextSortColumn;\n        } else {\n          // remove sort\n          nextSortColumns.splice(sortIndex!, 1);\n        }\n        onSortColumnsChange(nextSortColumns);\n      } else {\n        onSortColumnsChange(nextSortColumn ? [nextSortColumn] : []);\n      }\n    }\n  }\n\n  function onMouseDown() {\n    selectCell({ idx: column.idx, rowIdx });\n  }\n\n  function onClick(event: React.MouseEvent<HTMLSpanElement>) {\n    if (sortable) {\n      onSort(event.ctrlKey || event.metaKey);\n    }\n  }\n\n  function onKeyDown(event: React.KeyboardEvent<HTMLSpanElement>) {\n    const { key } = event;\n    if (sortable && (key === ' ' || key === 'Enter')) {\n      // prevent scrolling\n      event.preventDefault();\n      onSort(event.ctrlKey || event.metaKey);\n    } else if (\n      resizable &&\n      isCtrlKeyHeldDown(event) &&\n      (key === 'ArrowLeft' || key === 'ArrowRight')\n    ) {\n      // prevent navigation\n      // TODO: check if we can use `preventDefault` instead\n      event.stopPropagation();\n      const { width } = event.currentTarget.getBoundingClientRect();\n      const { leftKey } = getLeftRightKey(direction);\n      const offset = key === leftKey ? -10 : 10;\n      const newWidth = clampColumnWidth(width + offset, column);\n      if (newWidth !== width) {\n        onColumnResize(column, newWidth);\n      }\n    }\n  }\n\n  function onDragStart(event: React.DragEvent<HTMLDivElement>) {\n    // need flushSync to make sure the drag image is rendered before the drag starts\n    flushSync(() => {\n      setDraggedColumnKey(column.key);\n    });\n    event.dataTransfer.setDragImage(dragImageRef.current!, 0, 0);\n    event.dataTransfer.dropEffect = 'move';\n  }\n\n  function onDragEnd() {\n    setDraggedColumnKey(undefined);\n  }\n\n  function onDragOver(event: React.DragEvent<HTMLDivElement>) {\n    // prevent default to allow drop\n    event.preventDefault();\n    event.dataTransfer.dropEffect = 'move';\n  }\n\n  function onDrop(event: React.DragEvent<HTMLDivElement>) {\n    setIsOver(false);\n    // prevent the browser from redirecting in some cases\n    event.preventDefault();\n    onColumnsReorder?.(draggedColumnKey!, column.key);\n  }\n\n  function onDragEnter(event: React.DragEvent<HTMLDivElement>) {\n    if (isEventPertinent(event)) {\n      setIsOver(true);\n    }\n  }\n\n  function onDragLeave(event: React.DragEvent<HTMLDivElement>) {\n    if (isEventPertinent(event)) {\n      setIsOver(false);\n    }\n  }\n\n  let dragTargetProps: React.ComponentProps<'div'> | undefined;\n  let dropTargetProps: React.ComponentProps<'div'> | undefined;\n  if (draggable) {\n    dragTargetProps = {\n      draggable: true,\n      onDragStart,\n      onDragEnd\n    };\n\n    if (draggedColumnKey !== undefined && draggedColumnKey !== column.key) {\n      dropTargetProps = {\n        onDragOver,\n        onDragEnter,\n        onDragLeave,\n        onDrop\n      };\n    }\n  }\n\n  const style: React.CSSProperties = {\n    ...getHeaderCellStyle(column, rowIdx, rowSpan),\n    ...getCellStyle(column, colSpan)\n  };\n\n  const content = column.renderHeaderCell({\n    column,\n    sortDirection,\n    priority,\n    tabIndex: childTabIndex\n  });\n\n  return (\n    <>\n      {isDragging && (\n        <div\n          ref={dragImageRef}\n          style={style}\n          className={getCellClassname(column, column.headerCellClass, dragImageClassname)}\n        >\n          {content}\n        </div>\n      )}\n      <div\n        role=\"columnheader\"\n        aria-colindex={column.idx + 1}\n        aria-colspan={colSpan}\n        aria-rowspan={rowSpan}\n        aria-selected={isCellSelected}\n        aria-sort={ariaSort}\n        tabIndex={tabIndex}\n        className={className}\n        style={style}\n        onMouseDown={onMouseDown}\n        onFocus={onFocus}\n        onClick={onClick}\n        onKeyDown={onKeyDown}\n        {...dragTargetProps}\n        {...dropTargetProps}\n      >\n        {content}\n\n        {resizable && (\n          <ResizeHandle\n            direction={direction}\n            column={column}\n            onColumnResize={onColumnResize}\n            onColumnResizeEnd={onColumnResizeEnd}\n          />\n        )}\n      </div>\n    </>\n  );\n}\n\ntype ResizeHandleProps<R, SR> = Pick<\n  HeaderCellProps<R, SR>,\n  'direction' | 'column' | 'onColumnResize' | 'onColumnResizeEnd'\n>;\n\nfunction ResizeHandle<R, SR>({\n  direction,\n  column,\n  onColumnResize,\n  onColumnResizeEnd\n}: ResizeHandleProps<R, SR>) {\n  const resizingOffsetRef = useRef<number>(undefined);\n  const isRtl = direction === 'rtl';\n\n  function onPointerDown(event: React.PointerEvent<HTMLDivElement>) {\n    if (event.pointerType === 'mouse' && event.buttons !== 1) {\n      return;\n    }\n\n    // Fix column resizing on a draggable column in FF\n    event.preventDefault();\n\n    const { currentTarget, pointerId } = event;\n    currentTarget.setPointerCapture(pointerId);\n    const headerCell = currentTarget.parentElement!;\n    const { right, left } = headerCell.getBoundingClientRect();\n    resizingOffsetRef.current = isRtl ? event.clientX - left : right - event.clientX;\n  }\n\n  function onPointerMove(event: React.PointerEvent<HTMLDivElement>) {\n    const offset = resizingOffsetRef.current;\n    if (offset === undefined) return;\n    const { width, right, left } = event.currentTarget.parentElement!.getBoundingClientRect();\n    let newWidth = isRtl ? right + offset - event.clientX : event.clientX + offset - left;\n    newWidth = clampColumnWidth(newWidth, column);\n    if (width > 0 && newWidth !== width) {\n      onColumnResize(column, newWidth);\n    }\n  }\n\n  function onLostPointerCapture() {\n    onColumnResizeEnd();\n    resizingOffsetRef.current = undefined;\n  }\n\n  function onDoubleClick() {\n    onColumnResize(column, 'max-content');\n  }\n\n  return (\n    <div\n      className={resizeHandleClassname}\n      onClick={stopPropagation}\n      onPointerDown={onPointerDown}\n      onPointerMove={onPointerMove}\n      // we are not using pointerup because it does not fire in some cases\n      // pointer down -> alt+tab -> pointer up over another window -> pointerup event not fired\n      onLostPointerCapture={onLostPointerCapture}\n      onDoubleClick={onDoubleClick}\n    />\n  );\n}\n\n// only accept pertinent drag events:\n// - ignore drag events going from the container to an element inside the container\n// - ignore drag events going from an element inside the container to the container\nfunction isEventPertinent(event: React.DragEvent) {\n  const relatedTarget = event.relatedTarget as HTMLElement | null;\n\n  return !event.currentTarget.contains(relatedTarget);\n}\n", "import { css } from '@linaria/core';\n\nexport const row = css`\n  @layer rdg.Row {\n    display: contents;\n    background-color: var(--rdg-background-color);\n\n    &:hover {\n      background-color: var(--rdg-row-hover-background-color);\n    }\n\n    &[aria-selected='true'] {\n      background-color: var(--rdg-row-selected-background-color);\n\n      &:hover {\n        background-color: var(--rdg-row-selected-hover-background-color);\n      }\n    }\n  }\n`;\n\nexport const rowClassname = `rdg-row ${row}`;\n\nexport const rowSelected = css`\n  @layer rdg.FocusSink {\n    outline: 2px solid var(--rdg-selection-color);\n    outline-offset: -2px;\n  }\n`;\n\nexport const rowSelectedClassname = 'rdg-row-selected';\n\nexport const rowSelectedWithFrozenCell = css`\n  @layer rdg.FocusSink {\n    &::before {\n      content: '';\n      display: inline-block;\n      block-size: 100%;\n      position: sticky;\n      inset-inline-start: 0;\n      border-inline-start: 2px solid var(--rdg-selection-color);\n    }\n  }\n`;\n\nexport const topSummaryRowClassname = 'rdg-top-summary-row';\n\nexport const bottomSummaryRowClassname = 'rdg-bottom-summary-row';\n", "import { memo, useState } from 'react';\nimport { css } from '@linaria/core';\nimport clsx from 'clsx';\n\nimport { getColSpan } from './utils';\nimport type { CalculatedColumn, Direction, Maybe, Position, ResizedWidth } from './types';\nimport type { DataGridProps } from './DataGrid';\nimport HeaderCell from './HeaderCell';\nimport { cell, cellFrozen } from './style/cell';\nimport { rowSelectedClassname } from './style/row';\n\ntype SharedDataGridProps<R, SR, K extends React.Key> = Pick<\n  DataGridProps<R, SR, K>,\n  'sortColumns' | 'onSortColumnsChange' | 'onColumnsReorder'\n>;\n\nexport interface HeaderRowProps<R, SR, K extends React.Key> extends SharedDataGridProps<R, SR, K> {\n  rowIdx: number;\n  columns: readonly CalculatedColumn<R, SR>[];\n  onColumnResize: (column: CalculatedColumn<R, SR>, width: ResizedWidth) => void;\n  onColumnResizeEnd: () => void;\n  selectCell: (position: Position) => void;\n  lastFrozenColumnIndex: number;\n  selectedCellIdx: number | undefined;\n  direction: Direction;\n  headerRowClass: Maybe<string>;\n}\n\nconst headerRow = css`\n  @layer rdg.HeaderRow {\n    display: contents;\n    background-color: var(--rdg-header-background-color);\n    font-weight: bold;\n\n    & > .${cell} {\n      /* Should have a higher value than 1 to show up above regular cells and the focus sink */\n      z-index: 2;\n      position: sticky;\n    }\n\n    & > .${cellFrozen} {\n      z-index: 3;\n    }\n  }\n`;\n\nexport const headerRowClassname = `rdg-header-row ${headerRow}`;\n\nfunction HeaderRow<R, SR, K extends React.Key>({\n  headerRowClass,\n  rowIdx,\n  columns,\n  onColumnResize,\n  onColumnResizeEnd,\n  onColumnsReorder,\n  sortColumns,\n  onSortColumnsChange,\n  lastFrozenColumnIndex,\n  selectedCellIdx,\n  selectCell,\n  direction\n}: HeaderRowProps<R, SR, K>) {\n  const [draggedColumnKey, setDraggedColumnKey] = useState<string>();\n\n  const cells = [];\n  for (let index = 0; index < columns.length; index++) {\n    const column = columns[index];\n    const colSpan = getColSpan(column, lastFrozenColumnIndex, { type: 'HEADER' });\n    if (colSpan !== undefined) {\n      index += colSpan - 1;\n    }\n\n    cells.push(\n      <HeaderCell<R, SR>\n        key={column.key}\n        column={column}\n        colSpan={colSpan}\n        rowIdx={rowIdx}\n        isCellSelected={selectedCellIdx === column.idx}\n        onColumnResize={onColumnResize}\n        onColumnResizeEnd={onColumnResizeEnd}\n        onColumnsReorder={onColumnsReorder}\n        onSortColumnsChange={onSortColumnsChange}\n        sortColumns={sortColumns}\n        selectCell={selectCell}\n        direction={direction}\n        draggedColumnKey={draggedColumnKey}\n        setDraggedColumnKey={setDraggedColumnKey}\n      />\n    );\n  }\n\n  return (\n    <div\n      role=\"row\"\n      aria-rowindex={rowIdx} // aria-rowindex is 1 based\n      className={clsx(\n        headerRowClassname,\n        {\n          [rowSelectedClassname]: selectedCellIdx === -1\n        },\n        headerRowClass\n      )}\n    >\n      {cells}\n    </div>\n  );\n}\n\nexport default memo(HeaderRow) as <R, SR, K extends React.Key>(\n  props: HeaderRowProps<R, SR, K>\n) => React.JSX.Element;\n", "import { memo } from 'react';\n\nimport type { CalculatedColumn, CalculatedColumnParent, Position } from './types';\nimport GroupedColumnHeaderCell from './GroupedColumnHeaderCell';\nimport { headerRowClassname } from './HeaderRow';\n\nexport interface GroupedColumnHeaderRowProps<R, SR> {\n  rowIdx: number;\n  level: number;\n  columns: readonly CalculatedColumn<R, SR>[];\n  selectCell: (position: Position) => void;\n  selectedCellIdx: number | undefined;\n}\n\nfunction GroupedColumnHeaderRow<R, SR>({\n  rowIdx,\n  level,\n  columns,\n  selectedCellIdx,\n  selectCell\n}: GroupedColumnHeaderRowProps<R, SR>) {\n  const cells = [];\n  const renderedParents = new Set<CalculatedColumnParent<R, SR>>();\n\n  for (const column of columns) {\n    let { parent } = column;\n\n    if (parent === undefined) continue;\n\n    while (parent.level > level) {\n      if (parent.parent === undefined) break;\n      parent = parent.parent;\n    }\n\n    if (parent.level === level && !renderedParents.has(parent)) {\n      renderedParents.add(parent);\n      const { idx } = parent;\n      cells.push(\n        <GroupedColumnHeaderCell<R, SR>\n          key={idx}\n          column={parent}\n          rowIdx={rowIdx}\n          isCellSelected={selectedCellIdx === idx}\n          selectCell={selectCell}\n        />\n      );\n    }\n  }\n\n  return (\n    <div\n      role=\"row\"\n      aria-rowindex={rowIdx} // aria-rowindex is 1 based\n      className={headerRowClassname}\n    >\n      {cells}\n    </div>\n  );\n}\n\nexport default memo(GroupedColumnHeaderRow) as <R, SR>(\n  props: GroupedColumnHeaderRowProps<R, SR>\n) => React.JSX.Element;\n", "import { memo, useMemo } from 'react';\nimport clsx from 'clsx';\n\nimport { RowSelectionContext, useLatestFunc, type RowSelectionContextValue } from './hooks';\nimport { getColSpan, getRowStyle } from './utils';\nimport type { CalculatedColumn, RenderRowProps } from './types';\nimport { useDefaultRenderers } from './DataGridDefaultRenderersContext';\nimport { rowClassname, rowSelectedClassname } from './style/row';\n\nfunction Row<R, SR>({\n  className,\n  rowIdx,\n  gridRowStart,\n  selectedCellIdx,\n  isRowSelectionDisabled,\n  isRowSelected,\n  draggedOverCellIdx,\n  lastFrozenColumnIndex,\n  row,\n  viewportColumns,\n  selectedCellEditor,\n  onCellMouseDown,\n  onCellClick,\n  onCellDoubleClick,\n  onCellContextMenu,\n  rowClass,\n  onRowChange,\n  selectCell,\n  style,\n  ...props\n}: RenderRowProps<R, SR>) {\n  const renderCell = useDefaultRenderers<R, SR>()!.renderCell!;\n\n  const handleRowChange = useLatestFunc((column: CalculatedColumn<R, SR>, newRow: R) => {\n    onRowChange(column, rowIdx, newRow);\n  });\n\n  className = clsx(\n    rowClassname,\n    `rdg-row-${rowIdx % 2 === 0 ? 'even' : 'odd'}`,\n    {\n      [rowSelectedClassname]: selectedCellIdx === -1\n    },\n    rowClass?.(row, rowIdx),\n    className\n  );\n\n  const cells = [];\n\n  for (let index = 0; index < viewportColumns.length; index++) {\n    const column = viewportColumns[index];\n    const { idx } = column;\n    const colSpan = getColSpan(column, lastFrozenColumnIndex, { type: 'ROW', row });\n    if (colSpan !== undefined) {\n      index += colSpan - 1;\n    }\n\n    const isCellSelected = selectedCellIdx === idx;\n\n    if (isCellSelected && selectedCellEditor) {\n      cells.push(selectedCellEditor);\n    } else {\n      cells.push(\n        renderCell(column.key, {\n          column,\n          colSpan,\n          row,\n          rowIdx,\n          isDraggedOver: draggedOverCellIdx === idx,\n          isCellSelected,\n          onCellMouseDown,\n          onCellClick,\n          onCellDoubleClick,\n          onCellContextMenu,\n          onRowChange: handleRowChange,\n          selectCell\n        })\n      );\n    }\n  }\n\n  const selectionValue = useMemo(\n    (): RowSelectionContextValue => ({ isRowSelected, isRowSelectionDisabled }),\n    [isRowSelectionDisabled, isRowSelected]\n  );\n\n  return (\n    <RowSelectionContext value={selectionValue}>\n      <div\n        role=\"row\"\n        className={className}\n        style={{\n          ...getRowStyle(gridRowStart),\n          ...style\n        }}\n        {...props}\n      >\n        {cells}\n      </div>\n    </RowSelectionContext>\n  );\n}\n\nconst RowComponent = memo(Row) as <R, SR>(props: RenderRowProps<R, SR>) => React.JSX.Element;\n\nexport default RowComponent;\n\nexport function defaultRenderRow<R, SR>(key: React.Key, props: RenderRowProps<R, SR>) {\n  return <RowComponent key={key} {...props} />;\n}\n", "import { useLayoutEffect, useRef } from 'react';\n\nimport { scrollIntoView } from './utils';\n\nexport interface PartialPosition {\n  readonly idx?: number | undefined;\n  readonly rowIdx?: number | undefined;\n}\n\nexport default function ScrollToCell({\n  scrollToPosition: { idx, rowIdx },\n  gridRef,\n  setScrollToCellPosition\n}: {\n  scrollToPosition: PartialPosition;\n  gridRef: React.RefObject<HTMLDivElement | null>;\n  setScrollToCellPosition: (cell: null) => void;\n}) {\n  const ref = useRef<HTMLDivElement>(null);\n\n  useLayoutEffect(() => {\n    // scroll until the cell is completely visible\n    // this is needed if the grid has auto-sized columns\n    // setting the behavior to auto so it can be overridden\n    scrollIntoView(ref.current, 'auto');\n  });\n\n  useLayoutEffect(() => {\n    function removeScrollToCell() {\n      setScrollToCellPosition(null);\n    }\n\n    const observer = new IntersectionObserver(removeScrollToCell, {\n      root: gridRef.current!,\n      threshold: 1.0\n    });\n\n    observer.observe(ref.current!);\n\n    return () => {\n      observer.disconnect();\n    };\n  }, [gridRef, setScrollToCellPosition]);\n\n  return (\n    <div\n      ref={ref}\n      style={{\n        gridColumn: idx === undefined ? '1/-1' : idx + 1,\n        gridRow: rowIdx === undefined ? '1/-1' : rowIdx + 2\n      }}\n    />\n  );\n}\n", "import { css } from '@linaria/core';\n\nimport type { RenderSortIconProps, RenderSortPriorityProps, RenderSortStatusProps } from './types';\n\nconst arrow = css`\n  @layer rdg.SortIcon {\n    fill: currentColor;\n\n    > path {\n      transition: d 0.1s;\n    }\n  }\n`;\n\nconst arrowClassname = `rdg-sort-arrow ${arrow}`;\n\nexport default function renderSortStatus({ sortDirection, priority }: RenderSortStatusProps) {\n  return (\n    <>\n      {renderSortIcon({ sortDirection })}\n      {renderSortPriority({ priority })}\n    </>\n  );\n}\n\nexport function renderSortIcon({ sortDirection }: RenderSortIconProps) {\n  if (sortDirection === undefined) return null;\n\n  return (\n    <svg viewBox=\"0 0 12 8\" width=\"12\" height=\"8\" className={arrowClassname} aria-hidden>\n      <path d={sortDirection === 'ASC' ? 'M0 8 6 0 12 8' : 'M0 0 6 8 12 0'} />\n    </svg>\n  );\n}\n\nexport function renderSortPriority({ priority }: RenderSortPriorityProps) {\n  return priority;\n}\n", "import { css } from '@linaria/core';\n\nimport { cell } from './cell';\nimport { bottomSummaryRowClassname, row, topSummaryRowClassname } from './row';\n\nconst lightTheme = `\n  --rdg-color: #000;\n  --rdg-border-color: #ddd;\n  --rdg-summary-border-color: #aaa;\n  --rdg-background-color: hsl(0deg 0% 100%);\n  --rdg-header-background-color: hsl(0deg 0% 97.5%);\n  --rdg-header-draggable-background-color: hsl(0deg 0% 90.5%);\n  --rdg-row-hover-background-color: hsl(0deg 0% 96%);\n  --rdg-row-selected-background-color: hsl(207deg 76% 92%);\n  --rdg-row-selected-hover-background-color: hsl(207deg 76% 88%);\n\n  --rdg-checkbox-focus-color: hsl(207deg 100% 69%);\n`;\n\nconst darkTheme = `\n  --rdg-color: #ddd;\n  --rdg-border-color: #444;\n  --rdg-summary-border-color: #555;\n  --rdg-background-color: hsl(0deg 0% 13%);\n  --rdg-header-background-color: hsl(0deg 0% 10.5%);\n  --rdg-header-draggable-background-color: hsl(0deg 0% 17.5%);\n  --rdg-row-hover-background-color: hsl(0deg 0% 9%);\n  --rdg-row-selected-background-color: hsl(207deg 76% 42%);\n  --rdg-row-selected-hover-background-color: hsl(207deg 76% 38%);\n\n  --rdg-checkbox-focus-color: hsl(207deg 100% 89%);\n`;\n\nconst root = css`\n  @layer rdg.Defaults {\n    *,\n    *::before,\n    *::after {\n      box-sizing: inherit;\n    }\n  }\n\n  @layer rdg.Root {\n    ${lightTheme}\n    --rdg-selection-color: hsl(207, 75%, 66%);\n    --rdg-font-size: 14px;\n    --rdg-cell-frozen-box-shadow: 2px 0 5px -2px rgba(136, 136, 136, 0.3);\n\n    &:dir(rtl) {\n      --rdg-cell-frozen-box-shadow: -2px 0 5px -2px rgba(136, 136, 136, 0.3);\n    }\n\n    display: grid;\n\n    color-scheme: var(--rdg-color-scheme, light dark);\n    accent-color: light-dark(hsl(207deg 100% 29%), hsl(207deg 100% 79%));\n\n    /* https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Positioning/Understanding_z_index/The_stacking_context */\n    /* We set a stacking context so internal elements don't render on top of external elements. */\n    /* size containment is not used as it could break \"width: min-content\" for example, and the grid would infinitely resize on Chromium browsers */\n    contain: content;\n    content-visibility: auto;\n    block-size: 350px;\n    border: 1px solid var(--rdg-border-color);\n    box-sizing: border-box;\n    overflow: auto;\n    background-color: var(--rdg-background-color);\n    color: var(--rdg-color);\n    font-size: var(--rdg-font-size);\n\n    /* needed on Firefox to fix scrollbars */\n    &::before {\n      content: '';\n      grid-column: 1/-1;\n      grid-row: 1/-1;\n    }\n\n    &.rdg-dark {\n      --rdg-color-scheme: dark;\n      ${darkTheme}\n    }\n\n    &.rdg-light {\n      --rdg-color-scheme: light;\n    }\n\n    @media (prefers-color-scheme: dark) {\n      &:not(.rdg-light) {\n        ${darkTheme}\n      }\n    }\n\n    > :nth-last-child(1 of .${topSummaryRowClassname}) {\n      > .${cell} {\n        border-block-end: 2px solid var(--rdg-summary-border-color);\n      }\n    }\n\n    > :nth-child(1 of .${bottomSummaryRowClassname}) {\n      > .${cell} {\n        border-block-start: 2px solid var(--rdg-summary-border-color);\n      }\n    }\n  }\n`;\n\nexport const rootClassname = `rdg ${root}`;\n\nconst viewportDragging = css`\n  @layer rdg.Root {\n    user-select: none;\n\n    & .${row} {\n      cursor: move;\n    }\n  }\n`;\n\nexport const viewportDraggingClassname = `rdg-viewport-dragging ${viewportDragging}`;\n\nexport const focusSinkClassname = css`\n  @layer rdg.FocusSink {\n    grid-column: 1/-1;\n    pointer-events: none;\n    /* Should have a higher value than 1 to show up above regular frozen cells */\n    z-index: 1;\n  }\n`;\n\nexport const focusSinkHeaderAndSummaryClassname = css`\n  @layer rdg.FocusSink {\n    /* Should have a higher value than 3 to show up above header and summary rows */\n    z-index: 3;\n  }\n`;\n", "import { memo } from 'react';\nimport { css } from '@linaria/core';\n\nimport { useRovingTabIndex } from './hooks';\nimport { getCellClassname, getCellStyle } from './utils';\nimport type { CellRendererProps } from './types';\n\nexport const summaryCellClassname = css`\n  @layer rdg.SummaryCell {\n    inset-block-start: var(--rdg-summary-row-top);\n    inset-block-end: var(--rdg-summary-row-bottom);\n  }\n`;\n\ntype SharedCellRendererProps<R, SR> = Pick<\n  CellRendererProps<R, SR>,\n  'rowIdx' | 'column' | 'colSpan' | 'isCellSelected' | 'selectCell'\n>;\n\ninterface SummaryCellProps<R, SR> extends SharedCellRendererProps<R, SR> {\n  row: SR;\n}\n\nfunction SummaryCell<R, SR>({\n  column,\n  colSpan,\n  row,\n  rowIdx,\n  isCellSelected,\n  selectCell\n}: SummaryCellProps<R, SR>) {\n  const { tabIndex, childTabIndex, onFocus } = useRovingTabIndex(isCellSelected);\n  const { summaryCellClass } = column;\n  const className = getCellClassname(\n    column,\n    summaryCellClassname,\n    typeof summaryCellClass === 'function' ? summaryCellClass(row) : summaryCellClass\n  );\n\n  function onMouseDown() {\n    selectCell({ rowIdx, idx: column.idx });\n  }\n\n  return (\n    <div\n      role=\"gridcell\"\n      aria-colindex={column.idx + 1}\n      aria-colspan={colSpan}\n      aria-selected={isCellSelected}\n      tabIndex={tabIndex}\n      className={className}\n      style={getCellStyle(column, colSpan)}\n      onMouseDown={onMouseDown}\n      onFocus={onFocus}\n    >\n      {column.renderSummaryCell?.({ column, row, tabIndex: childTabIndex })}\n    </div>\n  );\n}\n\nexport default memo(SummaryCell) as <R, SR>(props: SummaryCellProps<R, SR>) => React.JSX.Element;\n", "import { memo } from 'react';\nimport { css } from '@linaria/core';\nimport clsx from 'clsx';\n\nimport { getColSpan, getRowStyle } from './utils';\nimport type { RenderRowProps } from './types';\nimport { cell, cellFrozen } from './style/cell';\nimport {\n  bottomSummaryRowClassname,\n  rowClassname,\n  rowSelectedClassname,\n  topSummaryRowClassname\n} from './style/row';\nimport SummaryCell from './SummaryCell';\n\ntype SharedRenderRowProps<R, SR> = Pick<\n  RenderRowProps<R, SR>,\n  'viewportColumns' | 'rowIdx' | 'gridRowStart' | 'selectCell'\n>;\n\ninterface SummaryRowProps<R, SR> extends SharedRenderRowProps<R, SR> {\n  'aria-rowindex': number;\n  row: SR;\n  top: number | undefined;\n  bottom: number | undefined;\n  lastFrozenColumnIndex: number;\n  selectedCellIdx: number | undefined;\n  isTop: boolean;\n}\n\nconst summaryRow = css`\n  @layer rdg.SummaryRow {\n    > .${cell} {\n      position: sticky;\n    }\n  }\n`;\n\nconst topSummaryRow = css`\n  @layer rdg.SummaryRow {\n    > .${cell} {\n      z-index: 2;\n    }\n\n    > .${cellFrozen} {\n      z-index: 3;\n    }\n  }\n`;\n\nconst summaryRowClassname = `rdg-summary-row ${summaryRow}`;\n\nfunction SummaryRow<R, SR>({\n  rowIdx,\n  gridRowStart,\n  row,\n  viewportColumns,\n  top,\n  bottom,\n  lastFrozenColumnIndex,\n  selectedCellIdx,\n  isTop,\n  selectCell,\n  'aria-rowindex': ariaRowIndex\n}: SummaryRowProps<R, SR>) {\n  const cells = [];\n  for (let index = 0; index < viewportColumns.length; index++) {\n    const column = viewportColumns[index];\n    const colSpan = getColSpan(column, lastFrozenColumnIndex, { type: 'SUMMARY', row });\n    if (colSpan !== undefined) {\n      index += colSpan - 1;\n    }\n\n    const isCellSelected = selectedCellIdx === column.idx;\n\n    cells.push(\n      <SummaryCell<R, SR>\n        key={column.key}\n        column={column}\n        colSpan={colSpan}\n        row={row}\n        rowIdx={rowIdx}\n        isCellSelected={isCellSelected}\n        selectCell={selectCell}\n      />\n    );\n  }\n\n  return (\n    <div\n      role=\"row\"\n      aria-rowindex={ariaRowIndex}\n      className={clsx(\n        rowClassname,\n        `rdg-row-${rowIdx % 2 === 0 ? 'even' : 'odd'}`,\n        summaryRowClassname,\n        {\n          [rowSelectedClassname]: selectedCellIdx === -1,\n          [`${topSummaryRowClassname} ${topSummaryRow}`]: isTop,\n          [bottomSummaryRowClassname]: !isTop\n        }\n      )}\n      style={\n        {\n          ...getRowStyle(gridRowStart),\n          '--rdg-summary-row-top': top !== undefined ? `${top}px` : undefined,\n          '--rdg-summary-row-bottom': bottom !== undefined ? `${bottom}px` : undefined\n        } as unknown as React.CSSProperties\n      }\n    >\n      {cells}\n    </div>\n  );\n}\n\nexport default memo(SummaryRow) as <R, SR>(props: SummaryRowProps<R, SR>) => React.JSX.Element;\n", "import {\n  useC<PERSON>back,\n  useImperativeHandle,\n  useLayoutEffect,\n  useMemo,\n  useRef,\n  useState\n} from 'react';\nimport type { Key, KeyboardEvent } from 'react';\nimport { flushSync } from 'react-dom';\nimport clsx from 'clsx';\n\nimport {\n  HeaderRowSelectionChangeContext,\n  HeaderRowSelectionContext,\n  RowSelectionChangeContext,\n  useCalculatedColumns,\n  useColumnWidths,\n  useGridDimensions,\n  useLatestFunc,\n  useViewportColumns,\n  useViewportRows,\n  type HeaderRowSelectionContextValue\n} from './hooks';\nimport {\n  abs,\n  assertIsValidKeyGetter,\n  canExitGrid,\n  createCellEvent,\n  getCellStyle,\n  getColSpan,\n  getLeftRightKey,\n  getNextSelectedCellPosition,\n  isCtrlKeyHeldDown,\n  isDefaultCellInput,\n  isSelectedCellEditable,\n  renderMeasuringCells,\n  scrollIntoView,\n  sign\n} from './utils';\nimport type {\n  CalculatedColumn,\n  CellClipboardEvent,\n  CellCopyArgs,\n  CellKeyboardEvent,\n  CellKeyDownArgs,\n  CellMouseEventHandler,\n  CellNavigationMode,\n  CellPasteArgs,\n  CellSelectArgs,\n  Column,\n  ColumnOrColumnGroup,\n  ColumnWidths,\n  Direction,\n  FillEvent,\n  Maybe,\n  Position,\n  Renderers,\n  RowsChangeData,\n  SelectCellOptions,\n  SelectHeaderRowEvent,\n  SelectRowEvent,\n  SortColumn\n} from './types';\nimport { defaultRenderCell } from './Cell';\nimport { renderCheckbox as defaultRenderCheckbox } from './cellRenderers';\nimport {\n  DataGridDefaultRenderersContext,\n  useDefaultRenderers\n} from './DataGridDefaultRenderersContext';\nimport EditCell from './EditCell';\nimport GroupedColumnHeaderRow from './GroupedColumnHeaderRow';\nimport HeaderRow from './HeaderRow';\nimport { defaultRenderRow } from './Row';\nimport type { PartialPosition } from './ScrollToCell';\nimport ScrollToCell from './ScrollToCell';\nimport { default as defaultRenderSortStatus } from './sortStatus';\nimport { cellDragHandleClassname, cellDragHandleFrozenClassname } from './style/cell';\nimport {\n  focusSinkClassname,\n  focusSinkHeaderAndSummaryClassname,\n  rootClassname,\n  viewportDraggingClassname\n} from './style/core';\nimport { rowSelected, rowSelectedWithFrozenCell } from './style/row';\nimport SummaryRow from './SummaryRow';\n\nexport interface SelectCellState extends Position {\n  readonly mode: 'SELECT';\n}\n\ninterface EditCellState<R> extends Position {\n  readonly mode: 'EDIT';\n  readonly row: R;\n  readonly originalRow: R;\n}\n\nexport type DefaultColumnOptions<R, SR> = Pick<\n  Column<R, SR>,\n  | 'renderCell'\n  | 'renderHeaderCell'\n  | 'width'\n  | 'minWidth'\n  | 'maxWidth'\n  | 'resizable'\n  | 'sortable'\n  | 'draggable'\n>;\n\nexport interface DataGridHandle {\n  element: HTMLDivElement | null;\n  scrollToCell: (position: PartialPosition) => void;\n  selectCell: (position: Position, options?: SelectCellOptions) => void;\n}\n\ntype SharedDivProps = Pick<\n  React.ComponentProps<'div'>,\n  | 'role'\n  | 'aria-label'\n  | 'aria-labelledby'\n  | 'aria-description'\n  | 'aria-describedby'\n  | 'aria-rowcount'\n  | 'className'\n  | 'style'\n>;\n\nexport interface DataGridProps<R, SR = unknown, K extends Key = Key> extends SharedDivProps {\n  ref?: Maybe<React.Ref<DataGridHandle>>;\n  /**\n   * Grid and data Props\n   */\n  /** An array of column definitions */\n  columns: readonly ColumnOrColumnGroup<NoInfer<R>, NoInfer<SR>>[];\n  /** A function called for each rendered row that should return a plain key/value pair object */\n  rows: readonly R[];\n  /** Rows pinned at the top of the grid for summary purposes */\n  topSummaryRows?: Maybe<readonly SR[]>;\n  /** Rows pinned at the bottom of the grid for summary purposes */\n  bottomSummaryRows?: Maybe<readonly SR[]>;\n  /** Function to return a unique key/identifier for each row */\n  rowKeyGetter?: Maybe<(row: NoInfer<R>) => K>;\n  /** Callback triggered when rows are changed */\n  onRowsChange?: Maybe<(rows: NoInfer<R>[], data: RowsChangeData<NoInfer<R>, NoInfer<SR>>) => void>;\n\n  /**\n   * Dimensions props\n   */\n  /**\n   * Height of each row in pixels\n   * @default 35\n   */\n  rowHeight?: Maybe<number | ((row: NoInfer<R>) => number)>;\n  /**\n   * Height of the header row in pixels\n   * @default 35\n   */\n  headerRowHeight?: Maybe<number>;\n  /**\n   * Height of each summary row in pixels\n   * @default 35\n   */\n  summaryRowHeight?: Maybe<number>;\n  /** A map of column widths */\n  columnWidths?: Maybe<ColumnWidths>;\n  /** Callback triggered when column widths change */\n  onColumnWidthsChange?: Maybe<(columnWidths: ColumnWidths) => void>;\n\n  /**\n   * Feature props\n   */\n  /** A set of selected row keys */\n  selectedRows?: Maybe<ReadonlySet<K>>;\n  /** Function to determine if row selection is disabled for a specific row */\n  isRowSelectionDisabled?: Maybe<(row: NoInfer<R>) => boolean>;\n  /** Callback triggered when the selection changes */\n  onSelectedRowsChange?: Maybe<(selectedRows: Set<NoInfer<K>>) => void>;\n  /** An array of sorted columns */\n  sortColumns?: Maybe<readonly SortColumn[]>;\n  /** Callback triggered when sorting changes */\n  onSortColumnsChange?: Maybe<(sortColumns: SortColumn[]) => void>;\n  /** Default options applied to all columns */\n  defaultColumnOptions?: Maybe<DefaultColumnOptions<NoInfer<R>, NoInfer<SR>>>;\n  onFill?: Maybe<(event: FillEvent<NoInfer<R>>) => NoInfer<R>>;\n\n  /**\n   * Event props\n   */\n  /** Callback triggered when a pointer becomes active in a cell */\n  onCellMouseDown?: CellMouseEventHandler<R, SR>;\n  /** Callback triggered when a cell is clicked */\n  onCellClick?: CellMouseEventHandler<R, SR>;\n  /** Callback triggered when a cell is double-clicked */\n  onCellDoubleClick?: CellMouseEventHandler<R, SR>;\n  /** Callback triggered when a cell is right-clicked */\n  onCellContextMenu?: CellMouseEventHandler<R, SR>;\n  /** Callback triggered when a key is pressed in a cell */\n  onCellKeyDown?: Maybe<\n    (args: CellKeyDownArgs<NoInfer<R>, NoInfer<SR>>, event: CellKeyboardEvent) => void\n  >;\n  /** Callback triggered when a cell's content is copied */\n  onCellCopy?: Maybe<\n    (args: CellCopyArgs<NoInfer<R>, NoInfer<SR>>, event: CellClipboardEvent) => void\n  >;\n  /** Callback triggered when content is pasted into a cell */\n  onCellPaste?: Maybe<\n    (args: CellPasteArgs<NoInfer<R>, NoInfer<SR>>, event: CellClipboardEvent) => NoInfer<R>\n  >;\n  /** Function called whenever cell selection is changed */\n  onSelectedCellChange?: Maybe<(args: CellSelectArgs<NoInfer<R>, NoInfer<SR>>) => void>;\n  /** Callback triggered when the grid is scrolled */\n  onScroll?: Maybe<(event: React.UIEvent<HTMLDivElement>) => void>;\n  /** Callback triggered when column is resized */\n  onColumnResize?: Maybe<(column: CalculatedColumn<R, SR>, width: number) => void>;\n  /** Callback triggered when columns are reordered */\n  onColumnsReorder?: Maybe<(sourceColumnKey: string, targetColumnKey: string) => void>;\n\n  /**\n   * Toggles and modes\n   */\n  /** @default true */\n  enableVirtualization?: Maybe<boolean>;\n\n  /**\n   * Miscellaneous\n   */\n  /** Custom renderers for cells, rows, and other components */\n  renderers?: Maybe<Renderers<NoInfer<R>, NoInfer<SR>>>;\n  /** Function to apply custom class names to rows */\n  rowClass?: Maybe<(row: NoInfer<R>, rowIdx: number) => Maybe<string>>;\n  /** Custom class name for the header row */\n  headerRowClass?: Maybe<string>;\n  /**\n   * Text direction of the grid ('ltr' or 'rtl')\n   * @default 'ltr'\n   * */\n  direction?: Maybe<Direction>;\n  'data-testid'?: Maybe<string>;\n  'data-cy'?: Maybe<string>;\n}\n\n/**\n * Main API Component to render a data grid of rows and columns\n *\n * @example\n *\n * <DataGrid columns={columns} rows={rows} />\n */\nexport function DataGrid<R, SR = unknown, K extends Key = Key>(props: DataGridProps<R, SR, K>) {\n  const {\n    ref,\n    // Grid and data Props\n    columns: rawColumns,\n    rows,\n    topSummaryRows,\n    bottomSummaryRows,\n    rowKeyGetter,\n    onRowsChange,\n    // Dimensions props\n    rowHeight: rawRowHeight,\n    headerRowHeight: rawHeaderRowHeight,\n    summaryRowHeight: rawSummaryRowHeight,\n    columnWidths: columnWidthsRaw,\n    onColumnWidthsChange: onColumnWidthsChangeRaw,\n    // Feature props\n    selectedRows,\n    isRowSelectionDisabled,\n    onSelectedRowsChange,\n    sortColumns,\n    onSortColumnsChange,\n    defaultColumnOptions,\n    // Event props\n    onCellMouseDown,\n    onCellClick,\n    onCellDoubleClick,\n    onCellContextMenu,\n    onCellKeyDown,\n    onSelectedCellChange,\n    onScroll,\n    onColumnResize,\n    onColumnsReorder,\n    onFill,\n    onCellCopy,\n    onCellPaste,\n    // Toggles and modes\n    enableVirtualization: rawEnableVirtualization,\n    // Miscellaneous\n    renderers,\n    className,\n    style,\n    rowClass,\n    headerRowClass,\n    direction: rawDirection,\n    // ARIA\n    role: rawRole,\n    'aria-label': ariaLabel,\n    'aria-labelledby': ariaLabelledBy,\n    'aria-description': ariaDescription,\n    'aria-describedby': ariaDescribedBy,\n    'aria-rowcount': rawAriaRowCount,\n    'data-testid': testId,\n    'data-cy': dataCy\n  } = props;\n\n  /**\n   * defaults\n   */\n  const defaultRenderers = useDefaultRenderers<R, SR>();\n  const role = rawRole ?? 'grid';\n  const rowHeight = rawRowHeight ?? 35;\n  const headerRowHeight = rawHeaderRowHeight ?? (typeof rowHeight === 'number' ? rowHeight : 35);\n  const summaryRowHeight = rawSummaryRowHeight ?? (typeof rowHeight === 'number' ? rowHeight : 35);\n  const renderRow = renderers?.renderRow ?? defaultRenderers?.renderRow ?? defaultRenderRow;\n  const renderCell = renderers?.renderCell ?? defaultRenderers?.renderCell ?? defaultRenderCell;\n  const renderSortStatus =\n    renderers?.renderSortStatus ?? defaultRenderers?.renderSortStatus ?? defaultRenderSortStatus;\n  const renderCheckbox =\n    renderers?.renderCheckbox ?? defaultRenderers?.renderCheckbox ?? defaultRenderCheckbox;\n  const noRowsFallback = renderers?.noRowsFallback ?? defaultRenderers?.noRowsFallback;\n  const enableVirtualization = rawEnableVirtualization ?? true;\n  const direction = rawDirection ?? 'ltr';\n\n  /**\n   * states\n   */\n  const [scrollTop, setScrollTop] = useState(0);\n  const [scrollLeft, setScrollLeft] = useState(0);\n  const [columnWidthsInternal, setColumnWidthsInternal] = useState(\n    (): ColumnWidths => columnWidthsRaw ?? new Map()\n  );\n  const [isColumnResizing, setColumnResizing] = useState(false);\n  const [isDragging, setDragging] = useState(false);\n  const [draggedOverRowIdx, setDraggedOverRowIdx] = useState<number | undefined>(undefined);\n  const [scrollToPosition, setScrollToPosition] = useState<PartialPosition | null>(null);\n  const [shouldFocusCell, setShouldFocusCell] = useState(false);\n  const [previousRowIdx, setPreviousRowIdx] = useState(-1);\n\n  const isColumnWidthsControlled =\n    columnWidthsRaw != null && onColumnWidthsChangeRaw != null && !isColumnResizing;\n  const columnWidths = isColumnWidthsControlled ? columnWidthsRaw : columnWidthsInternal;\n  const onColumnWidthsChange = isColumnWidthsControlled\n    ? (columnWidths: ColumnWidths) => {\n        // we keep the internal state in sync with the prop but this prevents an extra render\n        setColumnWidthsInternal(columnWidths);\n        onColumnWidthsChangeRaw(columnWidths);\n      }\n    : setColumnWidthsInternal;\n\n  const getColumnWidth = useCallback(\n    (column: CalculatedColumn<R, SR>) => {\n      return columnWidths.get(column.key)?.width ?? column.width;\n    },\n    [columnWidths]\n  );\n\n  const [gridRef, gridWidth, gridHeight, horizontalScrollbarHeight] = useGridDimensions();\n  const {\n    columns,\n    colSpanColumns,\n    lastFrozenColumnIndex,\n    headerRowsCount,\n    colOverscanStartIdx,\n    colOverscanEndIdx,\n    templateColumns,\n    layoutCssVars,\n    totalFrozenColumnWidth\n  } = useCalculatedColumns({\n    rawColumns,\n    defaultColumnOptions,\n    getColumnWidth,\n    scrollLeft,\n    viewportWidth: gridWidth,\n    enableVirtualization\n  });\n\n  const topSummaryRowsCount = topSummaryRows?.length ?? 0;\n  const bottomSummaryRowsCount = bottomSummaryRows?.length ?? 0;\n  const summaryRowsCount = topSummaryRowsCount + bottomSummaryRowsCount;\n  const headerAndTopSummaryRowsCount = headerRowsCount + topSummaryRowsCount;\n  const groupedColumnHeaderRowsCount = headerRowsCount - 1;\n  const minRowIdx = -headerAndTopSummaryRowsCount;\n  const mainHeaderRowIdx = minRowIdx + groupedColumnHeaderRowsCount;\n  const maxRowIdx = rows.length + bottomSummaryRowsCount - 1;\n\n  const [selectedPosition, setSelectedPosition] = useState(\n    (): SelectCellState | EditCellState<R> => ({ idx: -1, rowIdx: minRowIdx - 1, mode: 'SELECT' })\n  );\n\n  /**\n   * refs\n   */\n  const focusSinkRef = useRef<HTMLDivElement>(null);\n\n  /**\n   * computed values\n   */\n  const isTreeGrid = role === 'treegrid';\n  const headerRowsHeight = headerRowsCount * headerRowHeight;\n  const summaryRowsHeight = summaryRowsCount * summaryRowHeight;\n  const clientHeight = gridHeight - headerRowsHeight - summaryRowsHeight;\n  const isSelectable = selectedRows != null && onSelectedRowsChange != null;\n  const { leftKey, rightKey } = getLeftRightKey(direction);\n  const ariaRowCount = rawAriaRowCount ?? headerRowsCount + rows.length + summaryRowsCount;\n\n  const defaultGridComponents = useMemo(\n    () => ({\n      renderCheckbox,\n      renderSortStatus,\n      renderCell\n    }),\n    [renderCheckbox, renderSortStatus, renderCell]\n  );\n\n  const headerSelectionValue = useMemo((): HeaderRowSelectionContextValue => {\n    // no rows to select = explicitely unchecked\n    let hasSelectedRow = false;\n    let hasUnselectedRow = false;\n\n    if (rowKeyGetter != null && selectedRows != null && selectedRows.size > 0) {\n      for (const row of rows) {\n        if (selectedRows.has(rowKeyGetter(row))) {\n          hasSelectedRow = true;\n        } else {\n          hasUnselectedRow = true;\n        }\n\n        if (hasSelectedRow && hasUnselectedRow) break;\n      }\n    }\n\n    return {\n      isRowSelected: hasSelectedRow && !hasUnselectedRow,\n      isIndeterminate: hasSelectedRow && hasUnselectedRow\n    };\n  }, [rows, selectedRows, rowKeyGetter]);\n\n  const {\n    rowOverscanStartIdx,\n    rowOverscanEndIdx,\n    totalRowHeight,\n    gridTemplateRows,\n    getRowTop,\n    getRowHeight,\n    findRowIdx\n  } = useViewportRows({\n    rows,\n    rowHeight,\n    clientHeight,\n    scrollTop,\n    enableVirtualization\n  });\n\n  const viewportColumns = useViewportColumns({\n    columns,\n    colSpanColumns,\n    colOverscanStartIdx,\n    colOverscanEndIdx,\n    lastFrozenColumnIndex,\n    rowOverscanStartIdx,\n    rowOverscanEndIdx,\n    rows,\n    topSummaryRows,\n    bottomSummaryRows\n  });\n\n  const { gridTemplateColumns, handleColumnResize } = useColumnWidths(\n    columns,\n    viewportColumns,\n    templateColumns,\n    gridRef,\n    gridWidth,\n    columnWidths,\n    onColumnWidthsChange,\n    onColumnResize,\n    setColumnResizing\n  );\n\n  const minColIdx = isTreeGrid ? -1 : 0;\n  const maxColIdx = columns.length - 1;\n  const selectedCellIsWithinSelectionBounds = isCellWithinSelectionBounds(selectedPosition);\n  const selectedCellIsWithinViewportBounds = isCellWithinViewportBounds(selectedPosition);\n  const scrollHeight =\n    headerRowHeight + totalRowHeight + summaryRowsHeight + horizontalScrollbarHeight;\n  const shouldFocusGrid = !selectedCellIsWithinSelectionBounds;\n\n  /**\n   * The identity of the wrapper function is stable so it won't break memoization\n   */\n  const handleColumnResizeLatest = useLatestFunc(handleColumnResize);\n  const handleColumnResizeEndLatest = useLatestFunc(handleColumnResizeEnd);\n  const onColumnsReorderLastest = useLatestFunc(onColumnsReorder);\n  const onSortColumnsChangeLatest = useLatestFunc(onSortColumnsChange);\n  const onCellMouseDownLatest = useLatestFunc(onCellMouseDown);\n  const onCellClickLatest = useLatestFunc(onCellClick);\n  const onCellDoubleClickLatest = useLatestFunc(onCellDoubleClick);\n  const onCellContextMenuLatest = useLatestFunc(onCellContextMenu);\n  const selectHeaderRowLatest = useLatestFunc(selectHeaderRow);\n  const selectRowLatest = useLatestFunc(selectRow);\n  const handleFormatterRowChangeLatest = useLatestFunc(updateRow);\n  const selectCellLatest = useLatestFunc(selectCell);\n  const selectHeaderCellLatest = useLatestFunc(selectHeaderCell);\n\n  /**\n   * callbacks\n   */\n  const focusCell = useCallback(\n    (shouldScroll = true) => {\n      const cell = getCellToScroll(gridRef.current!);\n      if (cell === null) return;\n\n      if (shouldScroll) {\n        scrollIntoView(cell);\n      }\n\n      cell.focus({ preventScroll: true });\n    },\n    [gridRef]\n  );\n\n  /**\n   * effects\n   */\n  useLayoutEffect(() => {\n    if (shouldFocusCell) {\n      if (focusSinkRef.current !== null && selectedPosition.idx === -1) {\n        focusSinkRef.current.focus({ preventScroll: true });\n        scrollIntoView(focusSinkRef.current);\n      } else {\n        focusCell();\n      }\n      setShouldFocusCell(false);\n    }\n  }, [shouldFocusCell, focusCell, selectedPosition.idx]);\n\n  useImperativeHandle(ref, () => ({\n    element: gridRef.current,\n    scrollToCell({ idx, rowIdx }) {\n      const scrollToIdx =\n        idx !== undefined && idx > lastFrozenColumnIndex && idx < columns.length ? idx : undefined;\n      const scrollToRowIdx =\n        rowIdx !== undefined && isRowIdxWithinViewportBounds(rowIdx) ? rowIdx : undefined;\n\n      if (scrollToIdx !== undefined || scrollToRowIdx !== undefined) {\n        setScrollToPosition({ idx: scrollToIdx, rowIdx: scrollToRowIdx });\n      }\n    },\n    selectCell\n  }));\n\n  /**\n   * event handlers\n   */\n  function selectHeaderRow(args: SelectHeaderRowEvent) {\n    if (!onSelectedRowsChange) return;\n\n    assertIsValidKeyGetter<R, K>(rowKeyGetter);\n\n    const newSelectedRows = new Set(selectedRows);\n    for (const row of rows) {\n      if (isRowSelectionDisabled?.(row) === true) continue;\n      const rowKey = rowKeyGetter(row);\n      if (args.checked) {\n        newSelectedRows.add(rowKey);\n      } else {\n        newSelectedRows.delete(rowKey);\n      }\n    }\n    onSelectedRowsChange(newSelectedRows);\n  }\n\n  function selectRow(args: SelectRowEvent<R>) {\n    if (!onSelectedRowsChange) return;\n\n    assertIsValidKeyGetter<R, K>(rowKeyGetter);\n    const { row, checked, isShiftClick } = args;\n    if (isRowSelectionDisabled?.(row) === true) return;\n    const newSelectedRows = new Set(selectedRows);\n    const rowKey = rowKeyGetter(row);\n    const rowIdx = rows.indexOf(row);\n    setPreviousRowIdx(rowIdx);\n\n    if (checked) {\n      newSelectedRows.add(rowKey);\n    } else {\n      newSelectedRows.delete(rowKey);\n    }\n\n    if (\n      isShiftClick &&\n      previousRowIdx !== -1 &&\n      previousRowIdx !== rowIdx &&\n      previousRowIdx < rows.length\n    ) {\n      const step = sign(rowIdx - previousRowIdx);\n      for (let i = previousRowIdx + step; i !== rowIdx; i += step) {\n        const row = rows[i];\n        if (isRowSelectionDisabled?.(row) === true) continue;\n        if (checked) {\n          newSelectedRows.add(rowKeyGetter(row));\n        } else {\n          newSelectedRows.delete(rowKeyGetter(row));\n        }\n      }\n    }\n\n    onSelectedRowsChange(newSelectedRows);\n  }\n\n  function handleKeyDown(event: KeyboardEvent<HTMLDivElement>) {\n    const { idx, rowIdx, mode } = selectedPosition;\n    if (mode === 'EDIT') return;\n\n    if (onCellKeyDown && isRowIdxWithinViewportBounds(rowIdx)) {\n      const row = rows[rowIdx];\n      const cellEvent = createCellEvent(event);\n      onCellKeyDown(\n        {\n          mode: 'SELECT',\n          row,\n          column: columns[idx],\n          rowIdx,\n          selectCell\n        },\n        cellEvent\n      );\n      if (cellEvent.isGridDefaultPrevented()) return;\n    }\n\n    if (!(event.target instanceof Element)) return;\n    const isCellEvent = event.target.closest('.rdg-cell') !== null;\n    const isRowEvent = isTreeGrid && event.target === focusSinkRef.current;\n    if (!isCellEvent && !isRowEvent) return;\n\n    switch (event.key) {\n      case 'ArrowUp':\n      case 'ArrowDown':\n      case 'ArrowLeft':\n      case 'ArrowRight':\n      case 'Tab':\n      case 'Home':\n      case 'End':\n      case 'PageUp':\n      case 'PageDown':\n        navigate(event);\n        break;\n      default:\n        handleCellInput(event);\n        break;\n    }\n  }\n\n  function handleFocus(event: React.FocusEvent<HTMLDivElement>) {\n    // select the first header cell if the focus event is triggered by the grid\n    if (event.target === event.currentTarget) {\n      selectHeaderCell({ idx: minColIdx, rowIdx: headerRowsCount }, { shouldFocusCell: true });\n    }\n  }\n\n  function handleScroll(event: React.UIEvent<HTMLDivElement>) {\n    const { scrollTop, scrollLeft } = event.currentTarget;\n    flushSync(() => {\n      setScrollTop(scrollTop);\n      // scrollLeft is nagative when direction is rtl\n      setScrollLeft(abs(scrollLeft));\n    });\n    onScroll?.(event);\n  }\n\n  function updateRow(column: CalculatedColumn<R, SR>, rowIdx: number, row: R) {\n    if (typeof onRowsChange !== 'function') return;\n    if (row === rows[rowIdx]) return;\n    const updatedRows = rows.with(rowIdx, row);\n    onRowsChange(updatedRows, {\n      indexes: [rowIdx],\n      column\n    });\n  }\n\n  function commitEditorChanges() {\n    if (selectedPosition.mode !== 'EDIT') return;\n    updateRow(columns[selectedPosition.idx], selectedPosition.rowIdx, selectedPosition.row);\n  }\n\n  function handleCellCopy(event: CellClipboardEvent) {\n    if (!selectedCellIsWithinViewportBounds) return;\n    const { idx, rowIdx } = selectedPosition;\n    onCellCopy?.({ row: rows[rowIdx], column: columns[idx] }, event);\n  }\n\n  function handleCellPaste(event: CellClipboardEvent) {\n    if (!onCellPaste || !onRowsChange || !isCellEditable(selectedPosition)) {\n      return;\n    }\n\n    const { idx, rowIdx } = selectedPosition;\n    const column = columns[idx];\n    const updatedRow = onCellPaste({ row: rows[rowIdx], column }, event);\n    updateRow(column, rowIdx, updatedRow);\n  }\n\n  function handleCellInput(event: KeyboardEvent<HTMLDivElement>) {\n    if (!selectedCellIsWithinViewportBounds) return;\n    const row = rows[selectedPosition.rowIdx];\n    const { key, shiftKey } = event;\n\n    // Select the row on Shift + Space\n    if (isSelectable && shiftKey && key === ' ') {\n      assertIsValidKeyGetter<R, K>(rowKeyGetter);\n      const rowKey = rowKeyGetter(row);\n      selectRow({ row, checked: !selectedRows.has(rowKey), isShiftClick: false });\n      // prevent scrolling\n      event.preventDefault();\n      return;\n    }\n\n    if (isCellEditable(selectedPosition) && isDefaultCellInput(event, onCellPaste != null)) {\n      setSelectedPosition(({ idx, rowIdx }) => ({\n        idx,\n        rowIdx,\n        mode: 'EDIT',\n        row,\n        originalRow: row\n      }));\n    }\n  }\n\n  function handleColumnResizeEnd() {\n    // This check is needed as double click on the resize handle triggers onPointerMove\n    if (isColumnResizing) {\n      onColumnWidthsChangeRaw?.(columnWidths);\n      setColumnResizing(false);\n    }\n  }\n\n  function handleDragHandlePointerDown(event: React.PointerEvent<HTMLDivElement>) {\n    // keep the focus on the cell\n    event.preventDefault();\n    if (event.pointerType === 'mouse' && event.buttons !== 1) {\n      return;\n    }\n    setDragging(true);\n    event.currentTarget.setPointerCapture(event.pointerId);\n  }\n\n  function handleDragHandlePointerMove(event: React.PointerEvent<HTMLDivElement>) {\n    // find dragged over row using the pointer position\n    const gridEl = gridRef.current!;\n    const headerAndTopSummaryRowsHeight = headerRowsHeight + topSummaryRowsCount * summaryRowHeight;\n    const offset =\n      scrollTop -\n      headerAndTopSummaryRowsHeight +\n      event.clientY -\n      gridEl.getBoundingClientRect().top;\n    const overRowIdx = findRowIdx(offset);\n    setDraggedOverRowIdx(overRowIdx);\n    const ariaRowIndex = headerAndTopSummaryRowsCount + overRowIdx + 1;\n    const el = gridEl.querySelector(\n      `:scope > [aria-rowindex=\"${ariaRowIndex}\"] > [aria-colindex=\"${selectedPosition.idx + 1}\"]`\n    );\n    scrollIntoView(el);\n  }\n\n  function handleDragHandleLostPointerCapture() {\n    setDragging(false);\n    if (draggedOverRowIdx === undefined) return;\n\n    const { rowIdx } = selectedPosition;\n    const [startRowIndex, endRowIndex] =\n      rowIdx < draggedOverRowIdx\n        ? [rowIdx + 1, draggedOverRowIdx + 1]\n        : [draggedOverRowIdx, rowIdx];\n    updateRows(startRowIndex, endRowIndex);\n    setDraggedOverRowIdx(undefined);\n  }\n\n  function handleDragHandleClick() {\n    // keep the focus on the cell but do not scroll\n    focusCell(false);\n  }\n\n  function handleDragHandleDoubleClick(event: React.MouseEvent<HTMLDivElement>) {\n    event.stopPropagation();\n    updateRows(selectedPosition.rowIdx + 1, rows.length);\n  }\n\n  function updateRows(startRowIdx: number, endRowIdx: number) {\n    if (onRowsChange == null) return;\n\n    const { rowIdx, idx } = selectedPosition;\n    const column = columns[idx];\n    const sourceRow = rows[rowIdx];\n    const updatedRows = [...rows];\n    const indexes: number[] = [];\n    for (let i = startRowIdx; i < endRowIdx; i++) {\n      if (isCellEditable({ rowIdx: i, idx })) {\n        const updatedRow = onFill!({ columnKey: column.key, sourceRow, targetRow: rows[i] });\n        if (updatedRow !== rows[i]) {\n          updatedRows[i] = updatedRow;\n          indexes.push(i);\n        }\n      }\n    }\n\n    if (indexes.length > 0) {\n      onRowsChange(updatedRows, { indexes, column });\n    }\n  }\n\n  /**\n   * utils\n   */\n  function isColIdxWithinSelectionBounds(idx: number) {\n    return idx >= minColIdx && idx <= maxColIdx;\n  }\n\n  function isRowIdxWithinViewportBounds(rowIdx: number) {\n    return rowIdx >= 0 && rowIdx < rows.length;\n  }\n\n  function isCellWithinSelectionBounds({ idx, rowIdx }: Position): boolean {\n    return rowIdx >= minRowIdx && rowIdx <= maxRowIdx && isColIdxWithinSelectionBounds(idx);\n  }\n\n  function isCellWithinEditBounds({ idx, rowIdx }: Position): boolean {\n    return isRowIdxWithinViewportBounds(rowIdx) && idx >= 0 && idx <= maxColIdx;\n  }\n\n  function isCellWithinViewportBounds({ idx, rowIdx }: Position): boolean {\n    return isRowIdxWithinViewportBounds(rowIdx) && isColIdxWithinSelectionBounds(idx);\n  }\n\n  function isCellEditable(position: Position): boolean {\n    return (\n      isCellWithinEditBounds(position) &&\n      isSelectedCellEditable({ columns, rows, selectedPosition: position })\n    );\n  }\n\n  function selectCell(position: Position, options?: SelectCellOptions): void {\n    if (!isCellWithinSelectionBounds(position)) return;\n    commitEditorChanges();\n\n    const samePosition = isSamePosition(selectedPosition, position);\n\n    if (options?.enableEditor && isCellEditable(position)) {\n      const row = rows[position.rowIdx];\n      setSelectedPosition({ ...position, mode: 'EDIT', row, originalRow: row });\n    } else if (samePosition) {\n      // Avoid re-renders if the selected cell state is the same\n      scrollIntoView(getCellToScroll(gridRef.current!));\n    } else {\n      setShouldFocusCell(options?.shouldFocusCell === true);\n      setSelectedPosition({ ...position, mode: 'SELECT' });\n    }\n\n    if (onSelectedCellChange && !samePosition) {\n      onSelectedCellChange({\n        rowIdx: position.rowIdx,\n        row: isRowIdxWithinViewportBounds(position.rowIdx) ? rows[position.rowIdx] : undefined,\n        column: columns[position.idx]\n      });\n    }\n  }\n\n  function selectHeaderCell({ idx, rowIdx }: Position, options?: SelectCellOptions): void {\n    selectCell({ rowIdx: minRowIdx + rowIdx - 1, idx }, options);\n  }\n\n  function getNextPosition(key: string, ctrlKey: boolean, shiftKey: boolean): Position {\n    const { idx, rowIdx } = selectedPosition;\n    const isRowSelected = selectedCellIsWithinSelectionBounds && idx === -1;\n\n    switch (key) {\n      case 'ArrowUp':\n        return { idx, rowIdx: rowIdx - 1 };\n      case 'ArrowDown':\n        return { idx, rowIdx: rowIdx + 1 };\n      case leftKey:\n        return { idx: idx - 1, rowIdx };\n      case rightKey:\n        return { idx: idx + 1, rowIdx };\n      case 'Tab':\n        return { idx: idx + (shiftKey ? -1 : 1), rowIdx };\n      case 'Home':\n        // If row is selected then move focus to the first row\n        if (isRowSelected) return { idx, rowIdx: minRowIdx };\n        return { idx: 0, rowIdx: ctrlKey ? minRowIdx : rowIdx };\n      case 'End':\n        // If row is selected then move focus to the last row.\n        if (isRowSelected) return { idx, rowIdx: maxRowIdx };\n        return { idx: maxColIdx, rowIdx: ctrlKey ? maxRowIdx : rowIdx };\n      case 'PageUp': {\n        if (selectedPosition.rowIdx === minRowIdx) return selectedPosition;\n        const nextRowY = getRowTop(rowIdx) + getRowHeight(rowIdx) - clientHeight;\n        return { idx, rowIdx: nextRowY > 0 ? findRowIdx(nextRowY) : 0 };\n      }\n      case 'PageDown': {\n        if (selectedPosition.rowIdx >= rows.length) return selectedPosition;\n        const nextRowY = getRowTop(rowIdx) + clientHeight;\n        return { idx, rowIdx: nextRowY < totalRowHeight ? findRowIdx(nextRowY) : rows.length - 1 };\n      }\n      default:\n        return selectedPosition;\n    }\n  }\n\n  function navigate(event: KeyboardEvent<HTMLDivElement>) {\n    const { key, shiftKey } = event;\n    let cellNavigationMode: CellNavigationMode = 'NONE';\n    if (key === 'Tab') {\n      if (\n        canExitGrid({\n          shiftKey,\n          maxColIdx,\n          minRowIdx,\n          maxRowIdx,\n          selectedPosition\n        })\n      ) {\n        commitEditorChanges();\n        // Allow focus to leave the grid so the next control in the tab order can be focused\n        return;\n      }\n\n      cellNavigationMode = 'CHANGE_ROW';\n    }\n\n    // prevent scrolling and do not allow focus to leave\n    event.preventDefault();\n\n    const ctrlKey = isCtrlKeyHeldDown(event);\n    const nextPosition = getNextPosition(key, ctrlKey, shiftKey);\n    if (isSamePosition(selectedPosition, nextPosition)) return;\n\n    const nextSelectedCellPosition = getNextSelectedCellPosition({\n      moveUp: key === 'ArrowUp',\n      moveNext: key === rightKey || (key === 'Tab' && !shiftKey),\n      columns,\n      colSpanColumns,\n      rows,\n      topSummaryRows,\n      bottomSummaryRows,\n      minRowIdx,\n      mainHeaderRowIdx,\n      maxRowIdx,\n      lastFrozenColumnIndex,\n      cellNavigationMode,\n      currentPosition: selectedPosition,\n      nextPosition,\n      isCellWithinBounds: isCellWithinSelectionBounds\n    });\n\n    selectCell(nextSelectedCellPosition, { shouldFocusCell: true });\n  }\n\n  function getDraggedOverCellIdx(currentRowIdx: number): number | undefined {\n    if (draggedOverRowIdx === undefined) return;\n    const { rowIdx } = selectedPosition;\n\n    const isDraggedOver =\n      rowIdx < draggedOverRowIdx\n        ? rowIdx < currentRowIdx && currentRowIdx <= draggedOverRowIdx\n        : rowIdx > currentRowIdx && currentRowIdx >= draggedOverRowIdx;\n\n    return isDraggedOver ? selectedPosition.idx : undefined;\n  }\n\n  function getDragHandle() {\n    if (\n      onFill == null ||\n      selectedPosition.mode === 'EDIT' ||\n      !isCellWithinViewportBounds(selectedPosition)\n    ) {\n      return;\n    }\n\n    const { idx, rowIdx } = selectedPosition;\n    const column = columns[idx];\n    if (column.renderEditCell == null || column.editable === false) {\n      return;\n    }\n\n    const isLastRow = rowIdx === maxRowIdx;\n    const columnWidth = getColumnWidth(column);\n    const colSpan = column.colSpan?.({ type: 'ROW', row: rows[rowIdx] }) ?? 1;\n    const { insetInlineStart, ...style } = getCellStyle(column, colSpan);\n    const marginEnd = 'calc(var(--rdg-drag-handle-size) * -0.5 + 1px)';\n    const isLastColumn = column.idx + colSpan - 1 === maxColIdx;\n    const dragHandleStyle: React.CSSProperties = {\n      ...style,\n      gridRowStart: headerAndTopSummaryRowsCount + rowIdx + 1,\n      marginInlineEnd: isLastColumn ? undefined : marginEnd,\n      marginBlockEnd: isLastRow ? undefined : marginEnd,\n      insetInlineStart: insetInlineStart\n        ? `calc(${insetInlineStart} + ${columnWidth}px + var(--rdg-drag-handle-size) * -0.5 - 1px)`\n        : undefined\n    };\n\n    return (\n      <div\n        style={dragHandleStyle}\n        className={clsx(cellDragHandleClassname, column.frozen && cellDragHandleFrozenClassname)}\n        onPointerDown={handleDragHandlePointerDown}\n        onPointerMove={isDragging ? handleDragHandlePointerMove : undefined}\n        onLostPointerCapture={isDragging ? handleDragHandleLostPointerCapture : undefined}\n        onClick={handleDragHandleClick}\n        onDoubleClick={handleDragHandleDoubleClick}\n      />\n    );\n  }\n\n  function getCellEditor(rowIdx: number) {\n    if (selectedPosition.rowIdx !== rowIdx || selectedPosition.mode === 'SELECT') return;\n\n    const { idx, row } = selectedPosition;\n    const column = columns[idx];\n    const colSpan = getColSpan(column, lastFrozenColumnIndex, { type: 'ROW', row });\n    const closeOnExternalRowChange = column.editorOptions?.closeOnExternalRowChange ?? true;\n\n    const closeEditor = (shouldFocusCell: boolean) => {\n      setShouldFocusCell(shouldFocusCell);\n      setSelectedPosition(({ idx, rowIdx }) => ({ idx, rowIdx, mode: 'SELECT' }));\n    };\n\n    const onRowChange = (row: R, commitChanges: boolean, shouldFocusCell: boolean) => {\n      if (commitChanges) {\n        // Prevents two issues when editor is closed by clicking on a different cell\n        //\n        // Otherwise commitEditorChanges may be called before the cell state is changed to\n        // SELECT and this results in onRowChange getting called twice.\n        flushSync(() => {\n          updateRow(column, selectedPosition.rowIdx, row);\n          closeEditor(shouldFocusCell);\n        });\n      } else {\n        setSelectedPosition((position) => ({ ...position, row }));\n      }\n    };\n\n    if (\n      closeOnExternalRowChange &&\n      rows[selectedPosition.rowIdx] !== selectedPosition.originalRow\n    ) {\n      // Discard changes if rows are updated from outside\n      closeEditor(false);\n    }\n\n    return (\n      <EditCell\n        key={column.key}\n        column={column}\n        colSpan={colSpan}\n        row={row}\n        rowIdx={rowIdx}\n        onRowChange={onRowChange}\n        closeEditor={closeEditor}\n        onKeyDown={onCellKeyDown}\n        navigate={navigate}\n      />\n    );\n  }\n\n  function getRowViewportColumns(rowIdx: number) {\n    // idx can be -1 if grouping is enabled\n    const selectedColumn = selectedPosition.idx === -1 ? undefined : columns[selectedPosition.idx];\n    if (\n      selectedColumn !== undefined &&\n      selectedPosition.rowIdx === rowIdx &&\n      !viewportColumns.includes(selectedColumn)\n    ) {\n      // Add the selected column to viewport columns if the cell is not within the viewport\n      return selectedPosition.idx > colOverscanEndIdx\n        ? [...viewportColumns, selectedColumn]\n        : [\n            ...viewportColumns.slice(0, lastFrozenColumnIndex + 1),\n            selectedColumn,\n            ...viewportColumns.slice(lastFrozenColumnIndex + 1)\n          ];\n    }\n    return viewportColumns;\n  }\n\n  function getViewportRows() {\n    const rowElements: React.ReactNode[] = [];\n\n    const { idx: selectedIdx, rowIdx: selectedRowIdx } = selectedPosition;\n\n    const startRowIdx =\n      selectedCellIsWithinViewportBounds && selectedRowIdx < rowOverscanStartIdx\n        ? rowOverscanStartIdx - 1\n        : rowOverscanStartIdx;\n    const endRowIdx =\n      selectedCellIsWithinViewportBounds && selectedRowIdx > rowOverscanEndIdx\n        ? rowOverscanEndIdx + 1\n        : rowOverscanEndIdx;\n\n    for (let viewportRowIdx = startRowIdx; viewportRowIdx <= endRowIdx; viewportRowIdx++) {\n      const isRowOutsideViewport =\n        viewportRowIdx === rowOverscanStartIdx - 1 || viewportRowIdx === rowOverscanEndIdx + 1;\n      const rowIdx = isRowOutsideViewport ? selectedRowIdx : viewportRowIdx;\n\n      let rowColumns = viewportColumns;\n      const selectedColumn = selectedIdx === -1 ? undefined : columns[selectedIdx];\n      if (selectedColumn !== undefined) {\n        if (isRowOutsideViewport) {\n          // if the row is outside the viewport then only render the selected cell\n          rowColumns = [selectedColumn];\n        } else {\n          // if the row is within the viewport and cell is not, add the selected column to viewport columns\n          rowColumns = getRowViewportColumns(rowIdx);\n        }\n      }\n\n      const row = rows[rowIdx];\n      const gridRowStart = headerAndTopSummaryRowsCount + rowIdx + 1;\n      let key: K | number = rowIdx;\n      let isRowSelected = false;\n      if (typeof rowKeyGetter === 'function') {\n        key = rowKeyGetter(row);\n        isRowSelected = selectedRows?.has(key) ?? false;\n      }\n\n      rowElements.push(\n        renderRow(key, {\n          // aria-rowindex is 1 based\n          'aria-rowindex': headerAndTopSummaryRowsCount + rowIdx + 1,\n          'aria-selected': isSelectable ? isRowSelected : undefined,\n          rowIdx,\n          row,\n          viewportColumns: rowColumns,\n          isRowSelectionDisabled: isRowSelectionDisabled?.(row) ?? false,\n          isRowSelected,\n          onCellMouseDown: onCellMouseDownLatest,\n          onCellClick: onCellClickLatest,\n          onCellDoubleClick: onCellDoubleClickLatest,\n          onCellContextMenu: onCellContextMenuLatest,\n          rowClass,\n          gridRowStart,\n          selectedCellIdx: selectedRowIdx === rowIdx ? selectedIdx : undefined,\n          draggedOverCellIdx: getDraggedOverCellIdx(rowIdx),\n          lastFrozenColumnIndex,\n          onRowChange: handleFormatterRowChangeLatest,\n          selectCell: selectCellLatest,\n          selectedCellEditor: getCellEditor(rowIdx)\n        })\n      );\n    }\n\n    return rowElements;\n  }\n\n  // Reset the positions if the current values are no longer valid. This can happen if a column or row is removed\n  if (selectedPosition.idx > maxColIdx || selectedPosition.rowIdx > maxRowIdx) {\n    setSelectedPosition({ idx: -1, rowIdx: minRowIdx - 1, mode: 'SELECT' });\n    setDraggedOverRowIdx(undefined);\n  }\n\n  // Keep the state and prop in sync\n  if (isColumnWidthsControlled && columnWidthsInternal !== columnWidthsRaw) {\n    setColumnWidthsInternal(columnWidthsRaw);\n  }\n\n  let templateRows = `repeat(${headerRowsCount}, ${headerRowHeight}px)`;\n  if (topSummaryRowsCount > 0) {\n    templateRows += ` repeat(${topSummaryRowsCount}, ${summaryRowHeight}px)`;\n  }\n  if (rows.length > 0) {\n    templateRows += gridTemplateRows;\n  }\n  if (bottomSummaryRowsCount > 0) {\n    templateRows += ` repeat(${bottomSummaryRowsCount}, ${summaryRowHeight}px)`;\n  }\n\n  const isGroupRowFocused =\n    selectedPosition.idx === -1 && selectedPosition.rowIdx !== minRowIdx - 1;\n\n  return (\n    // biome-ignore lint/a11y/useValidAriaProps: aria-description is a valid prop\n    <div\n      role={role}\n      aria-label={ariaLabel}\n      aria-labelledby={ariaLabelledBy}\n      aria-description={ariaDescription}\n      aria-describedby={ariaDescribedBy}\n      aria-multiselectable={isSelectable ? true : undefined}\n      aria-colcount={columns.length}\n      aria-rowcount={ariaRowCount}\n      // Scrollable containers without tabIndex are keyboard focusable in Chrome only if there is no focusable element inside\n      // whereas they are always focusable in Firefox. We need to set tabIndex to have a consistent behavior across browsers.\n      tabIndex={shouldFocusGrid ? 0 : -1}\n      className={clsx(\n        rootClassname,\n        {\n          [viewportDraggingClassname]: isDragging\n        },\n        className\n      )}\n      style={\n        {\n          ...style,\n          // set scrollPadding to correctly position non-sticky cells after scrolling\n          scrollPaddingInlineStart:\n            selectedPosition.idx > lastFrozenColumnIndex || scrollToPosition?.idx !== undefined\n              ? `${totalFrozenColumnWidth}px`\n              : undefined,\n          scrollPaddingBlock:\n            isRowIdxWithinViewportBounds(selectedPosition.rowIdx) ||\n            scrollToPosition?.rowIdx !== undefined\n              ? `${headerRowsHeight + topSummaryRowsCount * summaryRowHeight}px ${\n                  bottomSummaryRowsCount * summaryRowHeight\n                }px`\n              : undefined,\n          gridTemplateColumns,\n          gridTemplateRows: templateRows,\n          '--rdg-header-row-height': `${headerRowHeight}px`,\n          '--rdg-scroll-height': `${scrollHeight}px`,\n          ...layoutCssVars\n        } as unknown as React.CSSProperties\n      }\n      dir={direction}\n      ref={gridRef}\n      onFocus={shouldFocusGrid ? handleFocus : undefined}\n      onScroll={handleScroll}\n      onKeyDown={handleKeyDown}\n      onCopy={handleCellCopy}\n      onPaste={handleCellPaste}\n      data-testid={testId}\n      data-cy={dataCy}\n    >\n      <DataGridDefaultRenderersContext value={defaultGridComponents}>\n        <HeaderRowSelectionChangeContext value={selectHeaderRowLatest}>\n          <HeaderRowSelectionContext value={headerSelectionValue}>\n            {Array.from({ length: groupedColumnHeaderRowsCount }, (_, index) => (\n              <GroupedColumnHeaderRow\n                key={index}\n                rowIdx={index + 1}\n                level={-groupedColumnHeaderRowsCount + index}\n                columns={getRowViewportColumns(minRowIdx + index)}\n                selectedCellIdx={\n                  selectedPosition.rowIdx === minRowIdx + index ? selectedPosition.idx : undefined\n                }\n                selectCell={selectHeaderCellLatest}\n              />\n            ))}\n            <HeaderRow\n              headerRowClass={headerRowClass}\n              rowIdx={headerRowsCount}\n              columns={getRowViewportColumns(mainHeaderRowIdx)}\n              onColumnResize={handleColumnResizeLatest}\n              onColumnResizeEnd={handleColumnResizeEndLatest}\n              onColumnsReorder={onColumnsReorderLastest}\n              sortColumns={sortColumns}\n              onSortColumnsChange={onSortColumnsChangeLatest}\n              lastFrozenColumnIndex={lastFrozenColumnIndex}\n              selectedCellIdx={\n                selectedPosition.rowIdx === mainHeaderRowIdx ? selectedPosition.idx : undefined\n              }\n              selectCell={selectHeaderCellLatest}\n              direction={direction}\n            />\n          </HeaderRowSelectionContext>\n        </HeaderRowSelectionChangeContext>\n        {rows.length === 0 && noRowsFallback ? (\n          noRowsFallback\n        ) : (\n          <>\n            {topSummaryRows?.map((row, rowIdx) => {\n              const gridRowStart = headerRowsCount + 1 + rowIdx;\n              const summaryRowIdx = mainHeaderRowIdx + 1 + rowIdx;\n              const isSummaryRowSelected = selectedPosition.rowIdx === summaryRowIdx;\n              const top = headerRowsHeight + summaryRowHeight * rowIdx;\n\n              return (\n                <SummaryRow\n                  key={rowIdx}\n                  aria-rowindex={gridRowStart}\n                  rowIdx={summaryRowIdx}\n                  gridRowStart={gridRowStart}\n                  row={row}\n                  top={top}\n                  bottom={undefined}\n                  viewportColumns={getRowViewportColumns(summaryRowIdx)}\n                  lastFrozenColumnIndex={lastFrozenColumnIndex}\n                  selectedCellIdx={isSummaryRowSelected ? selectedPosition.idx : undefined}\n                  isTop\n                  selectCell={selectCellLatest}\n                />\n              );\n            })}\n            <RowSelectionChangeContext value={selectRowLatest}>\n              {getViewportRows()}\n            </RowSelectionChangeContext>\n            {bottomSummaryRows?.map((row, rowIdx) => {\n              const gridRowStart = headerAndTopSummaryRowsCount + rows.length + rowIdx + 1;\n              const summaryRowIdx = rows.length + rowIdx;\n              const isSummaryRowSelected = selectedPosition.rowIdx === summaryRowIdx;\n              const top =\n                clientHeight > totalRowHeight\n                  ? gridHeight - summaryRowHeight * (bottomSummaryRows.length - rowIdx)\n                  : undefined;\n              const bottom =\n                top === undefined\n                  ? summaryRowHeight * (bottomSummaryRows.length - 1 - rowIdx)\n                  : undefined;\n\n              return (\n                <SummaryRow\n                  aria-rowindex={ariaRowCount - bottomSummaryRowsCount + rowIdx + 1}\n                  key={rowIdx}\n                  rowIdx={summaryRowIdx}\n                  gridRowStart={gridRowStart}\n                  row={row}\n                  top={top}\n                  bottom={bottom}\n                  viewportColumns={getRowViewportColumns(summaryRowIdx)}\n                  lastFrozenColumnIndex={lastFrozenColumnIndex}\n                  selectedCellIdx={isSummaryRowSelected ? selectedPosition.idx : undefined}\n                  isTop={false}\n                  selectCell={selectCellLatest}\n                />\n              );\n            })}\n          </>\n        )}\n      </DataGridDefaultRenderersContext>\n\n      {getDragHandle()}\n\n      {/* render empty cells that span only 1 column so we can safely measure column widths, regardless of colSpan */}\n      {renderMeasuringCells(viewportColumns)}\n\n      {/* extra div is needed for row navigation in a treegrid */}\n      {isTreeGrid && (\n        <div\n          ref={focusSinkRef}\n          tabIndex={isGroupRowFocused ? 0 : -1}\n          className={clsx(focusSinkClassname, {\n            [focusSinkHeaderAndSummaryClassname]: !isRowIdxWithinViewportBounds(\n              selectedPosition.rowIdx\n            ),\n            [rowSelected]: isGroupRowFocused,\n            [rowSelectedWithFrozenCell]: isGroupRowFocused && lastFrozenColumnIndex !== -1\n          })}\n          style={{\n            gridRowStart: selectedPosition.rowIdx + headerAndTopSummaryRowsCount + 1\n          }}\n        />\n      )}\n\n      {scrollToPosition !== null && (\n        <ScrollToCell\n          scrollToPosition={scrollToPosition}\n          setScrollToCellPosition={setScrollToPosition}\n          gridRef={gridRef}\n        />\n      )}\n    </div>\n  );\n}\n\nfunction getCellToScroll(gridEl: HTMLDivElement) {\n  return gridEl.querySelector<HTMLDivElement>(':scope > [role=\"row\"] > [tabindex=\"0\"]');\n}\n\nfunction isSamePosition(p1: Position, p2: Position) {\n  return p1.idx === p2.idx && p1.rowIdx === p2.rowIdx;\n}\n", "import { memo } from 'react';\n\nimport { useRovingTabIndex } from './hooks';\nimport { getCellClassname, getCellStyle } from './utils';\nimport type { CalculatedColumn, GroupRow } from './types';\n\ninterface GroupCellProps<R, SR> {\n  id: string;\n  groupKey: unknown;\n  childRows: readonly R[];\n  toggleGroup: (expandedGroupId: unknown) => void;\n  isExpanded: boolean;\n  column: CalculatedColumn<R, SR>;\n  row: GroupRow<R>;\n  isCellSelected: boolean;\n  groupColumnIndex: number;\n  isGroupByColumn: boolean;\n}\n\nfunction GroupCell<R, SR>({\n  id,\n  groupKey,\n  childRows,\n  isExpanded,\n  isCellSelected,\n  column,\n  row,\n  groupColumnIndex,\n  isGroupByColumn,\n  toggleGroup: toggleGroupWrapper\n}: GroupCellProps<R, SR>) {\n  const { tabIndex, childTabIndex, onFocus } = useRovingTabIndex(isCellSelected);\n\n  function toggleGroup() {\n    toggleGroupWrapper(id);\n  }\n\n  // Only make the cell clickable if the group level matches\n  const isLevelMatching = isGroupByColumn && groupColumnIndex === column.idx;\n\n  return (\n    <div\n      role=\"gridcell\"\n      aria-colindex={column.idx + 1}\n      aria-selected={isCellSelected}\n      tabIndex={tabIndex}\n      key={column.key}\n      className={getCellClassname(column)}\n      style={{\n        ...getCellStyle(column),\n        cursor: isLevelMatching ? 'pointer' : 'default'\n      }}\n      onMouseDown={(event) => {\n        // prevents clicking on the cell from stealing focus from focusSink\n        event.preventDefault();\n      }}\n      onClick={isLevelMatching ? toggleGroup : undefined}\n      onFocus={onFocus}\n    >\n      {(!isGroupByColumn || isLevelMatching) &&\n        column.renderGroupCell?.({\n          groupKey,\n          childRows,\n          column,\n          row,\n          isExpanded,\n          tabIndex: childTabIndex,\n          toggleGroup\n        })}\n    </div>\n  );\n}\n\nexport default memo(GroupCell) as <R, SR>(props: GroupCellProps<R, SR>) => React.JSX.Element;\n", "import { memo, useMemo } from 'react';\nimport { css } from '@linaria/core';\nimport clsx from 'clsx';\n\nimport { RowSelectionContext, type RowSelectionContextValue } from './hooks';\nimport { getRowStyle } from './utils';\nimport type { BaseRenderRowProps, GroupRow } from './types';\nimport { SELECT_COLUMN_KEY } from './Columns';\nimport GroupCell from './GroupCell';\nimport { cell, cellFrozen } from './style/cell';\nimport { rowClassname, rowSelectedClassname } from './style/row';\n\nconst groupRow = css`\n  @layer rdg.GroupedRow {\n    &:not([aria-selected='true']) {\n      background-color: var(--rdg-header-background-color);\n    }\n\n    > .${cell}:not(:last-child, .${cellFrozen}),\n    > :nth-last-child(n + 2 of .${cellFrozen}) {\n      border-inline-end: none;\n    }\n  }\n`;\n\nconst groupRowClassname = `rdg-group-row ${groupRow}`;\n\ninterface GroupRowRendererProps<R, SR> extends BaseRenderRowProps<R, SR> {\n  row: GroupRow<R>;\n  groupBy: readonly string[];\n  toggleGroup: (expandedGroupId: unknown) => void;\n}\n\nfunction GroupedRow<R, SR>({\n  className,\n  row,\n  rowIdx,\n  viewportColumns,\n  selectedCellIdx,\n  isRowSelected,\n  selectCell,\n  gridRowStart,\n  groupBy,\n  toggleGroup,\n  isRowSelectionDisabled,\n  ...props\n}: GroupRowRendererProps<R, SR>) {\n  // Select is always the first column\n  const idx = viewportColumns[0].key === SELECT_COLUMN_KEY ? row.level + 1 : row.level;\n\n  function handleSelectGroup() {\n    selectCell({ rowIdx, idx: -1 }, { shouldFocusCell: true });\n  }\n\n  const selectionValue = useMemo(\n    (): RowSelectionContextValue => ({ isRowSelectionDisabled: false, isRowSelected }),\n    [isRowSelected]\n  );\n\n  return (\n    <RowSelectionContext value={selectionValue}>\n      <div\n        role=\"row\"\n        aria-level={row.level + 1} // aria-level is 1-based\n        aria-setsize={row.setSize}\n        aria-posinset={row.posInSet + 1} // aria-posinset is 1-based\n        aria-expanded={row.isExpanded}\n        className={clsx(\n          rowClassname,\n          groupRowClassname,\n          `rdg-row-${rowIdx % 2 === 0 ? 'even' : 'odd'}`,\n          selectedCellIdx === -1 && rowSelectedClassname,\n          className\n        )}\n        onMouseDown={handleSelectGroup}\n        style={getRowStyle(gridRowStart)}\n        {...props}\n      >\n        {viewportColumns.map((column) => (\n          <GroupCell\n            key={column.key}\n            id={row.id}\n            groupKey={row.groupKey}\n            childRows={row.childRows}\n            isExpanded={row.isExpanded}\n            isCellSelected={selectedCellIdx === column.idx}\n            column={column}\n            row={row}\n            groupColumnIndex={idx}\n            toggleGroup={toggleGroup}\n            isGroupByColumn={groupBy.includes(column.key)}\n          />\n        ))}\n      </div>\n    </RowSelectionContext>\n  );\n}\n\nexport default memo(GroupedRow) as <R, SR>(\n  props: GroupRowRendererProps<R, SR>\n) => React.JSX.Element;\n", "import { useCallback, useMemo } from 'react';\nimport type { Key } from 'react';\n\nimport { useLatestFunc } from './hooks';\nimport { assertIsValidKeyGetter, getLeftRightKey } from './utils';\nimport type {\n  CellClipboardEvent,\n  CellCopyArgs,\n  CellKeyboardEvent,\n  CellKeyDownArgs,\n  CellPasteArgs,\n  Column,\n  GroupRow,\n  Maybe,\n  Omit,\n  RenderRowProps,\n  RowHeightArgs,\n  RowsChangeData\n} from './types';\nimport { renderToggleGroup } from './cellRenderers';\nimport { SELECT_COLUMN_KEY } from './Columns';\nimport { DataGrid } from './DataGrid';\nimport type { DataGridProps } from './DataGrid';\nimport { useDefaultRenderers } from './DataGridDefaultRenderersContext';\nimport GroupedRow from './GroupRow';\nimport { defaultRenderRow } from './Row';\n\nexport interface TreeDataGridProps<R, SR = unknown, K extends Key = Key>\n  extends Omit<\n    DataGridProps<R, SR, K>,\n    'columns' | 'role' | 'aria-rowcount' | 'rowHeight' | 'onFill' | 'isRowSelectionDisabled'\n  > {\n  columns: readonly Column<NoInfer<R>, NoInfer<SR>>[];\n  rowHeight?: Maybe<number | ((args: RowHeightArgs<NoInfer<R>>) => number)>;\n  groupBy: readonly string[];\n  rowGrouper: (\n    rows: readonly NoInfer<R>[],\n    columnKey: string\n  ) => Record<string, readonly NoInfer<R>[]>;\n  expandedGroupIds: ReadonlySet<unknown>;\n  onExpandedGroupIdsChange: (expandedGroupIds: Set<unknown>) => void;\n  groupIdGetter?: Maybe<(groupKey: string, parentId?: string) => string>;\n}\n\ntype GroupByDictionary<TRow> = Record<\n  string,\n  {\n    readonly childRows: readonly TRow[];\n    readonly childGroups: readonly TRow[] | Readonly<GroupByDictionary<TRow>>;\n    readonly startRowIndex: number;\n  }\n>;\n\nexport function TreeDataGrid<R, SR = unknown, K extends Key = Key>({\n  columns: rawColumns,\n  rows: rawRows,\n  rowHeight: rawRowHeight,\n  rowKeyGetter: rawRowKeyGetter,\n  onCellKeyDown: rawOnCellKeyDown,\n  onCellCopy: rawOnCellCopy,\n  onCellPaste: rawOnCellPaste,\n  onRowsChange,\n  selectedRows: rawSelectedRows,\n  onSelectedRowsChange: rawOnSelectedRowsChange,\n  renderers,\n  groupBy: rawGroupBy,\n  rowGrouper,\n  expandedGroupIds,\n  onExpandedGroupIdsChange,\n  groupIdGetter: rawGroupIdGetter,\n  ...props\n}: TreeDataGridProps<R, SR, K>) {\n  const defaultRenderers = useDefaultRenderers<R, SR>();\n  const rawRenderRow = renderers?.renderRow ?? defaultRenderers?.renderRow ?? defaultRenderRow;\n  const headerAndTopSummaryRowsCount = 1 + (props.topSummaryRows?.length ?? 0);\n  const { leftKey, rightKey } = getLeftRightKey(props.direction);\n  const toggleGroupLatest = useLatestFunc(toggleGroup);\n  const groupIdGetter = rawGroupIdGetter ?? defaultGroupIdGetter;\n\n  const { columns, groupBy } = useMemo(() => {\n    const columns = rawColumns.toSorted(({ key: aKey }, { key: bKey }) => {\n      // Sort select column first:\n      if (aKey === SELECT_COLUMN_KEY) return -1;\n      if (bKey === SELECT_COLUMN_KEY) return 1;\n\n      // Sort grouped columns second, following the groupBy order:\n      if (rawGroupBy.includes(aKey)) {\n        if (rawGroupBy.includes(bKey)) {\n          return rawGroupBy.indexOf(aKey) - rawGroupBy.indexOf(bKey);\n        }\n        return -1;\n      }\n      if (rawGroupBy.includes(bKey)) return 1;\n\n      // Sort other columns last:\n      return 0;\n    });\n\n    const groupBy: string[] = [];\n    for (const [index, column] of columns.entries()) {\n      if (rawGroupBy.includes(column.key)) {\n        groupBy.push(column.key);\n        columns[index] = {\n          ...column,\n          frozen: true,\n          renderCell: () => null,\n          renderGroupCell: column.renderGroupCell ?? renderToggleGroup,\n          editable: false\n        };\n      }\n    }\n\n    return { columns, groupBy };\n  }, [rawColumns, rawGroupBy]);\n\n  const [groupedRows, rowsCount] = useMemo(() => {\n    if (groupBy.length === 0) return [undefined, rawRows.length];\n\n    const groupRows = (\n      rows: readonly R[],\n      [groupByKey, ...remainingGroupByKeys]: readonly string[],\n      startRowIndex: number\n    ): [Readonly<GroupByDictionary<R>>, number] => {\n      let groupRowsCount = 0;\n      const groups: GroupByDictionary<R> = {};\n      for (const [key, childRows] of Object.entries(rowGrouper(rows, groupByKey))) {\n        // Recursively group each parent group\n        const [childGroups, childRowsCount] =\n          remainingGroupByKeys.length === 0\n            ? [childRows, childRows.length]\n            : groupRows(childRows, remainingGroupByKeys, startRowIndex + groupRowsCount + 1); // 1 for parent row\n        groups[key] = { childRows, childGroups, startRowIndex: startRowIndex + groupRowsCount };\n        groupRowsCount += childRowsCount + 1; // 1 for parent row\n      }\n\n      return [groups, groupRowsCount];\n    };\n\n    return groupRows(rawRows, groupBy, 0);\n  }, [groupBy, rowGrouper, rawRows]);\n\n  const [rows, isGroupRow] = useMemo((): [\n    ReadonlyArray<R | GroupRow<R>>,\n    (row: R | GroupRow<R>) => row is GroupRow<R>\n  ] => {\n    const allGroupRows = new Set<unknown>();\n    if (!groupedRows) return [rawRows, isGroupRow];\n\n    const flattenedRows: Array<R | GroupRow<R>> = [];\n\n    const expandGroup = (\n      rows: GroupByDictionary<R> | readonly R[],\n      parentId: string | undefined,\n      level: number\n    ): void => {\n      if (isReadonlyArray(rows)) {\n        flattenedRows.push(...rows);\n        return;\n      }\n      Object.keys(rows).forEach((groupKey, posInSet, keys) => {\n        const id = groupIdGetter(groupKey, parentId);\n        const isExpanded = expandedGroupIds.has(id);\n        const { childRows, childGroups, startRowIndex } = rows[groupKey];\n\n        const groupRow: GroupRow<R> = {\n          id,\n          parentId,\n          groupKey,\n          isExpanded,\n          childRows,\n          level,\n          posInSet,\n          startRowIndex,\n          setSize: keys.length\n        };\n        flattenedRows.push(groupRow);\n        allGroupRows.add(groupRow);\n\n        if (isExpanded) {\n          expandGroup(childGroups, id, level + 1);\n        }\n      });\n    };\n\n    expandGroup(groupedRows, undefined, 0);\n    return [flattenedRows, isGroupRow];\n\n    function isGroupRow(row: R | GroupRow<R>): row is GroupRow<R> {\n      return allGroupRows.has(row);\n    }\n  }, [expandedGroupIds, groupedRows, rawRows, groupIdGetter]);\n\n  const rowHeight = useMemo(() => {\n    if (typeof rawRowHeight === 'function') {\n      return (row: R | GroupRow<R>): number => {\n        if (isGroupRow(row)) {\n          return rawRowHeight({ type: 'GROUP', row });\n        }\n        return rawRowHeight({ type: 'ROW', row });\n      };\n    }\n\n    return rawRowHeight;\n  }, [isGroupRow, rawRowHeight]);\n\n  const getParentRowAndIndex = useCallback(\n    (row: R | GroupRow<R>) => {\n      const rowIdx = rows.indexOf(row);\n      for (let i = rowIdx - 1; i >= 0; i--) {\n        const parentRow = rows[i];\n        if (isGroupRow(parentRow) && (!isGroupRow(row) || row.parentId === parentRow.id)) {\n          return [parentRow, i] as const;\n        }\n      }\n\n      return undefined;\n    },\n    [isGroupRow, rows]\n  );\n\n  const rowKeyGetter = useCallback(\n    (row: R | GroupRow<R>) => {\n      if (isGroupRow(row)) {\n        return row.id;\n      }\n\n      if (typeof rawRowKeyGetter === 'function') {\n        return rawRowKeyGetter(row);\n      }\n\n      const parentRowAndIndex = getParentRowAndIndex(row);\n      if (parentRowAndIndex !== undefined) {\n        const { startRowIndex, childRows } = parentRowAndIndex[0];\n        const groupIndex = childRows.indexOf(row);\n        return startRowIndex + groupIndex + 1;\n      }\n\n      return rows.indexOf(row);\n    },\n    [getParentRowAndIndex, isGroupRow, rawRowKeyGetter, rows]\n  );\n\n  const selectedRows = useMemo((): Maybe<ReadonlySet<Key>> => {\n    if (rawSelectedRows == null) return null;\n\n    assertIsValidKeyGetter<R, K>(rawRowKeyGetter);\n\n    const selectedRows = new Set<Key>(rawSelectedRows);\n    for (const row of rows) {\n      if (isGroupRow(row)) {\n        // select parent row if all the children are selected\n        const isGroupRowSelected = row.childRows.every((cr) =>\n          rawSelectedRows.has(rawRowKeyGetter(cr))\n        );\n        if (isGroupRowSelected) {\n          selectedRows.add(row.id);\n        }\n      }\n    }\n\n    return selectedRows;\n  }, [isGroupRow, rawRowKeyGetter, rawSelectedRows, rows]);\n\n  function onSelectedRowsChange(newSelectedRows: Set<Key>) {\n    if (!rawOnSelectedRowsChange) return;\n\n    assertIsValidKeyGetter<R, K>(rawRowKeyGetter);\n\n    const newRawSelectedRows = new Set(rawSelectedRows);\n    for (const row of rows) {\n      const key = rowKeyGetter(row);\n      if (selectedRows?.has(key) && !newSelectedRows.has(key)) {\n        if (isGroupRow(row)) {\n          // select all children if the parent row is selected\n          for (const cr of row.childRows) {\n            newRawSelectedRows.delete(rawRowKeyGetter(cr));\n          }\n        } else {\n          newRawSelectedRows.delete(key as K);\n        }\n      } else if (!selectedRows?.has(key) && newSelectedRows.has(key)) {\n        if (isGroupRow(row)) {\n          // unselect all children if the parent row is unselected\n          for (const cr of row.childRows) {\n            newRawSelectedRows.add(rawRowKeyGetter(cr));\n          }\n        } else {\n          newRawSelectedRows.add(key as K);\n        }\n      }\n    }\n\n    rawOnSelectedRowsChange(newRawSelectedRows);\n  }\n\n  function handleKeyDown(args: CellKeyDownArgs<R, SR>, event: CellKeyboardEvent) {\n    rawOnCellKeyDown?.(args, event);\n    if (event.isGridDefaultPrevented()) return;\n\n    if (args.mode === 'EDIT') return;\n    const { column, rowIdx, selectCell } = args;\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    const idx = column?.idx ?? -1;\n    const row = rows[rowIdx];\n\n    if (!isGroupRow(row)) return;\n    if (\n      idx === -1 &&\n      // Collapse the current group row if it is focused and is in expanded state\n      ((event.key === leftKey && row.isExpanded) ||\n        // Expand the current group row if it is focused and is in collapsed state\n        (event.key === rightKey && !row.isExpanded))\n    ) {\n      // prevent scrolling\n      event.preventDefault();\n      event.preventGridDefault();\n      toggleGroup(row.id);\n    }\n\n    // If a group row is focused, and it is collapsed, move to the parent group row (if there is one).\n    if (idx === -1 && event.key === leftKey && !row.isExpanded && row.level !== 0) {\n      const parentRowAndIndex = getParentRowAndIndex(row);\n      if (parentRowAndIndex !== undefined) {\n        event.preventGridDefault();\n        selectCell({ idx, rowIdx: parentRowAndIndex[1] });\n      }\n    }\n  }\n\n  // Prevent copy/paste on group rows\n  function handleCellCopy(\n    { row, column }: CellCopyArgs<NoInfer<R>, NoInfer<SR>>,\n    event: CellClipboardEvent\n  ) {\n    if (!isGroupRow(row)) {\n      rawOnCellCopy?.({ row, column }, event);\n    }\n  }\n\n  function handleCellPaste(\n    { row, column }: CellPasteArgs<NoInfer<R>, NoInfer<SR>>,\n    event: CellClipboardEvent\n  ) {\n    return isGroupRow(row) ? row : rawOnCellPaste!({ row, column }, event);\n  }\n\n  function handleRowsChange(updatedRows: R[], { indexes, column }: RowsChangeData<R, SR>) {\n    if (!onRowsChange) return;\n    const updatedRawRows = [...rawRows];\n    const rawIndexes: number[] = [];\n    for (const index of indexes) {\n      const rawIndex = rawRows.indexOf(rows[index] as R);\n      updatedRawRows[rawIndex] = updatedRows[index];\n      rawIndexes.push(rawIndex);\n    }\n    onRowsChange(updatedRawRows, {\n      indexes: rawIndexes,\n      column\n    });\n  }\n\n  function toggleGroup(groupId: unknown) {\n    const newExpandedGroupIds = new Set(expandedGroupIds);\n    if (newExpandedGroupIds.has(groupId)) {\n      newExpandedGroupIds.delete(groupId);\n    } else {\n      newExpandedGroupIds.add(groupId);\n    }\n    onExpandedGroupIdsChange(newExpandedGroupIds);\n  }\n\n  function renderRow(\n    key: Key,\n    {\n      row,\n      rowClass,\n      onCellMouseDown,\n      onCellClick,\n      onCellDoubleClick,\n      onCellContextMenu,\n      onRowChange,\n      lastFrozenColumnIndex,\n      draggedOverCellIdx,\n      selectedCellEditor,\n      ...rowProps\n    }: RenderRowProps<R, SR>\n  ) {\n    if (isGroupRow(row)) {\n      const { startRowIndex } = row;\n      return (\n        <GroupedRow\n          key={key}\n          {...rowProps}\n          aria-rowindex={headerAndTopSummaryRowsCount + startRowIndex + 1}\n          row={row}\n          groupBy={groupBy}\n          toggleGroup={toggleGroupLatest}\n        />\n      );\n    }\n\n    let ariaRowIndex = rowProps['aria-rowindex'];\n    const parentRowAndIndex = getParentRowAndIndex(row);\n    if (parentRowAndIndex !== undefined) {\n      const { startRowIndex, childRows } = parentRowAndIndex[0];\n      const groupIndex = childRows.indexOf(row);\n      ariaRowIndex = startRowIndex + headerAndTopSummaryRowsCount + groupIndex + 2;\n    }\n\n    return rawRenderRow(key, {\n      ...rowProps,\n      'aria-rowindex': ariaRowIndex,\n      row,\n      rowClass,\n      onCellMouseDown,\n      onCellClick,\n      onCellDoubleClick,\n      onCellContextMenu,\n      onRowChange,\n      lastFrozenColumnIndex,\n      draggedOverCellIdx,\n      selectedCellEditor\n    });\n  }\n\n  return (\n    <DataGrid<R, SR>\n      {...props}\n      role=\"treegrid\"\n      aria-rowcount={\n        rowsCount + 1 + (props.topSummaryRows?.length ?? 0) + (props.bottomSummaryRows?.length ?? 0)\n      }\n      columns={columns}\n      rows={rows as R[]} // TODO: check types\n      rowHeight={rowHeight}\n      rowKeyGetter={rowKeyGetter}\n      onRowsChange={handleRowsChange}\n      selectedRows={selectedRows}\n      onSelectedRowsChange={onSelectedRowsChange}\n      onCellKeyDown={handleKeyDown}\n      onCellCopy={handleCellCopy}\n      onCellPaste={rawOnCellPaste ? handleCellPaste : undefined}\n      renderers={{\n        ...renderers,\n        renderRow\n      }}\n    />\n  );\n}\n\nfunction defaultGroupIdGetter(groupKey: string, parentId: string | undefined) {\n  return parentId !== undefined ? `${parentId}__${groupKey}` : groupKey;\n}\n\nfunction isReadonlyArray(arr: unknown): arr is readonly unknown[] {\n  return Array.isArray(arr);\n}\n", "import { css } from '@linaria/core';\n\nimport type { RenderEditCellProps } from '../types';\n\nconst textEditorInternalClassname = css`\n  @layer rdg.TextEditor {\n    appearance: none;\n\n    box-sizing: border-box;\n    inline-size: 100%;\n    block-size: 100%;\n    padding-block: 0;\n    padding-inline: 6px;\n    border: 2px solid #ccc;\n    vertical-align: top;\n    color: var(--rdg-color);\n    background-color: var(--rdg-background-color);\n\n    font-family: inherit;\n    font-size: var(--rdg-font-size);\n\n    &:focus {\n      border-color: var(--rdg-selection-color);\n      outline: none;\n    }\n\n    &::placeholder {\n      color: #999;\n      opacity: 1;\n    }\n  }\n`;\n\nexport const textEditorClassname = `rdg-text-editor ${textEditorInternalClassname}`;\n\nfunction autoFocusAndSelect(input: HTMLInputElement | null) {\n  input?.focus();\n  input?.select();\n}\n\nexport default function textEditor<TRow, TSummaryRow>({\n  row,\n  column,\n  onRowChange,\n  onClose\n}: RenderEditCellProps<TRow, TSummaryRow>) {\n  return (\n    <input\n      className={textEditorClassname}\n      ref={autoFocusAndSelect}\n      value={row[column.key as keyof TRow] as unknown as string}\n      onChange={(event) => onRowChange({ ...row, [column.key]: event.target.value })}\n      onBlur={() => onClose(true, false)}\n    />\n  );\n}\n"], "mappings": ";;;;;;AAEA,SAAgB,WACdA,QACAC,uBACAC,MACoB;CACpB,MAAM,iBAAiB,OAAO,YAAY,aAAa,OAAO,QAAQ,KAAK,GAAG;AAC9E,KACE,OAAO,UAAU,QAAQ,IACzB,UAAW,OAET,OAAO,UAAU,OAAO,MAAM,UAAW,KAAK,uBAEhD,QAAO;AAET;AACD;;;;ACfD,SAAgB,gBAAgBC,OAA6B;AAC3D,OAAM,iBAAiB;AACxB;AAED,SAAgB,eAAeC,SAAyBC,WAA2B,WAAW;AAC5F,UAAS,eAAe;EAAE,QAAQ;EAAW,OAAO;EAAW;CAAU,EAAC;AAC3E;;;;ACND,SAAgB,gBACdC,OACc;CACd,IAAI,mBAAmB;CACvB,MAAM,YAAY;EAChB,GAAG;EACH,qBAAqB;AACnB,sBAAmB;EACpB;EACD,yBAAyB;AACvB,UAAO;EACR;CACF;AAED,QAAO,eAAe,WAAW,OAAO,eAAe,MAAM,CAAC;AAE9D,QAAO;AACR;;;;AChBD,MAAM,eAAe,IAAI,IAAI;CAE3B;CAEA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CAEA;CAEA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CAEA;CAEA;CACA;CACA;CACA;CAEA;CAEA;CAEA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;AACD;AAED,SAAgB,kBAAkBC,GAAiC;AACjE,SAAQ,EAAE,WAAW,EAAE,YAAY,EAAE,QAAQ;AAC9C;AAID,MAAM,OAAO;AAEb,SAAgB,mBACdC,OACAC,qBACS;AAET,KAAI,kBAAkB,MAAM,KAAK,MAAM,YAAY,QAAQ,qBAAsB,QAAO;AACxF,SAAQ,aAAa,IAAI,MAAM,IAAI;AACpC;;;;;;;AAQD,SAAgB,mBAAmB,EAAE,KAAK,QAA6C,EAAW;AAChG,KACE,QAAQ,UACP,kBAAkB,oBACjB,kBAAkB,uBAClB,kBAAkB,mBAEpB,QACE,OAAO,QAAQ,wBAAwB,EAAE,iBAAiB,0BAA0B,CACjF,WAAW;AAGlB,QAAO;AACR;AAED,SAAgB,gBAAgBC,WAA6B;CAC3D,MAAM,QAAQ,cAAc;AAE5B,QAAO;EACL,SAAS,QAAQ,eAAe;EAChC,UAAU,QAAQ,cAAc;CACjC;AACF;;;;AC7FD,MAAME,yBAAsB;AAQ5B,SAAgBC,qBAA4BC,iBAAqD;AAC/F,QAAOA,gBAAgBG,IAAI,CAAC,EAAEC,KAAKC,KAAKC,UAAUC,UAAU,qBAC1D,IAAC;EAEC,WAAWT;EACX,OAAO;GAAEU,iBAAiBH,MAAM;GAAGC;GAAUC;EAAU;EACvD,2BAAyBH;IAHpBA,IAKR,CAAC;AACJ;;;;ACNA,SAAgB,uBAA8B,EAC5C,kBACA,SACA,MACkC,EAAW;CAC7C,MAAM,SAAS,QAAQ,iBAAiB;CACxC,MAAMK,QAAM,KAAK,iBAAiB;AAClC,QAAO,mBAAmB,QAAQA,MAAI;AACvC;AAGD,SAAgB,mBAA0BC,QAAiCC,OAAiB;AAC1F,QACE,OAAO,kBAAkB,gBACjB,OAAO,aAAa,aAAa,OAAO,SAASF,MAAI,GAAG,OAAO,cAAc;AAExF;AAoBD,SAAS,uBAA8B,EACrC,MACA,gBACA,mBACA,QACA,kBACA,uBACA,QAOD,EAAE;CACD,MAAM,sBAAsB,gBAAgB,UAAU;AACtD,KAAI,WAAW,iBACb,QAAO,WAAW,QAAQ,uBAAuB,EAAE,MAAM,SAAU,EAAC;AAGtE,KACE,kBACA,SAAS,oBACT,UAAU,sBAAsB,iBAEhC,QAAO,WAAW,QAAQ,uBAAuB;EAC/C,MAAM;EACN,KAAK,eAAe,SAAS;CAC9B,EAAC;AAGJ,KAAI,UAAU,KAAK,SAAS,KAAK,QAAQ;EACvC,MAAMA,QAAM,KAAK;AACjB,SAAO,WAAW,QAAQ,uBAAuB;GAAE,MAAM;GAAO;EAAK,EAAC;CACvE;AAED,KAAI,kBACF,QAAO,WAAW,QAAQ,uBAAuB;EAC/C,MAAM;EACN,KAAK,kBAAkB,SAAS,KAAK;CACtC,EAAC;AAGJ;AACD;AAED,SAAgB,4BAAmC,EACjD,QACA,UACA,oBACA,SACA,gBACA,MACA,gBACA,mBACA,WACA,kBACA,WACA,iBAAiB,EAAE,KAAK,YAAY,QAAQ,eAAe,EAC3D,cACA,uBACA,oBACuC,EAAY;CACnD,IAAI,EAAE,KAAK,SAAS,QAAQ,YAAY,GAAG;CAC3C,MAAM,eAAe,QAAQ;CAE7B,MAAM,aAAa,CAACG,eAAsB;AAGxC,OAAK,MAAM,UAAU,gBAAgB;GACnC,MAAM,SAAS,OAAO;AACtB,OAAI,SAAS,QAAS;GACtB,MAAM,UAAU,uBAAuB;IACrC;IACA;IACA;IACA,QAAQ;IACR;IACA;IACA;GACD,EAAC;AAEF,OAAI,WAAW,UAAU,UAAU,UAAU,UAAU,QAAQ;AAC7D,cAAU,UAAUC,aAAW,UAAU;AACzC;GACD;EACF;CACF;CAED,MAAM,kBAAkB,CAACC,WAA0C;AACjE,SAAO,OAAO,QAAQ;CACvB;CAED,MAAM,8BAA8B,MAAM;AACxC,MAAI,UAAU;GAEZ,MAAM,aAAa,QAAQ;GAC3B,IAAI,SAAS,WAAW;AACxB,UAAO,mBAAsB;IAC3B,MAAM,eAAe,gBAAgB,OAAO;AAC5C,QAAI,eAAe,cAAc;AAC/B,eAAU,OAAO,MAAM,OAAO;AAC9B;IACD;AACD,aAAS,OAAO;GACjB;EACF,WAAU,QAAQ;GAEjB,MAAM,aAAa,QAAQ;GAC3B,IAAI,SAAS,WAAW;GACxB,IAAI,QAAQ;AACZ,UAAO,mBAAsB;IAC3B,MAAM,eAAe,gBAAgB,OAAO;AAC5C,QAAI,cAAc,cAAc;AAC9B,eAAU,OAAO;AACjB,kBAAa;AACb,aAAQ;AACR;IACD;AACD,aAAS,OAAO;GACjB;AAGD,QAAK,OAAO;AACV,cAAU;AACV,iBAAa;GACd;EACF;CACF;AAED,KAAI,mBAAmB,aAAa,EAAE;AACpC,aAAW,SAAS;AAEpB,MAAI,aAAa,iBACf,8BAA6B;CAEhC;AAED,KAAI,uBAAuB,cAAc;EACvC,MAAM,oBAAoB,YAAY;EACtC,MAAM,sBAAsB,YAAY;AAExC,MAAI,mBAAmB;GACrB,MAAM,YAAY,eAAe;AACjC,QAAK,WAAW;AACd,cAAU;AACV,kBAAc;GACf;EACF,WAAU,qBAAqB;GAC9B,MAAM,aAAa,eAAe;AAClC,QAAK,YAAY;AACf,kBAAc;AACd,cAAU,eAAe;GAC1B;AACD,cAAW,MAAM;EAClB;CACF;AAED,KAAI,aAAa,oBAAoB,UAAU,MAAM,UAAU,cAAc;EAI3E,MAAM,aAAa,QAAQ;EAC3B,IAAI,SAAS,WAAW;EACxB,MAAM,mBAAmB;AACzB,eAAa;AACb,SAAO,mBAAsB;GAC3B,MAAM,eAAe,gBAAgB,OAAO;AAC5C,OAAI,gBAAgB,kBAAkB;AACpC,iBAAa;AACb,cAAU,OAAO;GAClB;AACD,YAAS,OAAO;EACjB;CACF;AAED,QAAO;EAAE,KAAK;EAAS,QAAQ;CAAY;AAC5C;AAUD,SAAgB,YAAY,EAC1B,WACA,WACA,WACA,kBAAkB,EAAE,QAAQ,KAAK,EACjC,UACgB,EAAW;CAE3B,MAAM,kBAAkB,QAAQ;CAChC,MAAM,mBAAmB,QAAQ;CACjC,MAAM,YAAY,WAAW;CAC7B,MAAM,aAAa,WAAW;AAE9B,QAAO,WAAW,oBAAoB,aAAa,mBAAmB;AACvE;;;;AC1PD,MAAaC,OAAI;AA6BjB,MAAaC,iBAAgB,WAAYD,KAAI;AAE7C,MAAaE,aAAU;AAavB,MAAaC,uBAAsB,kBAAmBD,WAAU;AAEhE,MAAME,iBAAc;AAkBpB,MAAaC,gCAA6B;AAO1C,MAAaC,2BAA0B,uBAAwBF,eAAc;;;;ACnE7E,SAAgB,YAAYG,QAA+B;AACzD,QAAO,EAAE,wBAAwB,OAAQ;AAC1C;AAED,SAAgB,mBACdC,QACAD,QACAE,SACqB;CACrB,MAAM,aAAa,SAAS;CAC5B,MAAM,qBAAqB,OAAO,UAAU,EAAE;AAE9C,KAAI,OAAO,kBACT,QAAO;EACL,iBAAiB;EACjB,cAAc;EACd;EACA;CACD;AAGH,QAAO;EACL,kBAAkB,OAAO,SAAS,QAAQ;EAC1C,cAAc,aAAa;EAC3B;EACA;CACD;AACF;AAED,SAAgB,aACdC,QACA,UAAU,GACW;CACrB,MAAM,QAAQ,OAAO,MAAM;AAC3B,QAAO;EACL,iBAAiB;EACjB,eAAe,QAAQ;EACvB,kBAAkB,OAAO,UAAU,wBAAwB,OAAO,IAAI;CACvE;AACF;AAED,SAAgB,iBACdA,QACA,GAAG,cACK;AACR,QAAO,KACL,eACA,GACG,sBAAsB,OAAO,OAC/B,GACD,GAAG,aACJ;AACF;;;;AChDD,MAAa,EAAE,KAAK,KAAK,OAAO,MAAM,KAAK,GAAG;AAE9C,SAAgB,uBACdC,WACoC;AACpC,YAAW,cAAc,WACvB,OAAM,IAAI,MAAM;AAEnB;AAED,SAAgB,iBACdC,OACA,EAAE,UAAU,UAAmC,EACvC;AACR,SAAQ,IAAI,OAAO,SAAS;AAG5B,YAAW,aAAa,YAAY,YAAY,SAC9C,QAAO,IAAI,OAAO,SAAS;AAG7B,QAAO;AACR;AAED,SAAgB,qBACdC,QACAC,QACA;AACA,QAAO,OAAO,oBAAuB,SAAS,OAAO,QAAQ,OAAO,OAAO;AAC5E;;;;ACnCD,MAAME,WAAQ;AAkBd,MAAMC,qBAAoB,qBAAsBD,SAAQ;AAExD,SAAgBE,eAAe,EAAEC,UAAUC,cAAe,GAAGC,OAA4B,EAAE;CACzF,SAASC,aAAaC,GAAwC;AAC5DJ,WAASI,EAAEI,OAAOC,SAAUL,EAAEM,YAA2BE,SAAS;CACpE;AAEA,wBACE,IAAC;EACC,KAAMC,QAAO;AACX,OAAIA,GACFA,IAAGZ,gBAAgBA,kBAAkB;EAExC;EACD,MAAK;EACL,WAAWH;EACX,UAAUK;EACV,GAAID;GACJ;AAEN;;;;ACtCA,MAAMa,mBAAgB;AAMtB,MAAMC,6BAA4B,yBAA0BD,iBAAgB;AAE5E,MAAME,QAAK;AAcX,MAAMC,kBAAiB,YAAaD,MAAK;AAEzC,SAAgBE,kBAAyBC,OAAoC;AAC3E,wBAAO,IAAC,eAAY,GAAIA,QAAS;AACnC;AAEA,SAAgBG,YAAmB,EACjCC,UACAC,YACAC,UACAC,aAC4B,EAAE;CAC9B,SAASC,cAAc,EAAEC,KAA2C,EAAE;AACpE,MAAIA,QAAQ,QACVF,cAAa;CAEjB;CAEA,MAAMM,IAAIR,aAAa,sBAAsB;AAE7C,wBACE,KAAC;EAAK,WAAWT;EAAqCU;EAAU,WAAWE;aACxEJ,0BACD,IAAC;GAAI,SAAQ;GAAW,OAAM;GAAK,QAAO;GAAI,WAAWN;GAAgB;6BACvE,IAAC,UAAQe,IAAE;IACR;GACA;AAEX;;;;ACpDA,SAAgB,YAAmBC,OAA+B;AAChE,KAAI;AACF,SAAO,MAAM,IAAI,MAAM,OAAO;CAC/B,QAAO;AACN,SAAO;CACR;AACF;;;;ACHD,MAAa,kCAAkC,qBAAoD;AAEnG,SAAgB,sBAAsD;AACpE,QAAO,WAAW,gCAAgC;AACnD;;;;ACGD,SAAgB,oBAAoB,EAClC,OACA,UACA,eACA,UACA,UACA,cAAc,WACd,mBAAmB,gBACM,EAAE;CAC3B,MAAMC,mBAAiB,qBAAqB,CAAE;AAE9C,QAAO,iBAAe;EACpB,cAAc;EACd,mBAAmB;EACnB;EACA;EACA;EACA,SAAS;EACT;CACD,EAAC;AACH;;;;ACvBD,MAAa,sBAAsB,qBAA8D;AAEjG,MAAa,4BAA4B,qBAG7B;AAEZ,SAAgB,kBAAkB;CAChC,MAAM,sBAAsB,WAAW,oBAAoB;CAC3D,MAAM,4BAA4B,WAAW,0BAA0B;AAEvE,KAAI,kCAAqC,qCACvC,OAAM,IAAI,MAAM;AAGlB,QAAO;EACL,wBAAwB,oBAAoB;EAC5C,eAAe,oBAAoB;EACnC,sBAAsB;CACvB;AACF;AAOD,MAAa,4BAA4B,qBAExC;AAED,MAAa,kCAAkC,qBAEnC;AAEZ,SAAgB,wBAAwB;CACtC,MAAM,4BAA4B,WAAW,0BAA0B;CACvE,MAAM,kCAAkC,WAAW,gCAAgC;AAEnF,KAAI,wCAA2C,2CAC7C,OAAM,IAAI,MAAM;AAGlB,QAAO;EACL,iBAAiB,0BAA0B;EAC3C,eAAe,0BAA0B;EACzC,sBAAsB;CACvB;AACF;;;;ACrDD,MAAa,oBAAoB;AAEjC,SAAS,eAAeC,OAAuC;CAC7D,MAAM,EAAE,iBAAiB,eAAe,sBAAsB,GAAG,uBAAuB;AAExF,wBACE,IAAC;EACC,cAAW;EACX,UAAU,MAAM;EAChB,eAAe;EACf,OAAO;EACP,UAAU,CAAC,YAAY;AACrB,wBAAqB,EAAE,SAAS,kBAAkB,QAAQ,QAAS,EAAC;EACrE;GACD;AAEL;AAED,SAAS,gBAAgBC,OAAiC;CACxD,MAAM,EAAE,wBAAwB,eAAe,sBAAsB,GAAG,iBAAiB;AAEzF,wBACE,IAAC;EACC,cAAW;EACX,UAAU,MAAM;EAChB,UAAU;EACV,OAAO;EACP,UAAU,CAAC,SAAS,iBAAiB;AACnC,wBAAqB;IAAE,KAAK,MAAM;IAAK;IAAS;GAAc,EAAC;EAChE;GACD;AAEL;AAED,SAAS,qBAAqBC,OAAsC;CAClE,MAAM,EAAE,eAAe,sBAAsB,GAAG,iBAAiB;AAEjE,wBACE,IAAC;EACC,cAAW;EACX,UAAU,MAAM;EAChB,OAAO;EACP,UAAU,CAAC,YAAY;AACrB,wBAAqB;IAAE,KAAK,MAAM;IAAK;IAAS,cAAc;GAAO,EAAC;EACvE;GACD;AAEL;AAGD,MAAaC,eAAiC;CAC5C,KAAK;CACL,MAAM;CACN,OAAO;CACP,UAAU;CACV,UAAU;CACV,WAAW;CACX,UAAU;CACV,QAAQ;CACR,iBAAiB,OAAO;AACtB,yBAAO,IAAC,kBAAe,GAAI,QAAS;CACrC;CACD,WAAW,OAAO;AAChB,yBAAO,IAAC,mBAAgB,GAAI,QAAS;CACtC;CACD,gBAAgB,OAAO;AACrB,yBAAO,IAAC,wBAAqB,GAAI,QAAS;CAC3C;AACF;;;;ACnED,MAAMG,0BAAuB;AAM7B,MAAMC,iBAAc;AAQpB,MAAMC,2BAA0B,uBAAwBD,eAAc;AAEtE,SAAwBE,iBAAwB,EAC9CC,QACAC,eACAC,UAC6B,EAAE;AAC/B,MAAKF,OAAOK,SAAU,QAAOL,OAAOM;AAEpC,wBACE,IAAC;EAAkCL;EAAyBC;YACzDF,OAAOM;GACW;AAEzB;AAWA,SAASO,mBAA0B,EACjCZ,eACAC,UACAQ,UAC+B,EAAE;CACjC,MAAMI,qBAAmBnB,qBAA4B,CAAEmB;AAEvD,wBACE,KAAC;EAAK,WAAWlB;6BACf,IAAC;GAAK,WAAWE;GAA0BY;IAAe,kBAC1D,IAAC,oBAAMI,mBAAiB;GAAEb;GAAeC;EAAU,EAAC,GAAO;GACtD;AAEX;;;;AC9BA,MAAM,uBAAuB;AAC7B,MAAM,2BAA2B;AAWjC,SAAgB,qBAA4B,EAC1C,YACA,sBACA,gBACA,eACA,YACA,sBAC6B,EAAE;CAC/B,MAAM,eAAe,sBAAsB,SAAS;CACpD,MAAM,kBAAkB,sBAAsB,YAAY;CAC1D,MAAM,kBAAkB,sBAAsB;CAC9C,MAAMa,sBAAoB,sBAAsB,cAAc;CAC9D,MAAM,0BAA0B,sBAAsB,oBAAoB;CAC1E,MAAM,kBAAkB,sBAAsB,YAAY;CAC1D,MAAM,mBAAmB,sBAAsB,aAAa;CAC5D,MAAM,mBAAmB,sBAAsB,aAAa;CAE5D,MAAM,EAAE,SAAS,gBAAgB,uBAAuB,iBAAiB,GAAG,QAAQ,MAK/E;EACH,IAAIC,0BAAwB;EAC5B,IAAIC,oBAAkB;EACtB,MAAMC,YAA4C,CAAE;AAEpD,iBAAe,YAAY,EAAE;EAE7B,SAAS,eACPC,cACAC,OACAC,QACA;AACA,QAAK,MAAM,aAAaC,cAAY;AAClC,QAAI,cAAc,WAAW;KAC3B,MAAMC,yBAA+D;MACnE,MAAM,UAAU;MAChB;MACA,KAAK;MACL,SAAS;MACT,OAAO;MACP,iBAAiB,UAAU;KAC5B;AAED,oBAAe,UAAU,UAAU,QAAQ,GAAG,uBAAuB;AACrE;IACD;IAED,MAAM,SAAS,UAAU,UAAU;IAEnC,MAAMC,SAAyC;KAC7C,GAAG;KACH;KACA,KAAK;KACL,OAAO;KACP;KACA,OAAO,UAAU,SAAS;KAC1B,UAAU,UAAU,YAAY;KAChC,UAAU,UAAU,YAAY;KAChC,UAAU,UAAU,YAAY;KAChC,WAAW,UAAU,aAAa;KAClC,WAAW,UAAU,aAAa;KAClC,YAAY,UAAU,cAAcT;KACpC,kBAAkB,UAAU,oBAAoB;IACjD;AAED,cAAQ,KAAK,OAAO;AAEpB,QAAI,OACF;AAGF,QAAI,QAAQE,kBACV,qBAAkB;GAErB;EACF;AAED,YAAQ,KAAK,CAAC,EAAE,KAAK,MAAM,QAAQ,SAAS,EAAE,EAAE,KAAK,MAAM,QAAQ,SAAS,KAAK;AAE/E,OAAI,SAAS,kBAAmB,QAAO;AACvC,OAAI,SAAS,kBAAmB,QAAO;AAGvC,OAAI,SAAS;AACX,QAAI,QAAS,QAAO;AACpB,WAAO;GACR;AACD,OAAI,QAAS,QAAO;AAKpB,UAAO;EACR,EAAC;EAEF,MAAMQ,mBAA4C,CAAE;AACpD,YAAQ,QAAQ,CAAC,QAAQ,QAAQ;AAC/B,UAAO,MAAM;AACb,sBAAmB,QAAQ,KAAK,EAAE;AAElC,OAAI,OAAO,WAAW,KACpB,kBAAe,KAAK,OAAO;EAE9B,EAAC;AAEF,SAAO;GACL;GACA;GACA;GACA;EACD;CACF,GAAE;EACD;EACA;EACA;EACA;EACAV;EACA;EACA;EACA;EACA;CACD,EAAC;CAEF,MAAM,EAAE,iBAAiB,eAAe,wBAAwB,eAAe,GAAG,QAAQ,MAKrF;EACH,MAAMW,kBAAgB,IAAI;EAC1B,IAAI,OAAO;EACX,IAAIC,2BAAyB;EAC7B,MAAMC,oBAA4B,CAAE;AAEpC,OAAK,MAAM,UAAU,SAAS;GAC5B,IAAI,QAAQ,eAAe,OAAO;AAElC,cAAW,UAAU,SACnB,SAAQ,iBAAiB,OAAO,OAAO;OAIvC,SAAQ,OAAO;AAEjB,qBAAgB,MAAM,EAAE,MAAM,IAAI;AAClC,mBAAc,IAAI,QAAQ;IAAE;IAAO;GAAM,EAAC;AAC1C,WAAQ;EACT;AAED,MAAI,0BAA0B,IAAI;GAChC,MAAM,eAAe,gBAAc,IAAI,QAAQ,uBAAuB;AACtE,8BAAyB,aAAa,OAAO,aAAa;EAC3D;EAED,MAAMC,kBAAwC,CAAE;AAEhD,OAAK,IAAI,IAAI,GAAG,KAAK,uBAAuB,KAAK;GAC/C,MAAM,SAAS,QAAQ;AACvB,oBAAe,oBAAoB,OAAO,IAAI,MAAM,EAAE,gBAAc,IAAI,OAAO,CAAE,KAAK;EACvF;AAED,SAAO;GAAE;GAAiB;GAAe;GAAwB;EAAe;CACjF,GAAE;EAAC;EAAgB;EAAS;CAAsB,EAAC;CAEpD,MAAM,CAAC,qBAAqB,kBAAkB,GAAG,QAAQ,MAAwB;AAC/E,OAAK,qBACH,QAAO,CAAC,GAAG,QAAQ,SAAS,CAAE;EAGhC,MAAM,eAAe,aAAa;EAClC,MAAM,gBAAgB,aAAa;EAEnC,MAAM,aAAa,QAAQ,SAAS;EACpC,MAAM,yBAAyB,IAAI,wBAAwB,GAAG,WAAW;AAGzE,MAAI,gBAAgB,cAClB,QAAO,CAAC,wBAAwB,sBAAuB;EAIzD,IAAI,qBAAqB;AACzB,SAAO,qBAAqB,YAAY;GACtC,MAAM,EAAE,MAAM,OAAO,GAAG,cAAc,IAAI,QAAQ,oBAAoB;AAGtE,OAAI,OAAO,QAAQ,aACjB;AAEF;EACD;EAGD,IAAI,mBAAmB;AACvB,SAAO,mBAAmB,YAAY;GACpC,MAAM,EAAE,MAAM,OAAO,GAAG,cAAc,IAAI,QAAQ,kBAAkB;AAGpE,OAAI,OAAO,SAAS,cAClB;AAEF;EACD;EAED,MAAMC,wBAAsB,IAAI,wBAAwB,qBAAqB,EAAE;EAC/E,MAAMC,sBAAoB,IAAI,YAAY,mBAAmB,EAAE;AAE/D,SAAO,CAACD,uBAAqBC,mBAAkB;CAChD,GAAE;EACD;EACA;EACA;EACA;EACA;EACA;EACA;CACD,EAAC;AAEF,QAAO;EACL;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;CACD;AACF;AAED,SAAS,mBACPC,QACAC,OACAb,OACA;AACA,KAAI,QAAQ,OAAO,MACjB,QAAO,QAAQ;AAGjB,KAAI,OAAO,mBAAsB;EAC/B,MAAM,EAAE,QAAQ,GAAG;AACnB,MAAI,OAAO,QAAQ,GACjB,QAAO,MAAM;AAEf,SAAO,WAAW;AAClB,qBAAmB,QAAQ,OAAO,QAAQ,EAAE;CAC7C;AACF;;;;AC3RD,SAAgB,gBACdc,SACAC,iBACAC,iBACAC,SACAC,WACAC,cACAC,sBACAC,gBACAC,mBACA;CACA,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,SAG1C,KAAK;CACf,MAAM,CAAC,0BAA0B,4BAA4B,GAC3D,SAAqC,KAAK;CAC5C,MAAM,CAAC,eAAe,qBAAqB,GAAG,SAAS,UAAU;CACjE,MAAMC,iBAA0B,QAAQ,WAAW,gBAAgB;CACnE,MAAM,mDAEJ,kBAEA,cAAc;CAChB,MAAM,qBAAqB,CAAC,GAAG,eAAgB;CAC/C,MAAMC,mBAA6B,CAAE;AAErC,MAAK,MAAM,EAAE,KAAK,KAAK,OAAO,IAAI,iBAAiB;EACjD,MAAM,cAAc,aAAa,IAAI,IAAI;AACzC,MAAI,QAAQ,oBAAoB,KAAK;AACnC,sBAAmB,OACjB,mBAAmB,UAAU,gBACzB,mBAAmB,SAClB,EAAE,mBAAmB,MAAM;AAClC,oBAAiB,KAAK,IAAI;EAC3B,kBACQ,UAAU,YAEjB,aAAa,SAAS,cACrB,oDACC,0BAA0B,IAAI,IAAI,KAAK,QACvC,yBACF;AACA,sBAAmB,OAAO;AAC1B,oBAAiB,KAAK,IAAI;EAC3B;CACF;CAED,MAAM,sBAAsB,mBAAmB,KAAK,IAAI;AAExD,iBAAgB,+BAA+B;CAE/C,SAAS,iCAAiC;AACxC,uBAAqB,UAAU;AAC/B,MAAI,iBAAiB,WAAW,EAAG;EAEnC,MAAM,kBAAkB,IAAI,IAAI;EAChC,IAAI,aAAa;AAEjB,OAAK,MAAM,OAAO,kBAAkB;GAClC,MAAM,gBAAgB,mBAAmB,SAAS,IAAI;AACtD,kBAAe,kBAAkB,aAAa,IAAI,IAAI,EAAE;AACxD,OAAI,yBACF,iBAAgB,OAAO,IAAI;OAE3B,iBAAgB,IAAI,KAAK;IAAE,MAAM;IAAY,OAAO;GAAe,EAAC;EAEvE;AAED,MAAI,uBAAuB,MAAM;GAC/B,MAAM,cAAc,mBAAmB;GACvC,MAAM,WAAW,aAAa,IAAI,YAAY,EAAE;GAChD,MAAM,WAAW,mBAAmB,SAAS,YAAY;AACzD,OAAI,uBAA0B,aAAa,UAAU;AACnD,iBAAa;AACb,oBAAgB,IAAI,aAAa;KAC/B,MAAM;KACN,OAAO;IACR,EAAC;GACH;AACD,yBAAsB,KAAK;EAC5B;AAED,MAAI,WACF,sBAAqB,gBAAgB;CAExC;CAED,SAAS,mBAAmBC,QAAiCC,WAAyB;EACpF,MAAM,EAAE,KAAK,aAAa,GAAG;AAE7B,YAAU,MAAM;AACd,OAAI,gBAAgB;IAElB,MAAM,qBAAqB,IAAI;AAC/B,SAAK,MAAM,EAAE,KAAK,OAAO,IAAI,gBAC3B,KACE,gBAAgB,cACT,UAAU,YACjB,aAAa,IAAI,IAAI,EAAE,SAAS,UAEhC,oBAAmB,IAAI,IAAI;AAI/B,gCAA4B,mBAAmB;GAChD;AAED,yBAAsB;IACpB,KAAK;IACL,OAAO;GACR,EAAC;AAEF,4BAAyB,cAAc,SAAS;EACjD,EAAC;AAEF,8BAA4B,KAAK;AAEjC,MAAI,gBAAgB;GAClB,MAAM,gBAAgB,aAAa,IAAI,YAAY,EAAE;GACrD,MAAM,kBACG,cAAc,WAAW,YAAY,mBAAmB,SAAS,YAAY;AACtF,OAAI,uBAA0B,aAAa,cACzC,gBAAe,QAAQ,SAAS;EAEnC;CACF;AAED,QAAO;EACL;EACA;CACD;AACF;AAED,SAAS,mBAAmBT,SAAiDU,KAAa;CACxF,MAAM,YAAY,4BAA4B,IAAI,OAAO,IAAI,CAAC;CAC9D,MAAM,gBAAgB,QAAQ,SAAS,cAAc,SAAS;AAC9D,QAAO,eAAe,uBAAuB,CAAC;AAC/C;;;;AC7ID,SAAgB,oBAAoB;CAClC,MAAM,UAAU,OAAuB,KAAK;CAC5C,MAAM,CAAC,YAAY,cAAc,GAAG,SAAS,EAAE;CAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,SAAS,EAAE;CAC7C,MAAM,CAAC,2BAA2B,6BAA6B,GAAG,SAAS,EAAE;AAE7E,iBAAgB,MAAM;EACpB,MAAM,EAAE,gBAAgB,GAAG;AAI3B,MAAI,kBAAkB,KAAM;EAE5B,MAAM,EAAE,aAAa,cAAc,aAAa,cAAc,GAAG,QAAQ;EACzE,MAAM,EAAE,OAAO,QAAQ,GAAG,QAAQ,QAAS,uBAAuB;EAClE,MAAM,mCAAmC,eAAe;EACxD,MAAM,eAAe,QAAQ,cAAc;EAC3C,MAAM,gBAAgB,SAAS;AAE/B,gBAAc,aAAa;AAC3B,eAAa,cAAc;AAC3B,+BAA6B,iCAAiC;EAE9D,MAAM,iBAAiB,IAAI,eAAe,CAAC,YAAY;GACrD,MAAM,OAAO,QAAQ,GAAG,eAAe;GACvC,MAAM,EAAE,8BAAc,8BAAc,GAAG,QAAQ;AAG/C,aAAU,MAAM;AACd,kBAAc,KAAK,WAAW;AAC9B,iBAAa,KAAK,UAAU;AAC5B,iCAA6BC,iBAAeC,eAAa;GAC1D,EAAC;EACH;AACD,iBAAe,QAAQ,QAAQ,QAAS;AAExC,SAAO,MAAM;AACX,kBAAe,YAAY;EAC5B;CACF,GAAE,CAAE,EAAC;AAEN,QAAO;EAAC;EAAS;EAAY;EAAW;CAA0B;AACnE;;;;ACvCD,SAAgB,cAAwDC,IAAU;CAChF,MAAM,MAAM,OAAO,GAAG;AAEtB,iBAAgB,MAAM;AACpB,MAAI,UAAU;CACf,EAAC;CAEF,MAAM,aAAa,YAAY,CAAC,GAAG,SAAqC;AACtE,MAAI,QAAS,GAAG,KAAK;CACtB,GAAE,CAAE,EAAC;AAGN,QAAO,KAAK,aAAa;AAC1B;;;;AChBD,SAAgB,kBAAkBC,YAAqB;CAErD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,SAAS,MAAM;AAE3D,KAAI,mBAAmB,WACrB,mBAAkB,MAAM;CAG1B,SAAS,QAAQC,OAAyC;EACxD,MAAM,iBAAiB,MAAM,cAAc,cACzC,mBACD;AAGD,MAAI,mBAAmB,MAAM;AAC3B,kBAAe,MAAM,EAAE,eAAe,KAAM,EAAC;AAC7C,qBAAkB,KAAK;EACxB;CACF;CAED,MAAM,cAAc,eAAe;AAEnC,QAAO;EACL,UAAU,cAAc,IAAI;EAC5B,eAAe,aAAa,IAAI;EAChC,SAAS,aAAa;CACvB;AACF;;;;ACZD,SAAgB,mBAA0B,EACxC,SACA,gBACA,MACA,gBACA,mBACA,qBACA,mBACA,uBACA,qBACA,mBAC2B,EAAE;CAE7B,MAAM,WAAW,QAAQ,MAAM;AAC7B,MAAI,wBAAwB,EAAG,QAAO;EAEtC,IAAIC,aAAW;EAEf,MAAM,iBAAiB,CAACC,QAAgBC,YAAgC;AACtE,OAAI,sBAAyB,SAAS,UAAU,qBAAqB;AAEnE,iBAAW;AACX,WAAO;GACR;AACD,UAAO;EACR;AAED,OAAK,MAAM,UAAU,gBAAgB;GAEnC,MAAM,SAAS,OAAO;AACtB,OAAI,UAAUF,WAAU;AACxB,OAAI,eAAe,QAAQ,WAAW,QAAQ,uBAAuB,EAAE,MAAM,SAAU,EAAC,CAAC,CACvF;AAIF,QAAK,IAAI,SAAS,qBAAqB,UAAU,mBAAmB,UAAU;IAC5E,MAAMG,QAAM,KAAK;AACjB,QACE,eAAe,QAAQ,WAAW,QAAQ,uBAAuB;KAAE,MAAM;KAAO;IAAK,EAAC,CAAC,CAEvF;GAEH;AAGD,OAAI,kBAAkB,MACpB;SAAK,MAAMA,SAAO,eAChB,KACE,eACE,QACA,WAAW,QAAQ,uBAAuB;KAAE,MAAM;KAAW;IAAK,EAAC,CACpE,CAED;GAEH;AAGH,OAAI,qBAAqB,MACvB;SAAK,MAAMA,SAAO,kBAChB,KACE,eACE,QACA,WAAW,QAAQ,uBAAuB;KAAE,MAAM;KAAW;IAAK,EAAC,CACpE,CAED;GAEH;EAEJ;AAED,SAAOH;CACR,GAAE;EACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;CACD,EAAC;AAEF,QAAO,QAAQ,MAA0C;EACvD,MAAMI,kBAA6C,CAAE;AACrD,OAAK,IAAI,SAAS,GAAG,UAAU,mBAAmB,UAAU;GAC1D,MAAM,SAAS,QAAQ;AAEvB,OAAI,SAAS,aAAa,OAAO,OAAQ;AACzC,mBAAgB,KAAK,OAAO;EAC7B;AAED,SAAO;CACR,GAAE;EAAC;EAAU;EAAmB;CAAQ,EAAC;AAC3C;;;;ACtGD,SAAgB,gBAAmB,EACjC,MACA,WACA,cACA,WACA,sBACoB,EAAE;CACtB,MAAM,EAAE,gBAAgB,kBAAkB,WAAW,cAAc,YAAY,GAAG,QAAQ,MAAM;AAC9F,aAAW,cAAc,SACvB,QAAO;GACL,gBAAgB,YAAY,KAAK;GACjC,mBAAmB,UAAU,KAAK,OAAO,IAAI,UAAU;GACvD,WAAW,CAACC,WAAmB,SAAS;GACxC,cAAc,MAAM;GACpB,YAAY,CAACC,WAAmB,MAAM,SAAS,UAAU;EAC1D;EAGH,IAAIC,mBAAiB;EACrB,IAAIC,qBAAmB;EAIvB,MAAM,eAAe,KAAK,IAAI,CAACC,UAAQ;GACrC,MAAM,mBAAmB,UAAUA,MAAI;GACvC,MAAM,WAAW;IAAE,KAAKF;IAAgB,QAAQ;GAAkB;AAClE,0BAAqB,EAAE,iBAAiB;AACxC,uBAAkB;AAClB,UAAO;EACR,EAAC;EAEF,MAAM,iBAAiB,CAACF,WAAmB;AACzC,UAAO,IAAI,GAAG,IAAI,KAAK,SAAS,GAAG,OAAO,CAAC;EAC5C;AAED,SAAO;GACL;GACA;GACA,WAAW,CAACA,WAAmB,aAAa,eAAe,OAAO,EAAE;GACpE,cAAc,CAACA,WAAmB,aAAa,eAAe,OAAO,EAAE;GACvE,WAAWC,QAAgB;IACzB,IAAI,QAAQ;IACZ,IAAI,MAAM,aAAa,SAAS;AAChC,WAAO,SAAS,KAAK;KACnB,MAAM,SAAS,QAAQ,OAAO,MAAM,SAAS,EAAE;KAC/C,MAAM,gBAAgB,aAAa,QAAQ;AAE3C,SAAI,kBAAkB,OAAQ,QAAO;AAErC,SAAI,gBAAgB,OAClB,SAAQ,SAAS;cACR,gBAAgB,OACzB,OAAM,SAAS;AAGjB,SAAI,QAAQ,IAAK,QAAO;IACzB;AACD,WAAO;GACR;EACF;CACF,GAAE,CAAC,WAAW,IAAK,EAAC;CAErB,IAAI,sBAAsB;CAC1B,IAAI,oBAAoB,KAAK,SAAS;AAEtC,KAAI,sBAAsB;EACxB,MAAM,oBAAoB;EAC1B,MAAM,qBAAqB,WAAW,UAAU;EAChD,MAAM,mBAAmB,WAAW,YAAY,aAAa;AAC7D,wBAAsB,IAAI,GAAG,qBAAqB,kBAAkB;AACpE,sBAAoB,IAAI,KAAK,SAAS,GAAG,mBAAmB,kBAAkB;CAC/E;AAED,QAAO;EACL;EACA;EACA;EACA;EACA;EACA;EACA;CACD;AACF;;;;ACvFD,MAAMa,kBAAe;AAMrB,MAAMC,4BAA2B,wBAAyBD,gBAAe;AAEzE,SAASE,KAAY,EACnBC,QACAC,SACAC,gBACAC,eACAC,YACAC,QACAC,WACAC,aACAC,iBACAC,SACAC,aACAC,eACAC,mBACAC,eACAC,mBACAC,aACAC,YACAC,MACA,GAAGC,OACsB,EAAE;CAC3B,MAAM,EAAEG,UAAUC,eAAeC,SAAS,GAAGjC,kBAAkBY,eAAe;CAE9E,MAAM,EAAEsB,WAAW,GAAGxB;AACtBM,aAAYd,iBACVQ,QACA,GACGF,2BAA2BK,cAC7B,UACMqB,cAAc,aAAaA,UAAUpB,MAAI,GAAGoB,WACnDlB,UACD;CACD,MAAMmB,aAAa/B,mBAAmBM,QAAQI,MAAI;CAElD,SAASsB,kBAAkBC,cAAwB;AACjDX,aAAW;GAAEX;GAAQuB,KAAK5B,OAAO4B;EAAK,GAAE,EAAED,aAAc,EAAC;CAC3D;CAEA,SAASE,iBACPC,OACAG,cACA;EACA,IAAIC,eAAe;AACnB,MAAID,cAAc;GAChB,MAAME,YAAY5C,gBAAgBuC,MAAM;AACxCG,gBAAa;IAAE5B;IAAQD;IAAKJ;IAAQgB,YAAYU;GAAmB,GAAES,UAAU;AAC/ED,kBAAeC,UAAUC,wBAAwB;EACnD;AACA,SAAOF;CACT;CAEA,SAASG,gBAAgBP,OAAmC;AAC1DvB,gBAAcuB,MAAM;AACpB,OAAKD,iBAAiBC,OAAOtB,gBAAgB,CAE3CkB,oBAAmB;CAEvB;CAEA,SAASY,YAAYR,OAAmC;AACtDrB,YAAUqB,MAAM;AAChBD,mBAAiBC,OAAOpB,YAAY;CACtC;CAEA,SAAS6B,kBAAkBT,OAAmC;AAC5DnB,kBAAgBmB,MAAM;AACtB,OAAKD,iBAAiBC,OAAOlB,kBAAkB,CAE7Cc,mBAAkB,KAAK;CAE3B;CAEA,SAASc,kBAAkBV,OAAmC;AAC5DjB,kBAAgBiB,MAAM;AACtBD,mBAAiBC,OAAOhB,kBAAkB;CAC5C;CAEA,SAAS2B,gBAAgBC,QAAW;AAClC3B,cAAYf,QAAQ0C,OAAO;CAC7B;AAEA,wBACE,IAAC;EACC,MAAK;EACL,iBAAe1C,OAAO4B,MAAM;EAC5B,gBAAc3B;EACd,iBAAeC;EACf,kBAAgBuB;EACNJ;EACCf;EACX,OAAO;GACL,GAAGb,aAAaO,QAAQC,QAAQ;GAChC,GAAGgB;EACJ;EACD,SAASqB;EACT,aAAaD;EACb,eAAeE;EACf,eAAeC;EACNjB;EACT,GAAIL;YAEHlB,OAAO4C,WAAW;GACjB5C;GACAI;GACAC;GACAwC,gBAAgBpB;GAChBJ,UAAUC;GACVP,aAAa0B;EACd,EAAC;GACE;AAEV;AAEA,MAAMK,gBAAgB1D,KAAKW,KAAK;AAEhC,mBAAe+C;AAEf,SAAgBG,kBAAyBC,KAAgBhC,OAAiC;AACxF,wBAAO,IAAC,iBAAwB,GAAIA,SAATgC,IAAkB;AAC/C;;;;ACzFA,MAAM0B,wBAAwBV,cAAc,mBAAmBA,UAAUE,aAAa;AAEtF,MAAMS,cAAW;AAkBjB,SAAwBmB,SAAgB,EACtCC,QACAC,SACAb,YACAF,QACAC,aACAI,aACAM,WACAL,UACqB,EAAE;CACvB,MAAMU,kBAAkB9C,cAAyC;CACjE,MAAMiD,qBAAqBjD,cAAkC;CAC7D,MAAMmD,kBAAkBnD,cAAyB;CACjD,MAAMoD,uBAAuBR,OAAOS,eAAeD,wBAAwB;CAK3E,MAAME,2BAA2BrD,cAAc,MAAM;AACnDsD,UAAQ,MAAM,MAAM;CACrB,EAAC;AAEFxD,iBAAgB,MAAM;AACpB,OAAKqD,qBAAsB;EAE3B,SAASI,yBAAyBnB,OAAmB;AACnDS,mBAAgBW,UAAUpB;AAE1B,OAAId,gBAAgB;IAClB,MAAMmC,kBAAkB,IAAIR;IAC5B,MAAM,EAAE/B,QAAQ,GAAGuC;AACnBT,uBAAmBQ,UAAUC;AAG7B7C,cACGE,SAASuC,0BAA0B;KAClCpC,UAAU;KACVC;IACD,EAAA,CAEAwC,MAAM,MAAM,CAAE,EAAC;GACnB,MACCR,iBAAgBM,UAAUG,sBAAsBN,yBAAyB;EAE7E;EAEA,SAASO,kBAAkBxB,OAAmB;AAC5C,OAAIS,gBAAgBW,YAAYpB,MAC9BiB,2BAA0B;EAE9B;AAEAQ,mBAAiB,aAAaN,0BAA0B,EAAEO,SAAS,KAAM,EAAC;AAC1ED,mBAAiB,aAAaD,kBAAkB;AAEhD,SAAO,MAAM;AACXG,uBAAoB,aAAaR,0BAA0B,EAAEO,SAAS,KAAM,EAAC;AAC7EC,uBAAoB,aAAaH,kBAAkB;AACnDI,eAAY;EACb;CACF,GAAE,CAACb,sBAAsBE,wBAAyB,EAAC;CAEpD,SAASW,aAAa;AACpBnB,kBAAgBW;AAChB,MAAIR,mBAAmBQ,oBAAuB;AAC5CR,sBAAmBQ,QAAQS,OAAO;AAClCjB,sBAAmBQ;EACrB;AACA,MAAIN,gBAAgBM,oBAAuB;AACzCU,wBAAqBhB,gBAAgBM,QAAQ;AAC7CN,mBAAgBM;EAClB;CACF;CAEA,SAASW,cAAc/B,OAA4C;AACjE,MAAII,WAAW;GACb,MAAM4B,YAAYnE,gBAAgBmC,MAAM;AACxCI,aACE;IACE6B,MAAM;IACNtC;IACAY;IACAd;IACAM,WAAW;AACTA,cAASC,MAAM;IAChB;IACDkB;GACD,GACDc,UACD;AACD,OAAIA,UAAUE,wBAAwB,CAAE;EAC1C;AAEA,MAAIlC,MAAMmC,QAAQ,SAEhBjB,UAAS;WACAlB,MAAMmC,QAAQ,QACvBjB,SAAQ,KAAK;WACJlD,mBAAmBgC,MAAM,CAClCD,UAASC,MAAM;CAEnB;CAEA,SAASkB,QAAQtB,gBAAgB,OAAOC,kBAAkB,MAAM;AAC9D,MAAID,cACFF,aAAYC,OAAK,MAAME,gBAAgB;MAEvCC,aAAYD,gBAAgB;CAEhC;CAEA,SAASuC,kBAAkBzC,OAAQ0C,wBAAwB,OAAO;AAChE3C,cAAYC,OAAK0C,uBAAuBA,sBAAsB;CAChE;CAEA,MAAM,EAAEC,WAAW,GAAG/B;CACtB,MAAMgC,YAAYzE,iBAChByC,QACA,yBACCA,OAAOS,eAAewB,sBAAsBrD,oBACtCmD,cAAc,aAAaA,UAAU3C,MAAI,GAAG2C,UACpD;AAED,wBACE,IAAC;EACC,MAAK;EACL,iBAAe/B,OAAOkC,MAAM;EAC5B,gBAAcjC;EACd;EACW+B;EACX,OAAOxE,aAAawC,QAAQC,QAAQ;EACpC,WAAWuB;EACX,oBAAoBH;YAEnBrB,OAAOmC,kBAAkB,wBACxB,4BACGnC,OAAOmC,eAAe;GACrBnC;GACAZ;GACAF;GACAC,aAAa0C;GACblB;EACD,EAAC,EACDX,OAAOS,eAAewB,sBACrBjC,OAAOoC,WAAW;GAChBpC;GACAZ;GACAF;GACAmD,gBAAgB;GAChBC,UAAU;GACVnD,aAAa0C;EACd,EAAC,IAEP;GACG;AAEV;;;;AC3MA,SAAwB,wBAA+B,EACrD,QACA,QACA,gBACA,YACoC,EAAE;CACtC,MAAM,EAAE,UAAU,SAAS,GAAG,kBAAkB,eAAe;CAC/D,MAAM,EAAE,SAAS,GAAG;CACpB,MAAM,UAAU,qBAAqB,QAAQ,OAAO;CACpD,MAAM,QAAQ,OAAO,MAAM;CAE3B,SAAS,cAAc;AACrB,aAAW;GAAE,KAAK,OAAO;GAAK;EAAQ,EAAC;CACxC;AAED,wBACE,IAAC;EACC,MAAK;EACL,iBAAe;EACf,gBAAc;EACd,gBAAc;EACd,iBAAe;EACL;EACV,WAAW,KAAK,eAAe,OAAO,gBAAgB;EACtD,OAAO;GACL,GAAG,mBAAmB,QAAQ,QAAQ,QAAQ;GAC9C,iBAAiB;GACjB,eAAe,QAAQ;EACxB;EACQ;EACI;YAEZ,OAAO;GACJ;AAET;;;;ACnCD,MAAMyB,wBAAqB;AAM3B,MAAMC,gBAAa;AAMnB,MAAMC,0BAAyB,qBAAsBD,cAAa;AAElE,MAAaE,wBAAqB;AAWlC,MAAMC,yBAAyB;AAE/B,MAAMC,eAAY;AAMlB,MAAMC,yBAAwB,oBAAqBD,aAAY;AAE/D,MAAME,WAAQ;AAMd,MAAMC,qBAAoB,qBAAsBD,SAAQ;AAExD,MAAME,qBAAkB;AA6BxB,SAAwBc,WAAkB,EACxCN,QACAC,SACAC,QACAC,gBACAI,gBACAC,mBACAC,kBACAC,aACAC,qBACAC,YACAC,WACAT,kBACAC,qBACuB,EAAE;CACzB,MAAM,CAACS,QAAQC,UAAU,GAAG9C,SAAS,MAAM;CAC3C,MAAM+C,eAAehD,OAAuB,KAAK;CACjD,MAAMkD,aAAad,qBAAqBJ,OAAOmB;CAC/C,MAAMC,UAAU7C,qBAAqByB,QAAQE,OAAO;CACpD,MAAM,EAAEmB,UAAUC,eAAeC,SAAS,GAAGpD,kBAAkBgC,eAAe;CAC9E,MAAMqB,YAAYd,aAAae,UAAWC,UAASA,KAAKC,cAAc3B,OAAOmB,IAAI;CACjF,MAAMS,aACJJ,wBAA2BA,YAAY,KAAKd,YAAac;CAC3D,MAAMM,gBAAgBF,YAAYf;CAClC,MAAMkB,WAAWH,yBAA4BlB,YAAasB,SAAS,IAAIR,YAAa;CACpF,MAAMS,WACJH,kBAAkBC,WAAYD,kBAAkB,QAAQ,cAAc;CACxE,MAAM,EAAEI,UAAUC,WAAWC,WAAW,GAAGpC;CAE3C,MAAMqC,YAAYhE,iBAAiB2B,QAAQA,OAAOsC,iBAAiB;GAChEvD,wBAAwBmD;GACxBjD,yBAAyBkD;GACzBhD,yBAAyBiD;GACzB/C,wBAAwB6B;GACxB3B,oBAAoBuB;CACtB,EAAC;CAEF,SAASyB,OAAOC,WAAoB;AAClC,MAAI7B,uBAAuB,KAAM;EACjC,MAAM,EAAE8B,qBAAqB,GAAGzC;AAChC,MAAI4B,uBAA0B;GAE5B,MAAMc,WAAuB;IAC3Bf,WAAW3B,OAAOmB;IAClBN,WAAW4B,sBAAsB,SAAS;GAC3C;AACD9B,uBAAoBD,eAAe8B,YAAY,CAAC,GAAG9B,aAAagC,QAAS,IAAG,CAACA,QAAS,EAAC;EACxF,OAAM;GACL,IAAIC;AACJ,OACGF,wBAAwB,QAAQX,kBAAkB,UAClDW,wBAAwB,QAAQX,kBAAkB,MAEnDa,kBAAiB;IACfhB,WAAW3B,OAAOmB;IAClBN,WAAWiB,kBAAkB,QAAQ,SAAS;GAC/C;AAEH,OAAIU,WAAW;IACb,MAAMI,kBAAkB,CAAC,GAAGlC,WAAa;AACzC,QAAIiC,eAEFC,iBAAgBpB,aAAcmB;QAG9BC,iBAAgBC,OAAOrB,WAAY,EAAE;AAEvCb,wBAAoBiC,gBAAgB;GACrC,MACCjC,qBAAoBgC,iBAAiB,CAACA,cAAe,IAAG,CAAE,EAAC;EAE/D;CACF;CAEA,SAASG,cAAc;AACrBlC,aAAW;GAAEmC,KAAK/C,OAAO+C;GAAK7C;EAAQ,EAAC;CACzC;CAEA,SAAS8C,QAAQC,OAA0C;AACzD,MAAIf,SACFK,QAAOU,MAAMG,WAAWH,MAAMI,QAAQ;CAE1C;CAEA,SAASC,UAAUL,OAA6C;EAC9D,MAAM,EAAE9B,KAAK,GAAG8B;AAChB,MAAIf,aAAaf,QAAQ,OAAOA,QAAQ,UAAU;AAEhD8B,SAAMO,gBAAgB;AACtBjB,UAAOU,MAAMG,WAAWH,MAAMI,QAAQ;EACvC,WACClB,aACAzD,kBAAkBuE,MAAM,KACvB9B,QAAQ,eAAeA,QAAQ,eAChC;AAGA8B,SAAMtE,iBAAiB;GACvB,MAAM,EAAE8E,OAAO,GAAGR,MAAMS,cAAcC,uBAAuB;GAC7D,MAAM,EAAEC,SAAS,GAAGnF,gBAAgBoC,UAAU;GAC9C,MAAMgD,SAAS1C,QAAQyC,UAAU,MAAM;GACvC,MAAME,WAAW1F,iBAAiBqF,QAAQI,QAAQ7D,OAAO;AACzD,OAAI8D,aAAaL,MACflD,gBAAeP,QAAQ8D,SAAS;EAEpC;CACF;CAEA,SAASC,YAAYd,OAAwC;AAE3D/E,YAAU,MAAM;AACdmC,uBAAoBL,OAAOmB,IAAI;EAChC,EAAC;AACF8B,QAAMgB,aAAaC,aAAalD,aAAamD,SAAU,GAAG,EAAE;AAC5DlB,QAAMgB,aAAaG,aAAa;CAClC;CAEA,SAASC,YAAY;AACnBhE,6BAA8B;CAChC;CAEA,SAASiE,WAAWrB,OAAwC;AAE1DA,QAAMO,gBAAgB;AACtBP,QAAMgB,aAAaG,aAAa;CAClC;CAEA,SAASG,OAAOtB,OAAwC;AACtDlC,YAAU,MAAM;AAEhBkC,QAAMO,gBAAgB;AACtB/C,qBAAmBL,kBAAmBJ,OAAOmB,IAAI;CACnD;CAEA,SAASqD,YAAYvB,OAAwC;AAC3D,MAAIwB,iBAAiBxB,MAAM,CACzBlC,WAAU,KAAK;CAEnB;CAEA,SAAS2D,YAAYzB,OAAwC;AAC3D,MAAIwB,iBAAiBxB,MAAM,CACzBlC,WAAU,MAAM;CAEpB;CAEA,IAAI4D;CACJ,IAAIE;AACJ,KAAIzC,WAAW;AACbuC,oBAAkB;GAChBvC,WAAW;GACX2B;GACAM;EACD;AAED,MAAIjE,+BAAkCA,qBAAqBJ,OAAOmB,IAChE0D,mBAAkB;GAChBP;GACAE;GACAE;GACAH;EACD;CAEL;CAEA,MAAMO,QAA6B;EACjC,GAAGtG,mBAAmBwB,QAAQE,QAAQkB,QAAQ;EAC9C,GAAG9C,aAAa0B,QAAQC,QAAO;CAChC;CAED,MAAM+E,UAAUhF,OAAOiF,iBAAiB;EACtCjF;EACA8B;EACAC;EACAV,UAAUC;CACX,EAAC;AAEF,wBACE,4BACGJ,8BACC,IAAC;EACC,KAAKF;EACE8D;EACP,WAAWzG,iBAAiB2B,QAAQA,OAAOsC,iBAAiB9C,mBAAmB;YAE9EwF;GAEJ,kBACD,KAAC;EACC,MAAK;EACL,iBAAehF,OAAO+C,MAAM;EAC5B,gBAAc9C;EACd,gBAAcmB;EACd,iBAAejB;EACf,aAAW8B;EACDZ;EACCgB;EACJyC;EACMhC;EACJvB;EACAyB;EACEM;EACX,GAAIqB;EACJ,GAAIE;aAEHG,SAEA7C,6BACC,IAAC;GACYtB;GACHb;GACQO;GACGC;IAEtB;GACE,IACJ;AAEP;AAOA,SAAS2E,aAAoB,EAC3BtE,WACAb,QACAO,gBACAC,mBACyB,EAAE;CAC3B,MAAM4E,oBAAoBpH,cAAyB;CACnD,MAAMqH,QAAQxE,cAAc;CAE5B,SAASyE,cAAcrC,OAA2C;AAChE,MAAIA,MAAMuC,gBAAgB,WAAWvC,MAAMwC,YAAY,EACrD;AAIFxC,QAAMO,gBAAgB;EAEtB,MAAM,EAAEE,eAAegC,WAAW,GAAGzC;AACrCS,gBAAciC,kBAAkBD,UAAU;EAC1C,MAAME,aAAalC,cAAcmC;EACjC,MAAM,EAAEC,OAAOC,MAAM,GAAGH,WAAWjC,uBAAuB;AAC1DyB,oBAAkBjB,UAAUkB,QAAQpC,MAAM+C,UAAUD,OAAOD,QAAQ7C,MAAM+C;CAC3E;CAEA,SAASC,cAAchD,OAA2C;EAChE,MAAMY,SAASuB,kBAAkBjB;AACjC,MAAIN,kBAAsB;EAC1B,MAAM,EAAEJ,OAAOqC,OAAOC,MAAM,GAAG9C,MAAMS,cAAcmC,cAAelC,uBAAuB;EACzF,IAAIG,WAAWuB,QAAQS,QAAQjC,SAASZ,MAAM+C,UAAU/C,MAAM+C,UAAUnC,SAASkC;AACjFjC,aAAW1F,iBAAiB0F,UAAU9D,OAAO;AAC7C,MAAIyD,QAAQ,KAAKK,aAAaL,MAC5BlD,gBAAeP,QAAQ8D,SAAS;CAEpC;CAEA,SAASoC,uBAAuB;AAC9B1F,qBAAmB;AACnB4E,oBAAkBjB;CACpB;CAEA,SAASgC,gBAAgB;AACvB5F,iBAAeP,QAAQ,cAAc;CACvC;AAEA,wBACE,IAAC;EACC,WAAWd;EACX,SAASP;EACM2G;EACAW;EAGOC;EACPC;GACf;AAEN;AAKA,SAAS1B,iBAAiBxB,OAAwB;CAChD,MAAMmD,gBAAgBnD,MAAMmD;AAE5B,SAAQnD,MAAMS,cAAc4C,SAASF,cAAc;AACrD;;;;AC1XA,MAAaG,MAAG;AAmBhB,MAAaC,gBAAe,UAAWD,IAAG;AAE1C,MAAaE,cAAW;AAOxB,MAAaC,uBAAuB;AAEpC,MAAaC,4BAAyB;AAatC,MAAaC,yBAAyB;AAEtC,MAAaC,4BAA4B;;;;ACnBzC,MAAMiC,YAAS;AAkBf,MAAaC,sBAAqB,iBAAkBD,UAAS;AAE7D,SAASE,UAAsC,EAC7CH,gBACAX,QACAC,SACAC,gBACAG,mBACAU,kBACAC,aACAC,qBACAT,uBACAC,iBACAH,YACAI,WACyB,EAAE;CAC3B,MAAM,CAACQ,kBAAkBC,oBAAoB,GAAGtC,UAAkB;CAElE,MAAMuC,QAAQ,CAAE;AAChB,MAAK,IAAIC,QAAQ,GAAGA,QAAQpB,QAAQqB,QAAQD,SAAS;EACnD,MAAMlB,SAASF,QAAQoB;EACvB,MAAME,UAAUxC,WAAWoB,QAAQK,uBAAuB,EAAEgB,MAAM,SAAU,EAAC;AAC7E,MAAID,mBACFF,UAASE,UAAU;AAGrBH,QAAMM,qBACJ,IAAC;GAESvB;GACCoB;GACDvB;GACR,gBAAgBS,oBAAoBN,OAAOyB;GAC3B1B;GACGG;GACDU;GACGE;GACRD;GACDV;GACDI;GACOQ;GACGC;KAbhBhB,OAAOwB,IAehB,CAAC;CACH;AAEA,wBACE,IAAC;EACC,MAAK;EACL,iBAAe3B;EACf,WAAWlB,KACT+B,oBACA,GACGtB,uBAAuBkB,oBAAoB,GAC7C,GACDE,eACD;YAEAS;GACG;AAEV;AAEA,wBAAexC,KAAKkC,UAAU;;;;AC/F9B,SAAS,uBAA8B,EACrC,QACA,OACA,SACA,iBACA,YACmC,EAAE;CACrC,MAAM,QAAQ,CAAE;CAChB,MAAM,kBAAkB,IAAI;AAE5B,MAAK,MAAM,UAAU,SAAS;EAC5B,IAAI,EAAE,QAAQ,GAAG;AAEjB,MAAI,kBAAsB;AAE1B,SAAO,OAAO,QAAQ,OAAO;AAC3B,OAAI,OAAO,kBAAsB;AACjC,YAAS,OAAO;EACjB;AAED,MAAI,OAAO,UAAU,UAAU,gBAAgB,IAAI,OAAO,EAAE;AAC1D,mBAAgB,IAAI,OAAO;GAC3B,MAAM,EAAE,KAAK,GAAG;AAChB,SAAM,qBACJ,IAAC;IAEC,QAAQ;IACA;IACR,gBAAgB,oBAAoB;IACxB;MAJP,IAKL,CACH;EACF;CACF;AAED,wBACE,IAAC;EACC,MAAK;EACL,iBAAe;EACf,WAAW;YAEV;GACG;AAET;AAED,qCAAe,KAAK,uBAAuB;;;;ACnD3C,SAAS,IAAW,EAClB,WACA,QACA,cACA,iBACA,wBACA,eACA,oBACA,uBACA,YACA,iBACA,oBACA,iBACA,aACA,mBACA,mBACA,UACA,aACA,YACA,MACA,GAAG,OACmB,EAAE;CACxB,MAAM,aAAa,qBAA4B,CAAE;CAEjD,MAAM,kBAAkB,cAAc,CAACkB,QAAiCC,WAAc;AACpF,cAAY,QAAQ,QAAQ,OAAO;CACpC,EAAC;AAEF,aAAY,KACV,eACC,UAAU,SAAS,MAAM,IAAI,SAAS,MAAM,GAC7C,GACG,uBAAuB,oBAAoB,GAC7C,GACD,WAAWC,OAAK,OAAO,EACvB,UACD;CAED,MAAM,QAAQ,CAAE;AAEhB,MAAK,IAAI,QAAQ,GAAG,QAAQ,gBAAgB,QAAQ,SAAS;EAC3D,MAAM,SAAS,gBAAgB;EAC/B,MAAM,EAAE,KAAK,GAAG;EAChB,MAAM,UAAU,WAAW,QAAQ,uBAAuB;GAAE,MAAM;GAAO;EAAK,EAAC;AAC/E,MAAI,mBACF,UAAS,UAAU;EAGrB,MAAM,iBAAiB,oBAAoB;AAE3C,MAAI,kBAAkB,mBACpB,OAAM,KAAK,mBAAmB;MAE9B,OAAM,KACJ,WAAW,OAAO,KAAK;GACrB;GACA;GACA;GACA;GACA,eAAe,uBAAuB;GACtC;GACA;GACA;GACA;GACA;GACA,aAAa;GACb;EACD,EAAC,CACH;CAEJ;CAED,MAAM,iBAAiB,QACrB,OAAiC;EAAE;EAAe;CAAwB,IAC1E,CAAC,wBAAwB,aAAc,EACxC;AAED,wBACE,IAAC;EAAoB,OAAO;4BAC1B,IAAC;GACC,MAAK;GACM;GACX,OAAO;IACL,GAAG,YAAY,aAAa;IAC5B,GAAG;GACJ;GACD,GAAI;aAEH;IACG;GACc;AAEzB;AAED,MAAM,eAAe,KAAK,IAAI;AAE9B,kBAAe;AAEf,SAAgB,iBAAwBC,KAAgBC,OAA8B;AACpF,wBAAO,IAAC,gBAAuB,GAAI,SAAT,IAAkB;AAC7C;;;;ACpGD,SAAwB,aAAa,EACnC,kBAAkB,EAAE,KAAK,QAAQ,EACjC,SACA,yBAKD,EAAE;CACD,MAAM,MAAM,OAAuB,KAAK;AAExC,iBAAgB,MAAM;AAIpB,iBAAe,IAAI,SAAS,OAAO;CACpC,EAAC;AAEF,iBAAgB,MAAM;EACpB,SAAS,qBAAqB;AAC5B,2BAAwB,KAAK;EAC9B;EAED,MAAM,WAAW,IAAI,qBAAqB,oBAAoB;GAC5D,MAAM,QAAQ;GACd,WAAW;EACZ;AAED,WAAS,QAAQ,IAAI,QAAS;AAE9B,SAAO,MAAM;AACX,YAAS,YAAY;EACtB;CACF,GAAE,CAAC,SAAS,uBAAwB,EAAC;AAEtC,wBACE,IAAC;EACM;EACL,OAAO;GACL,YAAY,iBAAoB,SAAS,MAAM;GAC/C,SAAS,oBAAuB,SAAS,SAAS;EACnD;GACD;AAEL;;;;ACjDD,MAAMI,QAAK;AAUX,MAAMC,kBAAiB,iBAAkBD,MAAK;AAE9C,SAAwBE,iBAAiB,EAAEC,eAAeC,UAAiC,EAAE;AAC3F,wBACE,4BACGC,eAAe,EAAEF,cAAe,EAAC,EACjCG,mBAAmB,EAAEF,SAAU,EAAC,IAChC;AAEP;AAEA,SAAgBC,eAAe,EAAEF,eAAoC,EAAE;AACrE,KAAIA,yBAA6B,QAAO;AAExC,wBACE,IAAC;EAAI,SAAQ;EAAW,OAAM;EAAK,QAAO;EAAI,WAAWF;EAAgB;4BACvE,IAAC,UAAK,GAAGE,kBAAkB,QAAQ,kBAAkB,kBAAgB;GACjE;AAEV;AAEA,SAAgBG,mBAAmB,EAAEF,UAAmC,EAAE;AACxE,QAAOA;AACT;;;;ACJA,MAAMI,OAAI;AAyEV,MAAaC,iBAAgB,MAAOD,KAAI;AAExC,MAAME,mBAAgB;AAUtB,MAAaC,6BAA4B,wBAAyBD,iBAAgB;AAElF,MAAaE,qBAAkB;AAS/B,MAAaC,qCAAkC;;;;AC1H/C,MAAaM,uBAAoB;AAgBjC,SAASO,YAAmB,EAC1BC,QACAC,SACAH,YACAI,QACAC,gBACAC,YACwB,EAAE;CAC1B,MAAM,EAAEC,UAAUC,eAAeC,SAAS,GAAGnB,kBAAkBe,eAAe;CAC9E,MAAM,EAAEK,kBAAkB,GAAGR;CAC7B,MAAMS,YAAYpB,iBAChBW,QACAR,6BACOgB,qBAAqB,aAAaA,iBAAiBV,MAAI,GAAGU,iBAClE;CAED,SAASE,cAAc;AACrBN,aAAW;GAAEF;GAAQS,KAAKX,OAAOW;EAAK,EAAC;CACzC;AAEA,wBACE,IAAC;EACC,MAAK;EACL,iBAAeX,OAAOW,MAAM;EAC5B,gBAAcV;EACd,iBAAeE;EACLE;EACCI;EACX,OAAOnB,aAAaU,QAAQC,QAAQ;EACvBS;EACJH;YAERP,OAAOY,oBAAoB;GAAEZ;GAAQF;GAAKO,UAAUC;EAAe,EAAC;GACjE;AAEV;AAEA,0BAAenB,KAAKY,YAAY;;;;AC9BhC,MAAMuC,aAAU;AAQhB,MAAMC,gBAAa;AAYnB,MAAMC,uBAAsB,kBAAmBF,WAAU;AAEzD,SAASG,WAAkB,EACzBC,QACAC,cACAX,YACAY,iBACAX,KACAC,QACAC,uBACAC,iBACAC,OACAQ,YACA,iBAAiBC,cACM,EAAE;CACzB,MAAMC,QAAQ,CAAE;AAChB,MAAK,IAAIC,QAAQ,GAAGA,QAAQJ,gBAAgBK,QAAQD,SAAS;EAC3D,MAAME,SAASN,gBAAgBI;EAC/B,MAAMG,UAAUhC,WAAW+B,QAAQf,uBAAuB;GAAEiB,MAAM;GAAWpB;EAAK,EAAC;AACnF,MAAImB,mBACFH,UAASG,UAAU;EAGrB,MAAMG,iBAAiBlB,oBAAoBc,OAAOK;AAElDR,QAAMS,qBACJ,IAAC;GAESN;GACCC;GACT,KAAKnB;GACGU;GACQY;GACJT;KANPK,OAAOO,IAQhB,CAAC;CACH;AAEA,wBACE,IAAC;EACC,MAAK;EACL,iBAAeX;EACf,WAAW5B,KACTK,eACA,UAAWmB,SAAS,MAAM,IAAI,SAAS,MAAK,GAC5CF,qBACA;IACGhB,uBAAuBY,oBAAoB;KAC3C,EAAGX,uBAAsB,GAAIc,cAAa,IAAKF;IAC/Cf,6BAA6Be;EAElC,EAAC;EACD,OACE;GACE,GAAGjB,YAAYuB,aAAa;GAC5B,yBAAyBV,kBAAoB,EAAGA,IAAG;GACnD,4BAA4BC,qBAAuB,EAAGA,OAAM;EAC7D;YAGFa;GACG;AAEV;AAEA,yBAAe9B,KAAKwB,WAAW;;;;;;;;;;;ACqI/B,SAAgB,SAA+CsB,OAAgC;CAC7F,MAAM,EACJ,KAEA,SAAS,YACT,MACA,gBACA,mBACA,cACA,cAEA,WAAW,cACX,iBAAiB,oBACjB,kBAAkB,qBAClB,cAAc,iBACd,sBAAsB,yBAEtB,cACA,wBACA,sBACA,aACA,qBACA,sBAEA,iBACA,aACA,mBACA,mBACA,eACA,sBACA,UACA,gBACA,kBACA,QACA,YACA,aAEA,sBAAsB,yBAEtB,WACA,WACA,OACA,UACA,gBACA,WAAW,cAEX,MAAM,SACN,cAAc,WACd,mBAAmB,gBACnB,oBAAoB,iBACpB,oBAAoB,iBACpB,iBAAiB,iBACjB,eAAe,QACf,WAAW,QACZ,GAAG;;;;CAKJ,MAAM,mBAAmB,qBAA4B;CACrD,MAAM,OAAO,WAAW;CACxB,MAAM,YAAY,gBAAgB;CAClC,MAAM,kBAAkB,8BAA8B,cAAc,WAAW,YAAY;CAC3F,MAAM,mBAAmB,+BAA+B,cAAc,WAAW,YAAY;CAC7F,MAAM,YAAY,WAAW,aAAa,kBAAkB,aAAa;CACzE,MAAM,aAAa,WAAW,cAAc,kBAAkB,cAAc;CAC5E,MAAMC,qBACJ,WAAW,oBAAoB,kBAAkB,oBAAoBC;CACvE,MAAMC,mBACJ,WAAW,kBAAkB,kBAAkB,kBAAkBC;CACnE,MAAM,iBAAiB,WAAW,kBAAkB,kBAAkB;CACtE,MAAM,uBAAuB,2BAA2B;CACxD,MAAM,YAAY,gBAAgB;;;;CAKlC,MAAM,CAAC,WAAW,aAAa,GAAG,SAAS,EAAE;CAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,SAAS,EAAE;CAC/C,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,SACtD,MAAoB,mBAAmB,IAAI,MAC5C;CACD,MAAM,CAAC,kBAAkB,kBAAkB,GAAG,SAAS,MAAM;CAC7D,MAAM,CAAC,YAAY,YAAY,GAAG,SAAS,MAAM;CACjD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,gBAAuC;CACzF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,SAAiC,KAAK;CACtF,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,SAAS,MAAM;CAC7D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,SAAS,GAAG;CAExD,MAAM,2BACJ,mBAAmB,QAAQ,2BAA2B,SAAS;CACjE,MAAM,eAAe,2BAA2B,kBAAkB;CAClE,MAAM,uBAAuB,2BACzB,CAACC,mBAA+B;AAE9B,0BAAwBC,eAAa;AACrC,0BAAwBA,eAAa;CACtC,IACD;CAEJ,MAAM,iBAAiB,YACrB,CAACC,WAAoC;AACnC,SAAO,aAAa,IAAI,OAAO,IAAI,EAAE,SAAS,OAAO;CACtD,GACD,CAAC,YAAa,EACf;CAED,MAAM,CAAC,SAAS,WAAW,YAAY,0BAA0B,GAAG,mBAAmB;CACvF,MAAM,EACJ,SACA,gBACA,uBACA,iBACA,qBACA,mBACA,iBACA,eACA,wBACD,GAAG,qBAAqB;EACvB;EACA;EACA;EACA;EACA,eAAe;EACf;CACD,EAAC;CAEF,MAAM,sBAAsB,gBAAgB,UAAU;CACtD,MAAM,yBAAyB,mBAAmB,UAAU;CAC5D,MAAM,mBAAmB,sBAAsB;CAC/C,MAAM,+BAA+B,kBAAkB;CACvD,MAAM,+BAA+B,kBAAkB;CACvD,MAAM,aAAa;CACnB,MAAM,mBAAmB,YAAY;CACrC,MAAM,YAAY,KAAK,SAAS,yBAAyB;CAEzD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,SAC9C,OAA2C;EAAE,KAAK;EAAI,QAAQ,YAAY;EAAG,MAAM;CAAU,GAC9F;;;;CAKD,MAAM,eAAe,OAAuB,KAAK;;;;CAKjD,MAAM,aAAa,SAAS;CAC5B,MAAM,mBAAmB,kBAAkB;CAC3C,MAAM,oBAAoB,mBAAmB;CAC7C,MAAM,eAAe,aAAa,mBAAmB;CACrD,MAAM,eAAe,gBAAgB,QAAQ,wBAAwB;CACrE,MAAM,EAAE,SAAS,UAAU,GAAG,gBAAgB,UAAU;CACxD,MAAM,eAAe,mBAAmB,kBAAkB,KAAK,SAAS;CAExE,MAAM,wBAAwB,QAC5B,OAAO;EACL;EACA;EACA;CACD,IACD;EAACJ;EAAgBF;EAAkB;CAAW,EAC/C;CAED,MAAM,uBAAuB,QAAQ,MAAsC;EAEzE,IAAI,iBAAiB;EACrB,IAAI,mBAAmB;AAEvB,MAAI,gBAAgB,QAAQ,gBAAgB,QAAQ,aAAa,OAAO,EACtE,MAAK,MAAMO,SAAO,MAAM;AACtB,OAAI,aAAa,IAAI,aAAaA,MAAI,CAAC,CACrC,kBAAiB;OAEjB,oBAAmB;AAGrB,OAAI,kBAAkB,iBAAkB;EACzC;AAGH,SAAO;GACL,eAAe,mBAAmB;GAClC,iBAAiB,kBAAkB;EACpC;CACF,GAAE;EAAC;EAAM;EAAc;CAAa,EAAC;CAEtC,MAAM,EACJ,qBACA,mBACA,gBACA,kBACA,WACA,cACA,YACD,GAAG,gBAAgB;EAClB;EACA;EACA;EACA;EACA;CACD,EAAC;CAEF,MAAM,kBAAkB,mBAAmB;EACzC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;CACD,EAAC;CAEF,MAAM,EAAE,qBAAqB,oBAAoB,GAAG,gBAClD,SACA,iBACA,iBACA,SACA,WACA,cACA,sBACA,gBACA,kBACD;CAED,MAAM,YAAY,aAAa,KAAK;CACpC,MAAM,YAAY,QAAQ,SAAS;CACnC,MAAM,sCAAsC,4BAA4B,iBAAiB;CACzF,MAAM,qCAAqC,2BAA2B,iBAAiB;CACvF,MAAM,eACJ,kBAAkB,iBAAiB,oBAAoB;CACzD,MAAM,mBAAmB;;;;CAKzB,MAAM,2BAA2B,cAAc,mBAAmB;CAClE,MAAM,8BAA8B,cAAc,sBAAsB;CACxE,MAAM,0BAA0B,cAAc,iBAAiB;CAC/D,MAAM,4BAA4B,cAAc,oBAAoB;CACpE,MAAM,wBAAwB,cAAc,gBAAgB;CAC5D,MAAM,oBAAoB,cAAc,YAAY;CACpD,MAAM,0BAA0B,cAAc,kBAAkB;CAChE,MAAM,0BAA0B,cAAc,kBAAkB;CAChE,MAAM,wBAAwB,cAAc,gBAAgB;CAC5D,MAAM,kBAAkB,cAAc,UAAU;CAChD,MAAM,iCAAiC,cAAc,UAAU;CAC/D,MAAM,mBAAmB,cAAc,WAAW;CAClD,MAAM,yBAAyB,cAAc,iBAAiB;;;;CAK9D,MAAM,YAAY,YAChB,CAAC,eAAe,SAAS;EACvB,MAAMC,SAAO,gBAAgB,QAAQ,QAAS;AAC9C,MAAIA,WAAS,KAAM;AAEnB,MAAI,aACF,gBAAeA,OAAK;AAGtB,SAAK,MAAM,EAAE,eAAe,KAAM,EAAC;CACpC,GACD,CAAC,OAAQ,EACV;;;;AAKD,iBAAgB,MAAM;AACpB,MAAI,iBAAiB;AACnB,OAAI,aAAa,YAAY,QAAQ,iBAAiB,QAAQ,IAAI;AAChE,iBAAa,QAAQ,MAAM,EAAE,eAAe,KAAM,EAAC;AACnD,mBAAe,aAAa,QAAQ;GACrC,MACC,YAAW;AAEb,sBAAmB,MAAM;EAC1B;CACF,GAAE;EAAC;EAAiB;EAAW,iBAAiB;CAAI,EAAC;AAEtD,qBAAoB,KAAK,OAAO;EAC9B,SAAS,QAAQ;EACjB,aAAa,EAAE,KAAK,QAAQ,EAAE;GAC5B,MAAM,cACJ,kBAAqB,MAAM,yBAAyB,MAAM,QAAQ,SAAS;GAC7E,MAAM,iBACJ,qBAAwB,6BAA6B,OAAO,GAAG;AAEjE,OAAI,0BAA6B,0BAC/B,qBAAoB;IAAE,KAAK;IAAa,QAAQ;GAAgB,EAAC;EAEpE;EACD;CACD,GAAE;;;;CAKH,SAAS,gBAAgBC,MAA4B;AACnD,OAAK,qBAAsB;AAE3B,yBAA6B,aAAa;EAE1C,MAAM,kBAAkB,IAAI,IAAI;AAChC,OAAK,MAAMF,SAAO,MAAM;AACtB,OAAI,yBAAyBA,MAAI,KAAK,KAAM;GAC5C,MAAM,SAAS,aAAaA,MAAI;AAChC,OAAI,KAAK,QACP,iBAAgB,IAAI,OAAO;OAE3B,iBAAgB,OAAO,OAAO;EAEjC;AACD,uBAAqB,gBAAgB;CACtC;CAED,SAAS,UAAUG,MAAyB;AAC1C,OAAK,qBAAsB;AAE3B,yBAA6B,aAAa;EAC1C,MAAM,EAAE,YAAK,SAAS,cAAc,GAAG;AACvC,MAAI,yBAAyBH,MAAI,KAAK,KAAM;EAC5C,MAAM,kBAAkB,IAAI,IAAI;EAChC,MAAM,SAAS,aAAaA,MAAI;EAChC,MAAM,SAAS,KAAK,QAAQA,MAAI;AAChC,oBAAkB,OAAO;AAEzB,MAAI,QACF,iBAAgB,IAAI,OAAO;MAE3B,iBAAgB,OAAO,OAAO;AAGhC,MACE,gBACA,mBAAmB,MACnB,mBAAmB,UACnB,iBAAiB,KAAK,QACtB;GACA,MAAM,OAAO,KAAK,SAAS,eAAe;AAC1C,QAAK,IAAI,IAAI,iBAAiB,MAAM,MAAM,QAAQ,KAAK,MAAM;IAC3D,MAAMA,QAAM,KAAK;AACjB,QAAI,yBAAyBA,MAAI,KAAK,KAAM;AAC5C,QAAI,QACF,iBAAgB,IAAI,aAAaA,MAAI,CAAC;QAEtC,iBAAgB,OAAO,aAAaA,MAAI,CAAC;GAE5C;EACF;AAED,uBAAqB,gBAAgB;CACtC;CAED,SAAS,cAAcI,OAAsC;EAC3D,MAAM,EAAE,KAAK,QAAQ,MAAM,GAAG;AAC9B,MAAI,SAAS,OAAQ;AAErB,MAAI,iBAAiB,6BAA6B,OAAO,EAAE;GACzD,MAAMJ,QAAM,KAAK;GACjB,MAAM,YAAY,gBAAgB,MAAM;AACxC,iBACE;IACE,MAAM;IACN;IACA,QAAQ,QAAQ;IAChB;IACA;GACD,GACD,UACD;AACD,OAAI,UAAU,wBAAwB,CAAE;EACzC;AAED,QAAM,MAAM,kBAAkB,SAAU;EACxC,MAAM,cAAc,MAAM,OAAO,QAAQ,YAAY,KAAK;EAC1D,MAAM,aAAa,cAAc,MAAM,WAAW,aAAa;AAC/D,OAAK,gBAAgB,WAAY;AAEjC,UAAQ,MAAM,KAAd;GACE,KAAK;GACL,KAAK;GACL,KAAK;GACL,KAAK;GACL,KAAK;GACL,KAAK;GACL,KAAK;GACL,KAAK;GACL,KAAK;AACH,aAAS,MAAM;AACf;GACF;AACE,oBAAgB,MAAM;AACtB;EACH;CACF;CAED,SAAS,YAAYK,OAAyC;AAE5D,MAAI,MAAM,WAAW,MAAM,cACzB,kBAAiB;GAAE,KAAK;GAAW,QAAQ;EAAiB,GAAE,EAAE,iBAAiB,KAAM,EAAC;CAE3F;CAED,SAAS,aAAaC,OAAsC;EAC1D,MAAM,EAAE,wBAAW,0BAAY,GAAG,MAAM;AACxC,YAAU,MAAM;AACd,gBAAaC,YAAU;AAEvB,iBAAc,IAAIC,aAAW,CAAC;EAC/B,EAAC;AACF,aAAW,MAAM;CAClB;CAED,SAAS,UAAUT,QAAiCU,QAAgBC,OAAQ;AAC1E,aAAW,iBAAiB,WAAY;AACxC,MAAIV,UAAQ,KAAK,QAAS;EAC1B,MAAM,cAAc,KAAK,KAAK,QAAQA,MAAI;AAC1C,eAAa,aAAa;GACxB,SAAS,CAAC,MAAO;GACjB;EACD,EAAC;CACH;CAED,SAAS,sBAAsB;AAC7B,MAAI,iBAAiB,SAAS,OAAQ;AACtC,YAAU,QAAQ,iBAAiB,MAAM,iBAAiB,QAAQ,iBAAiB,IAAI;CACxF;CAED,SAAS,eAAeW,OAA2B;AACjD,OAAK,mCAAoC;EACzC,MAAM,EAAE,KAAK,QAAQ,GAAG;AACxB,eAAa;GAAE,KAAK,KAAK;GAAS,QAAQ,QAAQ;EAAM,GAAE,MAAM;CACjE;CAED,SAAS,gBAAgBA,OAA2B;AAClD,OAAK,gBAAgB,iBAAiB,eAAe,iBAAiB,CACpE;EAGF,MAAM,EAAE,KAAK,QAAQ,GAAG;EACxB,MAAM,SAAS,QAAQ;EACvB,MAAM,aAAa,YAAY;GAAE,KAAK,KAAK;GAAS;EAAQ,GAAE,MAAM;AACpE,YAAU,QAAQ,QAAQ,WAAW;CACtC;CAED,SAAS,gBAAgBP,OAAsC;AAC7D,OAAK,mCAAoC;EACzC,MAAMJ,QAAM,KAAK,iBAAiB;EAClC,MAAM,EAAE,KAAK,UAAU,GAAG;AAG1B,MAAI,gBAAgB,YAAY,QAAQ,KAAK;AAC3C,0BAA6B,aAAa;GAC1C,MAAM,SAAS,aAAaA,MAAI;AAChC,aAAU;IAAE;IAAK,UAAU,aAAa,IAAI,OAAO;IAAE,cAAc;GAAO,EAAC;AAE3E,SAAM,gBAAgB;AACtB;EACD;AAED,MAAI,eAAe,iBAAiB,IAAI,mBAAmB,OAAO,eAAe,KAAK,CACpF,qBAAoB,CAAC,EAAE,KAAK,QAAQ,MAAM;GACxC;GACA;GACA,MAAM;GACN;GACA,aAAaA;EACd,GAAE;CAEN;CAED,SAAS,wBAAwB;AAE/B,MAAI,kBAAkB;AACpB,6BAA0B,aAAa;AACvC,qBAAkB,MAAM;EACzB;CACF;CAED,SAAS,4BAA4BY,OAA2C;AAE9E,QAAM,gBAAgB;AACtB,MAAI,MAAM,gBAAgB,WAAW,MAAM,YAAY,EACrD;AAEF,cAAY,KAAK;AACjB,QAAM,cAAc,kBAAkB,MAAM,UAAU;CACvD;CAED,SAAS,4BAA4BA,OAA2C;EAE9E,MAAM,SAAS,QAAQ;EACvB,MAAM,gCAAgC,mBAAmB,sBAAsB;EAC/E,MAAM,SACJ,YACA,gCACA,MAAM,UACN,OAAO,uBAAuB,CAAC;EACjC,MAAM,aAAa,WAAW,OAAO;AACrC,uBAAqB,WAAW;EAChC,MAAM,eAAe,+BAA+B,aAAa;EACjE,MAAM,KAAK,OAAO,eACf,2BAA2B,aAAa,uBAAuB,iBAAiB,MAAM,EAAE,IAC1F;AACD,iBAAe,GAAG;CACnB;CAED,SAAS,qCAAqC;AAC5C,cAAY,MAAM;AAClB,MAAI,6BAAiC;EAErC,MAAM,EAAE,QAAQ,GAAG;EACnB,MAAM,CAAC,eAAe,YAAY,GAChC,SAAS,oBACL,CAAC,SAAS,GAAG,oBAAoB,CAAE,IACnC,CAAC,mBAAmB,MAAO;AACjC,aAAW,eAAe,YAAY;AACtC,8BAA+B;CAChC;CAED,SAAS,wBAAwB;AAE/B,YAAU,MAAM;CACjB;CAED,SAAS,4BAA4BC,OAAyC;AAC5E,QAAM,iBAAiB;AACvB,aAAW,iBAAiB,SAAS,GAAG,KAAK,OAAO;CACrD;CAED,SAAS,WAAWC,aAAqBC,WAAmB;AAC1D,MAAI,gBAAgB,KAAM;EAE1B,MAAM,EAAE,QAAQ,KAAK,GAAG;EACxB,MAAM,SAAS,QAAQ;EACvB,MAAM,YAAY,KAAK;EACvB,MAAM,cAAc,CAAC,GAAG,IAAK;EAC7B,MAAMC,UAAoB,CAAE;AAC5B,OAAK,IAAI,IAAI,aAAa,IAAI,WAAW,IACvC,KAAI,eAAe;GAAE,QAAQ;GAAG;EAAK,EAAC,EAAE;GACtC,MAAM,aAAa,OAAQ;IAAE,WAAW,OAAO;IAAK;IAAW,WAAW,KAAK;GAAI,EAAC;AACpF,OAAI,eAAe,KAAK,IAAI;AAC1B,gBAAY,KAAK;AACjB,YAAQ,KAAK,EAAE;GAChB;EACF;AAGH,MAAI,QAAQ,SAAS,EACnB,cAAa,aAAa;GAAE;GAAS;EAAQ,EAAC;CAEjD;;;;CAKD,SAAS,8BAA8BC,KAAa;AAClD,SAAO,OAAO,aAAa,OAAO;CACnC;CAED,SAAS,6BAA6BR,QAAgB;AACpD,SAAO,UAAU,KAAK,SAAS,KAAK;CACrC;CAED,SAAS,4BAA4B,EAAE,KAAK,QAAkB,EAAW;AACvE,SAAO,UAAU,aAAa,UAAU,aAAa,8BAA8B,IAAI;CACxF;CAED,SAAS,uBAAuB,EAAE,KAAK,QAAkB,EAAW;AAClE,SAAO,6BAA6B,OAAO,IAAI,OAAO,KAAK,OAAO;CACnE;CAED,SAAS,2BAA2B,EAAE,KAAK,QAAkB,EAAW;AACtE,SAAO,6BAA6B,OAAO,IAAI,8BAA8B,IAAI;CAClF;CAED,SAAS,eAAeS,UAA6B;AACnD,SACE,uBAAuB,SAAS,IAChC,uBAAuB;GAAE;GAAS;GAAM,kBAAkB;EAAU,EAAC;CAExE;CAED,SAAS,WAAWA,UAAoBC,SAAmC;AACzE,OAAK,4BAA4B,SAAS,CAAE;AAC5C,uBAAqB;EAErB,MAAM,eAAe,eAAe,kBAAkB,SAAS;AAE/D,MAAI,SAAS,gBAAgB,eAAe,SAAS,EAAE;GACrD,MAAMnB,QAAM,KAAK,SAAS;AAC1B,uBAAoB;IAAE,GAAG;IAAU,MAAM;IAAQ;IAAK,aAAaA;GAAK,EAAC;EAC1E,WAAU,aAET,gBAAe,gBAAgB,QAAQ,QAAS,CAAC;OAC5C;AACL,sBAAmB,SAAS,oBAAoB,KAAK;AACrD,uBAAoB;IAAE,GAAG;IAAU,MAAM;GAAU,EAAC;EACrD;AAED,MAAI,yBAAyB,aAC3B,sBAAqB;GACnB,QAAQ,SAAS;GACjB,KAAK,6BAA6B,SAAS,OAAO,GAAG,KAAK,SAAS;GACnE,QAAQ,QAAQ,SAAS;EAC1B,EAAC;CAEL;CAED,SAAS,iBAAiB,EAAE,KAAK,QAAkB,EAAEmB,SAAmC;AACtF,aAAW;GAAE,QAAQ,YAAY,SAAS;GAAG;EAAK,GAAE,QAAQ;CAC7D;CAED,SAAS,gBAAgBC,KAAaC,SAAkBC,UAA6B;EACnF,MAAM,EAAE,KAAK,QAAQ,GAAG;EACxB,MAAM,gBAAgB,uCAAuC,QAAQ;AAErE,UAAQ,KAAR;GACE,KAAK,UACH,QAAO;IAAE;IAAK,QAAQ,SAAS;GAAG;GACpC,KAAK,YACH,QAAO;IAAE;IAAK,QAAQ,SAAS;GAAG;GACpC,KAAK,QACH,QAAO;IAAE,KAAK,MAAM;IAAG;GAAQ;GACjC,KAAK,SACH,QAAO;IAAE,KAAK,MAAM;IAAG;GAAQ;GACjC,KAAK,MACH,QAAO;IAAE,KAAK,OAAO,WAAW,KAAK;IAAI;GAAQ;GACnD,KAAK;AAEH,QAAI,cAAe,QAAO;KAAE;KAAK,QAAQ;IAAW;AACpD,WAAO;KAAE,KAAK;KAAG,QAAQ,UAAU,YAAY;IAAQ;GACzD,KAAK;AAEH,QAAI,cAAe,QAAO;KAAE;KAAK,QAAQ;IAAW;AACpD,WAAO;KAAE,KAAK;KAAW,QAAQ,UAAU,YAAY;IAAQ;GACjE,KAAK,UAAU;AACb,QAAI,iBAAiB,WAAW,UAAW,QAAO;IAClD,MAAM,WAAW,UAAU,OAAO,GAAG,aAAa,OAAO,GAAG;AAC5D,WAAO;KAAE;KAAK,QAAQ,WAAW,IAAI,WAAW,SAAS,GAAG;IAAG;GAChE;GACD,KAAK,YAAY;AACf,QAAI,iBAAiB,UAAU,KAAK,OAAQ,QAAO;IACnD,MAAM,WAAW,UAAU,OAAO,GAAG;AACrC,WAAO;KAAE;KAAK,QAAQ,WAAW,iBAAiB,WAAW,SAAS,GAAG,KAAK,SAAS;IAAG;GAC3F;GACD,QACE,QAAO;EACV;CACF;CAED,SAAS,SAASlB,OAAsC;EACtD,MAAM,EAAE,KAAK,UAAU,GAAG;EAC1B,IAAImB,qBAAyC;AAC7C,MAAI,QAAQ,OAAO;AACjB,OACE,YAAY;IACV;IACA;IACA;IACA;IACA;GACD,EAAC,EACF;AACA,yBAAqB;AAErB;GACD;AAED,wBAAqB;EACtB;AAGD,QAAM,gBAAgB;EAEtB,MAAM,UAAU,kBAAkB,MAAM;EACxC,MAAM,eAAe,gBAAgB,KAAK,SAAS,SAAS;AAC5D,MAAI,eAAe,kBAAkB,aAAa,CAAE;EAEpD,MAAM,2BAA2B,4BAA4B;GAC3D,QAAQ,QAAQ;GAChB,UAAU,QAAQ,YAAa,QAAQ,UAAU;GACjD;GACA;GACA;GACA;GACA;GACA;GACA;GACA;GACA;GACA;GACA,iBAAiB;GACjB;GACA,oBAAoB;EACrB,EAAC;AAEF,aAAW,0BAA0B,EAAE,iBAAiB,KAAM,EAAC;CAChE;CAED,SAAS,sBAAsBC,eAA2C;AACxE,MAAI,6BAAiC;EACrC,MAAM,EAAE,QAAQ,GAAG;EAEnB,MAAM,gBACJ,SAAS,oBACL,SAAS,iBAAiB,iBAAiB,oBAC3C,SAAS,iBAAiB,iBAAiB;AAEjD,SAAO,gBAAgB,iBAAiB;CACzC;CAED,SAAS,gBAAgB;AACvB,MACE,UAAU,QACV,iBAAiB,SAAS,WACzB,2BAA2B,iBAAiB,CAE7C;EAGF,MAAM,EAAE,KAAK,QAAQ,GAAG;EACxB,MAAM,SAAS,QAAQ;AACvB,MAAI,OAAO,kBAAkB,QAAQ,OAAO,aAAa,MACvD;EAGF,MAAM,YAAY,WAAW;EAC7B,MAAM,cAAc,eAAe,OAAO;EAC1C,MAAM,UAAU,OAAO,UAAU;GAAE,MAAM;GAAO,KAAK,KAAK;EAAS,EAAC,IAAI;EACxE,MAAM,EAAE,iBAAkB,GAAGC,SAAO,GAAG,aAAa,QAAQ,QAAQ;EACpE,MAAM,YAAY;EAClB,MAAM,eAAe,OAAO,MAAM,UAAU,MAAM;EAClD,MAAMC,kBAAuC;GAC3C,GAAGD;GACH,cAAc,+BAA+B,SAAS;GACtD,iBAAiB,wBAA2B;GAC5C,gBAAgB,qBAAwB;GACxC,kBAAkB,oBACb,OAAO,iBAAiB,KAAK,YAAY;EAE/C;AAED,yBACE,IAAC;GACC,OAAO;GACP,WAAW,KAAK,yBAAyB,OAAO,UAAU,8BAA8B;GACxF,eAAe;GACf,eAAe,aAAa;GAC5B,sBAAsB,aAAa;GACnC,SAAS;GACT,eAAe;IACf;CAEL;CAED,SAAS,cAAchB,QAAgB;AACrC,MAAI,iBAAiB,WAAW,UAAU,iBAAiB,SAAS,SAAU;EAE9E,MAAM,EAAE,KAAK,YAAK,GAAG;EACrB,MAAM,SAAS,QAAQ;EACvB,MAAM,UAAU,WAAW,QAAQ,uBAAuB;GAAE,MAAM;GAAO;EAAK,EAAC;EAC/E,MAAM,2BAA2B,OAAO,eAAe,4BAA4B;EAEnF,MAAM,cAAc,CAACkB,sBAA6B;AAChD,sBAAmBC,kBAAgB;AACnC,uBAAoB,CAAC,EAAE,YAAK,kBAAQ,MAAM;IAAE;IAAK;IAAQ,MAAM;GAAU,GAAE;EAC5E;EAED,MAAM,cAAc,CAAClB,OAAQmB,eAAwBF,sBAA6B;AAChF,OAAI,cAKF,WAAU,MAAM;AACd,cAAU,QAAQ,iBAAiB,QAAQ3B,MAAI;AAC/C,gBAAY4B,kBAAgB;GAC7B,EAAC;OAEF,qBAAoB,CAAC,cAAc;IAAE,GAAG;IAAU;GAAK,GAAE;EAE5D;AAED,MACE,4BACA,KAAK,iBAAiB,YAAY,iBAAiB,YAGnD,aAAY,MAAM;AAGpB,yBACE,IAAC;GAES;GACC;GACT,KAAK5B;GACG;GACK;GACA;GACb,WAAW;GACD;KARL,OAAO,IASZ;CAEL;CAED,SAAS,sBAAsBS,QAAgB;EAE7C,MAAM,iBAAiB,iBAAiB,QAAQ,cAAiB,QAAQ,iBAAiB;AAC1F,MACE,6BACA,iBAAiB,WAAW,WAC3B,gBAAgB,SAAS,eAAe,CAGzC,QAAO,iBAAiB,MAAM,oBAC1B,CAAC,GAAG,iBAAiB,cAAe,IACpC;GACE,GAAG,gBAAgB,MAAM,GAAG,wBAAwB,EAAE;GACtD;GACA,GAAG,gBAAgB,MAAM,wBAAwB,EAAE;EACpD;AAEP,SAAO;CACR;CAED,SAAS,kBAAkB;EACzB,MAAMqB,cAAiC,CAAE;EAEzC,MAAM,EAAE,KAAK,aAAa,QAAQ,gBAAgB,GAAG;EAErD,MAAM,cACJ,sCAAsC,iBAAiB,sBACnD,sBAAsB,IACtB;EACN,MAAM,YACJ,sCAAsC,iBAAiB,oBACnD,oBAAoB,IACpB;AAEN,OAAK,IAAI,iBAAiB,aAAa,kBAAkB,WAAW,kBAAkB;GACpF,MAAM,uBACJ,mBAAmB,sBAAsB,KAAK,mBAAmB,oBAAoB;GACvF,MAAM,SAAS,uBAAuB,iBAAiB;GAEvD,IAAI,aAAa;GACjB,MAAM,iBAAiB,gBAAgB,cAAiB,QAAQ;AAChE,OAAI,0BACF,KAAI,qBAEF,cAAa,CAAC,cAAe;OAG7B,cAAa,sBAAsB,OAAO;GAI9C,MAAM9B,QAAM,KAAK;GACjB,MAAM,eAAe,+BAA+B,SAAS;GAC7D,IAAI+B,MAAkB;GACtB,IAAI,gBAAgB;AACpB,cAAW,iBAAiB,YAAY;AACtC,UAAM,aAAa/B,MAAI;AACvB,oBAAgB,cAAc,IAAI,IAAI,IAAI;GAC3C;AAED,eAAY,KACV,UAAU,KAAK;IAEb,iBAAiB,+BAA+B,SAAS;IACzD,iBAAiB,eAAe;IAChC;IACA;IACA,iBAAiB;IACjB,wBAAwB,yBAAyBA,MAAI,IAAI;IACzD;IACA,iBAAiB;IACjB,aAAa;IACb,mBAAmB;IACnB,mBAAmB;IACnB;IACA;IACA,iBAAiB,mBAAmB,SAAS;IAC7C,oBAAoB,sBAAsB,OAAO;IACjD;IACA,aAAa;IACb,YAAY;IACZ,oBAAoB,cAAc,OAAO;GAC1C,EAAC,CACH;EACF;AAED,SAAO;CACR;AAGD,KAAI,iBAAiB,MAAM,aAAa,iBAAiB,SAAS,WAAW;AAC3E,sBAAoB;GAAE,KAAK;GAAI,QAAQ,YAAY;GAAG,MAAM;EAAU,EAAC;AACvE,8BAA+B;CAChC;AAGD,KAAI,4BAA4B,yBAAyB,gBACvD,yBAAwB,gBAAgB;CAG1C,IAAI,gBAAgB,SAAS,gBAAgB,IAAI,gBAAgB;AACjE,KAAI,sBAAsB,EACxB,kBAAiB,UAAU,oBAAoB,IAAI,iBAAiB;AAEtE,KAAI,KAAK,SAAS,EAChB,iBAAgB;AAElB,KAAI,yBAAyB,EAC3B,kBAAiB,UAAU,uBAAuB,IAAI,iBAAiB;CAGzE,MAAM,oBACJ,iBAAiB,QAAQ,MAAM,iBAAiB,WAAW,YAAY;AAEzE,wBAEE,KAAC;EACO;EACN,cAAY;EACZ,mBAAiB;EACjB,oBAAkB;EAClB,oBAAkB;EAClB,wBAAsB,eAAe;EACrC,iBAAe,QAAQ;EACvB,iBAAe;EAGf,UAAU,kBAAkB,IAAI;EAChC,WAAW,KACT,eACA,GACG,4BAA4B,WAC9B,GACD,UACD;EACD,OACE;GACE,GAAG;GAEH,0BACE,iBAAiB,MAAM,yBAAyB,kBAAkB,kBAC7D,EAAE,uBAAuB;GAEhC,oBACE,6BAA6B,iBAAiB,OAAO,IACrD,kBAAkB,qBACb,EAAE,mBAAmB,sBAAsB,iBAAiB,KAC3D,yBAAyB,iBAC1B;GAEP;GACA,kBAAkB;GAClB,4BAA4B,EAAE,gBAAgB;GAC9C,wBAAwB,EAAE,aAAa;GACvC,GAAG;EACJ;EAEH,KAAK;EACL,KAAK;EACL,SAAS,kBAAkB;EAC3B,UAAU;EACV,WAAW;EACX,QAAQ;EACR,SAAS;EACT,eAAa;EACb,WAAS;;mBAET,KAAC;IAAgC,OAAO;+BACtC,IAAC;KAAgC,OAAO;+BACtC,KAAC;MAA0B,OAAO;iBAC/B,MAAM,KAAK,EAAE,QAAQ,6BAA8B,GAAE,CAAC,GAAG,0BACxD,IAACgC;OAEC,QAAQ,QAAQ;OAChB,QAAQ,+BAA+B;OACvC,SAAS,sBAAsB,YAAY,MAAM;OACjD,iBACE,iBAAiB,WAAW,YAAY,QAAQ,iBAAiB;OAEnE,YAAY;SAPP,MAQL,CACF,kBACF,IAACC;OACiB;OAChB,QAAQ;OACR,SAAS,sBAAsB,iBAAiB;OAChD,gBAAgB;OAChB,mBAAmB;OACnB,kBAAkB;OACL;OACb,qBAAqB;OACE;OACvB,iBACE,iBAAiB,WAAW,mBAAmB,iBAAiB;OAElE,YAAY;OACD;QACX;OACwB;MACI,EACjC,KAAK,WAAW,KAAK,iBACpB,iCAEA;KACG,gBAAgB,IAAI,CAACjC,OAAK,WAAW;MACpC,MAAM,eAAe,kBAAkB,IAAI;MAC3C,MAAM,gBAAgB,mBAAmB,IAAI;MAC7C,MAAM,uBAAuB,iBAAiB,WAAW;MACzD,MAAM,MAAM,mBAAmB,mBAAmB;AAElD,6BACE,IAACkC;OAEC,iBAAe;OACf,QAAQ;OACM;OACd,KAAKlC;OACA;OACL;OACA,iBAAiB,sBAAsB,cAAc;OAC9B;OACvB,iBAAiB,uBAAuB,iBAAiB;OACzD;OACA,YAAY;SAXP,OAYL;KAEL,EAAC;qBACF,IAAC;MAA0B,OAAO;gBAC/B,iBAAiB;OACQ;KAC3B,mBAAmB,IAAI,CAACA,OAAK,WAAW;MACvC,MAAM,eAAe,+BAA+B,KAAK,SAAS,SAAS;MAC3E,MAAM,gBAAgB,KAAK,SAAS;MACpC,MAAM,uBAAuB,iBAAiB,WAAW;MACzD,MAAM,MACJ,eAAe,iBACX,aAAa,oBAAoB,kBAAkB,SAAS;MAElE,MAAM,SACJ,iBACI,oBAAoB,kBAAkB,SAAS,IAAI;AAGzD,6BACE,IAACkC;OACC,iBAAe,eAAe,yBAAyB,SAAS;OAEhE,QAAQ;OACM;OACd,KAAKlC;OACA;OACG;OACR,iBAAiB,sBAAsB,cAAc;OAC9B;OACvB,iBAAiB,uBAAuB,iBAAiB;OACzD,OAAO;OACP,YAAY;SAVP,OAWL;KAEL,EAAC;QACD;KAE2B;GAEjC,eAAe;GAGf,qBAAqB,gBAAgB;GAGrC,8BACC,IAAC;IACC,KAAK;IACL,UAAU,oBAAoB,IAAI;IAClC,WAAW,KAAK,oBAAoB;MACjC,sCAAsC,6BACrC,iBAAiB,OAClB;MACA,cAAc;MACd,4BAA4B,qBAAqB,0BAA0B;IAC7E,EAAC;IACF,OAAO,EACL,cAAc,iBAAiB,SAAS,+BAA+B,EACxE;KACD;GAGH,qBAAqB,wBACpB,IAAC;IACmB;IAClB,yBAAyB;IAChB;KACT;;GAEA;AAET;AAED,SAAS,gBAAgBmC,QAAwB;AAC/C,QAAO,OAAO,cAA8B,6CAAyC;AACtF;AAED,SAAS,eAAeC,IAAcC,IAAc;AAClD,QAAO,GAAG,QAAQ,GAAG,OAAO,GAAG,WAAW,GAAG;AAC9C;;;;ACn0CD,SAAS,UAAiB,EACxB,IACA,UACA,WACA,YACA,gBACA,QACA,YACA,kBACA,iBACA,aAAa,oBACS,EAAE;CACxB,MAAM,EAAE,UAAU,eAAe,SAAS,GAAG,kBAAkB,eAAe;CAE9E,SAAS,cAAc;AACrB,qBAAmB,GAAG;CACvB;CAGD,MAAM,kBAAkB,mBAAmB,qBAAqB,OAAO;AAEvE,wBACE,IAAC;EACC,MAAK;EACL,iBAAe,OAAO,MAAM;EAC5B,iBAAe;EACL;EAEV,WAAW,iBAAiB,OAAO;EACnC,OAAO;GACL,GAAG,aAAa,OAAO;GACvB,QAAQ,kBAAkB,YAAY;EACvC;EACD,aAAa,CAAC,UAAU;AAEtB,SAAM,gBAAgB;EACvB;EACD,SAAS,kBAAkB;EAClB;cAEN,mBAAmB,oBACpB,OAAO,kBAAkB;GACvB;GACA;GACA;GACA;GACA;GACA,UAAU;GACV;EACD,EAAC;IAtBC,OAAO,IAuBR;AAET;AAED,wBAAe,KAAK,UAAU;;;;AC7D9B,MAAMa,WAAQ;AAad,MAAMC,qBAAoB,gBAAiBD,SAAQ;AAQnD,SAASS,WAAkB,EACzBC,WACAL,YACAM,QACAC,iBACAC,iBACAC,eACAC,YACAC,cACAV,SACAC,aACAU,uBACA,GAAGC,OAC0B,EAAE;CAE/B,MAAMC,MAAMP,gBAAgB,GAAGQ,QAAQxB,oBAAoBS,MAAIgB,QAAQ,IAAIhB,MAAIgB;CAE/E,SAASC,oBAAoB;AAC3BP,aAAW;GAAEJ;GAAQQ,KAAK;EAAI,GAAE,EAAEI,iBAAiB,KAAM,EAAC;CAC5D;CAEA,MAAMC,iBAAiBnC,QACrB,OAAiC;EAAE4B,wBAAwB;EAAOH;CAAe,IACjF,CAACA,aACH,EAAC;AAED,wBACE,IAAC;EAAoB,OAAOU;4BAC1B,IAAC;GACC,MAAK;GACL,cAAYnB,MAAIgB,QAAQ;GACxB,gBAAchB,MAAIoB;GAClB,iBAAepB,MAAIqB,WAAW;GAC9B,iBAAerB,MAAIsB;GACnB,WAAWrC,KACTQ,cACAG,oBACA,UAAWU,SAAS,MAAM,IAAI,SAAS,MAAK,GAC5CE,oBAAoB,MAAMd,sBAC1BW,UACD;GACD,aAAaY;GACb,OAAO7B,YAAYuB,aAAa;GAChC,GAAIE;aAEHN,gBAAgBgB,IAAKC,4BACpB,IAAC;IAEC,IAAIxB,MAAIyB;IACR,UAAUzB,MAAI0B;IACd,WAAW1B,MAAI2B;IACf,YAAY3B,MAAIsB;IAChB,gBAAgBd,oBAAoBgB,OAAOV;IACnCU;IACR,KAAKxB;IACL,kBAAkBc;IACLZ;IACb,iBAAiBD,QAAQ2B,SAASJ,OAAOT,IAAI;MAVxCS,OAAOT,IAYf,CAAC;IACC;GACe;AAE1B;AAEA,uBAAehC,KAAKqB,WAAW;;;;AC7C/B,SAAgB,aAAmD,EACjE,SAAS,YACT,MAAM,SACN,WAAW,cACX,cAAc,iBACd,eAAe,kBACf,YAAY,eACZ,aAAa,gBACb,cACA,cAAc,iBACd,sBAAsB,yBACtB,WACA,SAAS,YACT,YACA,kBACA,0BACA,eAAe,iBACf,GAAG,OACyB,EAAE;CAC9B,MAAM,mBAAmB,qBAA4B;CACrD,MAAM,eAAe,WAAW,aAAa,kBAAkB,aAAa;CAC5E,MAAM,+BAA+B,KAAK,MAAM,gBAAgB,UAAU;CAC1E,MAAM,EAAE,SAAS,UAAU,GAAG,gBAAgB,MAAM,UAAU;CAC9D,MAAM,oBAAoB,cAAc,YAAY;CACpD,MAAM,gBAAgB,oBAAoB;CAE1C,MAAM,EAAE,SAAS,SAAS,GAAG,QAAQ,MAAM;EACzC,MAAM4B,YAAU,WAAW,SAAS,CAAC,EAAE,KAAK,MAAM,EAAE,EAAE,KAAK,MAAM,KAAK;AAEpE,OAAI,SAAS,kBAAmB,QAAO;AACvC,OAAI,SAAS,kBAAmB,QAAO;AAGvC,OAAI,WAAW,SAAS,KAAK,EAAE;AAC7B,QAAI,WAAW,SAAS,KAAK,CAC3B,QAAO,WAAW,QAAQ,KAAK,GAAG,WAAW,QAAQ,KAAK;AAE5D,WAAO;GACR;AACD,OAAI,WAAW,SAAS,KAAK,CAAE,QAAO;AAGtC,UAAO;EACR,EAAC;EAEF,MAAMC,YAAoB,CAAE;AAC5B,OAAK,MAAM,CAAC,OAAO,OAAO,IAAI,UAAQ,SAAS,CAC7C,KAAI,WAAW,SAAS,OAAO,IAAI,EAAE;AACnC,aAAQ,KAAK,OAAO,IAAI;AACxB,aAAQ,SAAS;IACf,GAAG;IACH,QAAQ;IACR,YAAY,MAAM;IAClB,iBAAiB,OAAO,mBAAmB;IAC3C,UAAU;GACX;EACF;AAGH,SAAO;GAAE;GAAS;EAAS;CAC5B,GAAE,CAAC,YAAY,UAAW,EAAC;CAE5B,MAAM,CAAC,aAAa,UAAU,GAAG,QAAQ,MAAM;AAC7C,MAAI,QAAQ,WAAW,EAAG,QAAO,SAAY,QAAQ,MAAO;EAE5D,MAAM,YAAY,CAChBC,QACA,CAAC,YAAY,GAAG,qBAAwC,EACxDC,kBAC6C;GAC7C,IAAI,iBAAiB;GACrB,MAAMC,SAA+B,CAAE;AACvC,QAAK,MAAM,CAAC,KAAK,UAAU,IAAI,OAAO,QAAQ,WAAWC,QAAM,WAAW,CAAC,EAAE;IAE3E,MAAM,CAAC,aAAa,eAAe,GACjC,qBAAqB,WAAW,IAC5B,CAAC,WAAW,UAAU,MAAO,IAC7B,UAAU,WAAW,sBAAsB,gBAAgB,iBAAiB,EAAE;AACpF,WAAO,OAAO;KAAE;KAAW;KAAa,eAAe,gBAAgB;IAAgB;AACvF,sBAAkB,iBAAiB;GACpC;AAED,UAAO,CAAC,QAAQ,cAAe;EAChC;AAED,SAAO,UAAU,SAAS,SAAS,EAAE;CACtC,GAAE;EAAC;EAAS;EAAY;CAAQ,EAAC;CAElC,MAAM,CAAC,MAAM,WAAW,GAAG,QAAQ,MAG9B;EACH,MAAM,eAAe,IAAI;AACzB,OAAK,YAAa,QAAO,CAAC,SAASC,YAAW;EAE9C,MAAMC,gBAAwC,CAAE;EAEhD,MAAM,cAAc,CAClBC,QACAC,UACAC,UACS;AACT,OAAI,gBAAgBL,OAAK,EAAE;AACzB,kBAAc,KAAK,GAAGA,OAAK;AAC3B;GACD;AACD,UAAO,KAAKA,OAAK,CAAC,QAAQ,CAAC,UAAU,UAAU,SAAS;IACtD,MAAM,KAAK,cAAc,UAAU,SAAS;IAC5C,MAAM,aAAa,iBAAiB,IAAI,GAAG;IAC3C,MAAM,EAAE,WAAW,aAAa,eAAe,GAAGA,OAAK;IAEvD,MAAMM,aAAwB;KAC5B;KACA;KACA;KACA;KACA;KACA;KACA;KACA;KACA,SAAS,KAAK;IACf;AACD,kBAAc,KAAKC,WAAS;AAC5B,iBAAa,IAAIA,WAAS;AAE1B,QAAI,WACF,aAAY,aAAa,IAAI,QAAQ,EAAE;GAE1C,EAAC;EACH;AAED,cAAY,qBAAwB,EAAE;AACtC,SAAO,CAAC,eAAeN,YAAW;EAElC,SAASA,aAAWO,OAA0C;AAC5D,UAAO,aAAa,IAAIC,MAAI;EAC7B;CACF,GAAE;EAAC;EAAkB;EAAa;EAAS;CAAc,EAAC;CAE3D,MAAM,YAAY,QAAQ,MAAM;AAC9B,aAAW,iBAAiB,WAC1B,QAAO,CAACD,UAAiC;AACvC,OAAI,WAAWC,MAAI,CACjB,QAAO,aAAa;IAAE,MAAM;IAAS;GAAK,EAAC;AAE7C,UAAO,aAAa;IAAE,MAAM;IAAO;GAAK,EAAC;EAC1C;AAGH,SAAO;CACR,GAAE,CAAC,YAAY,YAAa,EAAC;CAE9B,MAAM,uBAAuB,YAC3B,CAACD,UAAyB;EACxB,MAAM,SAAS,KAAK,QAAQC,MAAI;AAChC,OAAK,IAAI,IAAI,SAAS,GAAG,KAAK,GAAG,KAAK;GACpC,MAAM,YAAY,KAAK;AACvB,OAAI,WAAW,UAAU,MAAM,WAAWA,MAAI,IAAIA,MAAI,aAAa,UAAU,IAC3E,QAAO,CAAC,WAAW,CAAE;EAExB;AAED;CACD,GACD,CAAC,YAAY,IAAK,EACnB;CAED,MAAM,eAAe,YACnB,CAACD,UAAyB;AACxB,MAAI,WAAWC,MAAI,CACjB,QAAOA,MAAI;AAGb,aAAW,oBAAoB,WAC7B,QAAO,gBAAgBA,MAAI;EAG7B,MAAM,oBAAoB,qBAAqBA,MAAI;AACnD,MAAI,8BAAiC;GACnC,MAAM,EAAE,eAAe,WAAW,GAAG,kBAAkB;GACvD,MAAM,aAAa,UAAU,QAAQA,MAAI;AACzC,UAAO,gBAAgB,aAAa;EACrC;AAED,SAAO,KAAK,QAAQA,MAAI;CACzB,GACD;EAAC;EAAsB;EAAY;EAAiB;CAAK,EAC1D;CAED,MAAM,eAAe,QAAQ,MAA+B;AAC1D,MAAI,mBAAmB,KAAM,QAAO;AAEpC,yBAA6B,gBAAgB;EAE7C,MAAMC,iBAAe,IAAI,IAAS;AAClC,OAAK,MAAMD,SAAO,KAChB,KAAI,WAAWA,MAAI,EAAE;GAEnB,MAAM,qBAAqB,MAAI,UAAU,MAAM,CAAC,OAC9C,gBAAgB,IAAI,gBAAgB,GAAG,CAAC,CACzC;AACD,OAAI,mBACF,gBAAa,IAAIA,MAAI,GAAG;EAE3B;AAGH,SAAOC;CACR,GAAE;EAAC;EAAY;EAAiB;EAAiB;CAAK,EAAC;CAExD,SAAS,qBAAqBC,iBAA2B;AACvD,OAAK,wBAAyB;AAE9B,yBAA6B,gBAAgB;EAE7C,MAAM,qBAAqB,IAAI,IAAI;AACnC,OAAK,MAAMF,SAAO,MAAM;GACtB,MAAM,MAAM,aAAaA,MAAI;AAC7B,OAAI,cAAc,IAAI,IAAI,KAAK,gBAAgB,IAAI,IAAI,CACrD,KAAI,WAAWA,MAAI,CAEjB,MAAK,MAAM,MAAMA,MAAI,UACnB,oBAAmB,OAAO,gBAAgB,GAAG,CAAC;OAGhD,oBAAmB,OAAO,IAAS;aAE3B,cAAc,IAAI,IAAI,IAAI,gBAAgB,IAAI,IAAI,CAC5D,KAAI,WAAWA,MAAI,CAEjB,MAAK,MAAM,MAAMA,MAAI,UACnB,oBAAmB,IAAI,gBAAgB,GAAG,CAAC;OAG7C,oBAAmB,IAAI,IAAS;EAGrC;AAED,0BAAwB,mBAAmB;CAC5C;CAED,SAAS,cAAcG,MAA8BC,OAA0B;AAC7E,qBAAmB,MAAM,MAAM;AAC/B,MAAI,MAAM,wBAAwB,CAAE;AAEpC,MAAI,KAAK,SAAS,OAAQ;EAC1B,MAAM,EAAE,QAAQ,QAAQ,YAAY,GAAG;EAEvC,MAAM,MAAM,QAAQ,OAAO;EAC3B,MAAMJ,QAAM,KAAK;AAEjB,OAAK,WAAWA,MAAI,CAAE;AACtB,MACE,QAAQ,OAEN,MAAM,QAAQ,WAAWA,MAAI,cAE5B,MAAM,QAAQ,aAAaA,MAAI,aAClC;AAEA,SAAM,gBAAgB;AACtB,SAAM,oBAAoB;AAC1B,eAAYA,MAAI,GAAG;EACpB;AAGD,MAAI,QAAQ,MAAM,MAAM,QAAQ,YAAYA,MAAI,cAAcA,MAAI,UAAU,GAAG;GAC7E,MAAM,oBAAoB,qBAAqBA,MAAI;AACnD,OAAI,8BAAiC;AACnC,UAAM,oBAAoB;AAC1B,eAAW;KAAE;KAAK,QAAQ,kBAAkB;IAAI,EAAC;GAClD;EACF;CACF;CAGD,SAAS,eACP,EAAE,YAAK,QAA+C,EACtDK,OACA;AACA,OAAK,WAAWL,MAAI,CAClB,iBAAgB;GAAE;GAAK;EAAQ,GAAE,MAAM;CAE1C;CAED,SAAS,gBACP,EAAE,YAAK,QAAgD,EACvDK,OACA;AACA,SAAO,WAAWL,MAAI,GAAGA,QAAM,eAAgB;GAAE;GAAK;EAAQ,GAAE,MAAM;CACvE;CAED,SAAS,iBAAiBM,aAAkB,EAAE,SAAS,QAA+B,EAAE;AACtF,OAAK,aAAc;EACnB,MAAM,iBAAiB,CAAC,GAAG,OAAQ;EACnC,MAAMC,aAAuB,CAAE;AAC/B,OAAK,MAAM,SAAS,SAAS;GAC3B,MAAM,WAAW,QAAQ,QAAQ,KAAK,OAAY;AAClD,kBAAe,YAAY,YAAY;AACvC,cAAW,KAAK,SAAS;EAC1B;AACD,eAAa,gBAAgB;GAC3B,SAAS;GACT;EACD,EAAC;CACH;CAED,SAAS,YAAYC,SAAkB;EACrC,MAAM,sBAAsB,IAAI,IAAI;AACpC,MAAI,oBAAoB,IAAI,QAAQ,CAClC,qBAAoB,OAAO,QAAQ;MAEnC,qBAAoB,IAAI,QAAQ;AAElC,2BAAyB,oBAAoB;CAC9C;CAED,SAAS,UACPC,KACA,EACE,YACA,UACA,iBACA,aACA,mBACA,mBACA,aACA,uBACA,oBACA,mBACA,GAAG,UACmB,EACxB;AACA,MAAI,WAAWT,MAAI,EAAE;GACnB,MAAM,EAAE,eAAe,GAAGA;AAC1B,0BACE,IAACU;IAEC,GAAI;IACJ,iBAAe,+BAA+B,gBAAgB;IAC9D,KAAKV;IACI;IACT,aAAa;MALR,IAML;EAEL;EAED,IAAI,eAAe,SAAS;EAC5B,MAAM,oBAAoB,qBAAqBA,MAAI;AACnD,MAAI,8BAAiC;GACnC,MAAM,EAAE,eAAe,WAAW,GAAG,kBAAkB;GACvD,MAAM,aAAa,UAAU,QAAQA,MAAI;AACzC,kBAAe,gBAAgB,+BAA+B,aAAa;EAC5E;AAED,SAAO,aAAa,KAAK;GACvB,GAAG;GACH,iBAAiB;GACjB;GACA;GACA;GACA;GACA;GACA;GACA;GACA;GACA;GACA;EACD,EAAC;CACH;AAED,wBACE,IAAC;EACC,GAAI;EACJ,MAAK;EACL,iBACE,YAAY,KAAK,MAAM,gBAAgB,UAAU,MAAM,MAAM,mBAAmB,UAAU;EAEnF;EACH;EACK;EACG;EACd,cAAc;EACA;EACQ;EACtB,eAAe;EACf,YAAY;EACZ,aAAa,iBAAiB;EAC9B,WAAW;GACT,GAAG;GACH;EACD;GACD;AAEL;AAED,SAAS,qBAAqBW,UAAkBhB,UAA8B;AAC5E,QAAO,uBAA0B,EAAE,SAAS,IAAI,SAAS,IAAI;AAC9D;AAED,SAAS,gBAAgBiB,KAAyC;AAChE,QAAO,MAAM,QAAQ,IAAI;AAC1B;;;;ACpcD,MAAME,8BAA2B;AA6BjC,MAAaC,uBAAsB,kBAAmBD,4BAA2B;AAEjF,SAASE,mBAAmBC,OAAgC;AAC1DA,QAAOE,OAAO;AACdF,QAAOG,QAAQ;AACjB;AAEA,SAAwBC,WAA8B,EACpDC,YACAC,QACAC,aACAC,SACuC,EAAE;AACzC,wBACE,IAAC;EACC,WAAWV;EACX,KAAKC;EACL,OAAOM,MAAIC,OAAOK;EAClB,UAAWC,WAAUL,YAAY;GAAE,GAAGF;IAAMC,OAAOK,MAAMC,MAAMC,OAAOC;EAAO,EAAC;EAC9E,QAAQ,MAAMN,QAAQ,MAAM,MAAM;GAClC;AAEN"}