import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { canAccessChecksheet, canEditChecksheet } from '@/lib/permissions'
import { z } from 'zod'

const updateChecksheetSchema = z.object({
  title: z.string().min(1).optional(),
  description: z.string().optional(),
  data: z.record(z.any()).optional(),
  folderId: z.string().optional()
})

// GET /api/checksheets/[id] - Get checksheet by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const checksheet = await prisma.checksheet.findUnique({
      where: { id: params.id },
      include: {
        template: {
          select: {
            id: true,
            name: true,
            version: true,
            fields: true
          }
        },
        department: true,
        createdBy: {
          select: {
            id: true,
            username: true,
            fullName: true
          }
        },
        folder: {
          select: {
            id: true,
            name: true
          }
        },
        approvals: {
          include: {
            approver: {
              select: {
                id: true,
                username: true,
                fullName: true
              }
            }
          },
          orderBy: { level: 'asc' }
        }
      }
    })

    if (!checksheet) {
      return NextResponse.json({ error: 'Checksheet not found' }, { status: 404 })
    }

    // Check if user can access this checksheet
    const hasAccess = canAccessChecksheet(
      session.user.permissions,
      checksheet.createdById,
      session.user.id,
      checksheet.departmentId,
      session.user.departmentId
    )

    if (!hasAccess) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    return NextResponse.json(checksheet)
  } catch (error) {
    console.error('Error fetching checksheet:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// PUT /api/checksheets/[id] - Update checksheet
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = updateChecksheetSchema.parse(body)

    // Check if checksheet exists
    const existingChecksheet = await prisma.checksheet.findUnique({
      where: { id: params.id }
    })

    if (!existingChecksheet) {
      return NextResponse.json({ error: 'Checksheet not found' }, { status: 404 })
    }

    // Check if user can edit this checksheet
    const canEdit = canEditChecksheet(
      session.user.permissions,
      existingChecksheet.createdById,
      session.user.id,
      existingChecksheet.status
    )

    if (!canEdit) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Check if folder exists and user has access
    if (validatedData.folderId) {
      const folder = await prisma.folder.findUnique({
        where: { id: validatedData.folderId }
      })

      if (!folder) {
        return NextResponse.json({ error: 'Folder not found' }, { status: 404 })
      }

      if (folder.departmentId !== session.user.departmentId && 
          !session.user.permissions?.canViewAllChecksheets) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
      }
    }

    // Update checksheet
    const checksheet = await prisma.checksheet.update({
      where: { id: params.id },
      data: validatedData,
      include: {
        template: {
          select: {
            id: true,
            name: true,
            version: true,
            fields: true
          }
        },
        department: true,
        createdBy: {
          select: {
            id: true,
            username: true,
            fullName: true
          }
        },
        folder: {
          select: {
            id: true,
            name: true
          }
        },
        approvals: {
          include: {
            approver: {
              select: {
                id: true,
                username: true,
                fullName: true
              }
            }
          },
          orderBy: { level: 'asc' }
        }
      }
    })

    return NextResponse.json(checksheet)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error updating checksheet:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// DELETE /api/checksheets/[id] - Delete checksheet
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if checksheet exists
    const checksheet = await prisma.checksheet.findUnique({
      where: { id: params.id }
    })

    if (!checksheet) {
      return NextResponse.json({ error: 'Checksheet not found' }, { status: 404 })
    }

    // Check if user can delete this checksheet
    const canDelete = checksheet.createdById === session.user.id || 
                     session.user.permissions?.canViewAllChecksheets

    if (!canDelete) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Can only delete draft checksheets
    if (checksheet.status !== 'DRAFT') {
      return NextResponse.json(
        { error: 'Can only delete draft checksheets' },
        { status: 400 }
      )
    }

    // Delete checksheet and related approvals
    await prisma.$transaction([
      prisma.approval.deleteMany({
        where: { checksheetId: params.id }
      }),
      prisma.checksheet.delete({
        where: { id: params.id }
      })
    ])

    return NextResponse.json({ message: 'Checksheet deleted successfully' })
  } catch (error) {
    console.error('Error deleting checksheet:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
