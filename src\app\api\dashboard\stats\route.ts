import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// GET /api/dashboard/stats - Get dashboard statistics
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const departmentId = searchParams.get('departmentId')
    const dateFrom = searchParams.get('dateFrom')
    const dateTo = searchParams.get('dateTo')

    // Build base where clause based on user permissions
    let baseWhere: any = {}

    if (session.user.permissions?.canViewAllChecksheets) {
      // Final approver can see all
      if (departmentId) {
        baseWhere.departmentId = departmentId
      }
    } else if (session.user.permissions?.canViewDepartmentChecksheets) {
      // Approver1 can see department checksheets
      baseWhere.departmentId = session.user.departmentId
    } else {
      // Executor can only see own checksheets
      baseWhere.createdById = session.user.id
    }

    // Add date filter if provided
    if (dateFrom || dateTo) {
      baseWhere.createdAt = {}
      if (dateFrom) {
        baseWhere.createdAt.gte = new Date(dateFrom)
      }
      if (dateTo) {
        baseWhere.createdAt.lte = new Date(dateTo)
      }
    }

    // Get checksheet statistics
    const [
      totalChecksheets,
      draftChecksheets,
      submittedChecksheets,
      approvedLevel1Checksheets,
      approvedFinalChecksheets,
      rejectedChecksheets,
      checksheetsByStatus,
      checksheetsByDepartment,
      recentChecksheets,
      pendingApprovals
    ] = await Promise.all([
      // Total checksheets
      prisma.checksheet.count({ where: baseWhere }),
      
      // Draft checksheets
      prisma.checksheet.count({
        where: { ...baseWhere, status: 'DRAFT' }
      }),
      
      // Submitted checksheets
      prisma.checksheet.count({
        where: { ...baseWhere, status: 'SUBMITTED' }
      }),
      
      // Approved level 1 checksheets
      prisma.checksheet.count({
        where: { ...baseWhere, status: 'APPROVED_LEVEL_1' }
      }),
      
      // Approved final checksheets
      prisma.checksheet.count({
        where: { ...baseWhere, status: 'APPROVED_FINAL' }
      }),
      
      // Rejected checksheets
      prisma.checksheet.count({
        where: { ...baseWhere, status: 'REJECTED' }
      }),
      
      // Checksheets by status (for charts)
      prisma.checksheet.groupBy({
        by: ['status'],
        where: baseWhere,
        _count: { id: true }
      }),
      
      // Checksheets by department (if user can see multiple departments)
      session.user.permissions?.canViewAllChecksheets
        ? prisma.checksheet.groupBy({
            by: ['departmentId'],
            where: baseWhere,
            _count: { id: true },
            include: {
              department: {
                select: { name: true }
              }
            }
          })
        : [],
      
      // Recent checksheets
      prisma.checksheet.findMany({
        where: baseWhere,
        take: 10,
        orderBy: { createdAt: 'desc' },
        include: {
          template: {
            select: {
              id: true,
              name: true,
              version: true
            }
          },
          department: {
            select: {
              id: true,
              name: true
            }
          },
          createdBy: {
            select: {
              id: true,
              username: true,
              fullName: true
            }
          }
        }
      }),
      
      // Pending approvals for current user
      session.user.permissions?.canApproveLevel1 || session.user.permissions?.canApproveFinal
        ? prisma.approval.count({
            where: {
              approverId: session.user.id,
              status: 'PENDING'
            }
          })
        : 0
    ])

    // Get department names for department stats
    let departmentStats = []
    if (session.user.permissions?.canViewAllChecksheets && checksheetsByDepartment.length > 0) {
      const departments = await prisma.department.findMany({
        where: {
          id: {
            in: checksheetsByDepartment.map((item: any) => item.departmentId)
          }
        },
        select: {
          id: true,
          name: true
        }
      })

      departmentStats = checksheetsByDepartment.map((item: any) => ({
        departmentId: item.departmentId,
        departmentName: departments.find(d => d.id === item.departmentId)?.name || 'Unknown',
        count: item._count.id
      }))
    }

    // Calculate completion rate
    const completedChecksheets = approvedFinalChecksheets
    const completionRate = totalChecksheets > 0 
      ? Math.round((completedChecksheets / totalChecksheets) * 100) 
      : 0

    // Calculate approval rate
    const processedChecksheets = approvedFinalChecksheets + rejectedChecksheets
    const approvalRate = processedChecksheets > 0
      ? Math.round((approvedFinalChecksheets / processedChecksheets) * 100)
      : 0

    const stats = {
      overview: {
        totalChecksheets,
        draftChecksheets,
        submittedChecksheets,
        approvedLevel1Checksheets,
        approvedFinalChecksheets,
        rejectedChecksheets,
        pendingApprovals,
        completionRate,
        approvalRate
      },
      charts: {
        checksheetsByStatus: checksheetsByStatus.map((item: any) => ({
          status: item.status,
          count: item._count.id
        })),
        checksheetsByDepartment: departmentStats
      },
      recentActivity: recentChecksheets
    }

    return NextResponse.json(stats)
  } catch (error) {
    console.error('Error fetching dashboard stats:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
