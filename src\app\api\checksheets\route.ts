import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { AuditService } from '@/lib/auditService'
import { z } from 'zod'

const createChecksheetSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  templateId: z.string().min(1, 'Template is required'),
  data: z.record(z.any()).default({}),
  folderId: z.string().optional()
})

// GET /api/checksheets - Get checksheets with filtering
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const status = searchParams.get('status')
    const templateId = searchParams.get('templateId')
    const folderId = searchParams.get('folderId')
    const departmentId = searchParams.get('departmentId')

    const skip = (page - 1) * limit

    // Build where clause based on user permissions
    let where: any = {}

    if (search) {
      where.OR = [
        { title: { contains: search } },
        { description: { contains: search } }
      ]
    }

    if (status) {
      where.status = status
    }

    if (templateId) {
      where.templateId = templateId
    }

    if (folderId) {
      where.folderId = folderId
    }

    // Apply access control
    if (session.user.permissions?.canViewAllChecksheets) {
      // Final approver can see all
      if (departmentId) {
        where.departmentId = departmentId
      }
    } else if (session.user.permissions?.canViewDepartmentChecksheets) {
      // Approver1 can see department checksheets
      where.departmentId = session.user.departmentId
    } else {
      // Executor can only see own checksheets
      where.createdById = session.user.id
    }

    const [checksheets, total] = await Promise.all([
      prisma.checksheet.findMany({
        where,
        skip,
        take: limit,
        include: {
          template: {
            select: {
              id: true,
              name: true,
              version: true
            }
          },
          department: true,
          createdBy: {
            select: {
              id: true,
              username: true,
              fullName: true
            }
          },
          folder: {
            select: {
              id: true,
              name: true
            }
          },
          approvals: {
            include: {
              approver: {
                select: {
                  id: true,
                  username: true,
                  fullName: true
                }
              }
            },
            orderBy: { level: 'asc' }
          }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.checksheet.count({ where })
    ])

    return NextResponse.json({
      checksheets,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('Error fetching checksheets:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// POST /api/checksheets - Create new checksheet
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user can create checksheets
    if (!session.user.permissions?.canCreateChecksheet) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const body = await request.json()
    const validatedData = createChecksheetSchema.parse(body)

    // Check if template exists and user has access
    const template = await prisma.checksheetTemplate.findUnique({
      where: { id: validatedData.templateId }
    })

    if (!template) {
      return NextResponse.json({ error: 'Template not found' }, { status: 404 })
    }

    if (template.departmentId !== session.user.departmentId && 
        !session.user.permissions?.canViewAllChecksheets) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Check if folder exists and user has access
    if (validatedData.folderId) {
      const folder = await prisma.folder.findUnique({
        where: { id: validatedData.folderId }
      })

      if (!folder) {
        return NextResponse.json({ error: 'Folder not found' }, { status: 404 })
      }

      if (folder.departmentId !== session.user.departmentId && 
          !session.user.permissions?.canViewAllChecksheets) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
      }
    }

    // Create checksheet
    const checksheet = await prisma.checksheet.create({
      data: {
        ...validatedData,
        departmentId: session.user.departmentId,
        createdById: session.user.id,
        status: 'DRAFT'
      },
      include: {
        template: {
          select: {
            id: true,
            name: true,
            version: true,
            fields: true
          }
        },
        department: true,
        createdBy: {
          select: {
            id: true,
            username: true,
            fullName: true
          }
        },
        folder: {
          select: {
            id: true,
            name: true
          }
        }
      }
    })

    // Log audit trail
    await AuditService.logChecksheetCreate(session.user.id, checksheet.id, {
      title: checksheet.title,
      templateId: checksheet.templateId,
      departmentId: checksheet.departmentId
    }, request)

    return NextResponse.json(checksheet, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error creating checksheet:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
