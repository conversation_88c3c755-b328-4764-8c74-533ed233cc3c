'use client'

import { useState } from 'react'
import { useForm, useFieldArray } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Plus, Trash2, GripVertical } from 'lucide-react'

const fieldSchema = z.object({
  id: z.string(),
  type: z.enum(['text', 'textarea', 'number', 'date', 'select', 'checkbox', 'radio']),
  label: z.string().min(1, 'Label is required'),
  required: z.boolean(),
  placeholder: z.string().optional(),
  options: z.array(z.string()).optional(),
  validation: z.object({
    min: z.number().optional(),
    max: z.number().optional(),
    pattern: z.string().optional()
  }).optional()
})

const templateSchema = z.object({
  name: z.string().min(1, 'Template name is required'),
  description: z.string().optional(),
  version: z.string().default('1.0'),
  fields: z.array(fieldSchema),
  folderId: z.string().optional()
})

type TemplateForm = z.infer<typeof templateSchema>
type FieldType = z.infer<typeof fieldSchema>

interface TemplateBuilderProps {
  initialData?: Partial<TemplateForm>
  folders?: Array<{ id: string; name: string }>
  onSave: (data: TemplateForm) => Promise<void>
  onCancel: () => void
  isLoading?: boolean
}

export default function TemplateBuilder({
  initialData,
  folders = [],
  onSave,
  onCancel,
  isLoading = false
}: TemplateBuilderProps) {
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null)

  const {
    register,
    control,
    handleSubmit,
    watch,
    formState: { errors }
  } = useForm<TemplateForm>({
    resolver: zodResolver(templateSchema),
    defaultValues: {
      name: initialData?.name || '',
      description: initialData?.description || '',
      version: initialData?.version || '1.0',
      fields: initialData?.fields || [],
      folderId: initialData?.folderId || ''
    }
  })

  const { fields, append, remove, move } = useFieldArray({
    control,
    name: 'fields'
  })

  const addField = (type: FieldType['type']) => {
    const newField: FieldType = {
      id: `field_${Date.now()}`,
      type,
      label: `New ${type} field`,
      required: false,
      placeholder: '',
      options: type === 'select' || type === 'radio' ? ['Option 1', 'Option 2'] : undefined,
      validation: {}
    }
    append(newField)
  }

  const fieldTypes = [
    { value: 'text', label: 'Text Input' },
    { value: 'textarea', label: 'Text Area' },
    { value: 'number', label: 'Number' },
    { value: 'date', label: 'Date' },
    { value: 'select', label: 'Select Dropdown' },
    { value: 'checkbox', label: 'Checkbox' },
    { value: 'radio', label: 'Radio Buttons' }
  ]

  const handleDragStart = (index: number) => {
    setDraggedIndex(index)
  }

  const handleDragOver = (e: React.DragEvent, index: number) => {
    e.preventDefault()
    if (draggedIndex !== null && draggedIndex !== index) {
      move(draggedIndex, index)
      setDraggedIndex(index)
    }
  }

  const handleDragEnd = () => {
    setDraggedIndex(null)
  }

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <h2 className="text-2xl font-bold mb-6">
        {initialData ? 'Edit Template' : 'Create New Template'}
      </h2>

      <form onSubmit={handleSubmit(onSave)} className="space-y-6">
        {/* Template Basic Info */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Template Name *
            </label>
            <input
              {...register('name')}
              type="text"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              placeholder="Enter template name"
            />
            {errors.name && (
              <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Version
            </label>
            <input
              {...register('version')}
              type="text"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              placeholder="1.0"
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Description
          </label>
          <textarea
            {...register('description')}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
            placeholder="Enter template description"
          />
        </div>

        {folders.length > 0 && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Folder
            </label>
            <select
              {...register('folderId')}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="">Select a folder (optional)</option>
              {folders.map((folder) => (
                <option key={folder.id} value={folder.id}>
                  {folder.name}
                </option>
              ))}
            </select>
          </div>
        )}

        {/* Field Builder */}
        <div>
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium">Form Fields</h3>
            <div className="flex gap-2">
              {fieldTypes.map((type) => (
                <button
                  key={type.value}
                  type="button"
                  onClick={() => addField(type.value as FieldType['type'])}
                  className="px-3 py-1 text-xs bg-primary-100 text-primary-700 rounded-md hover:bg-primary-200"
                >
                  + {type.label}
                </button>
              ))}
            </div>
          </div>

          <div className="space-y-4">
            {fields.map((field, index) => (
              <FieldEditor
                key={field.id}
                field={field}
                index={index}
                register={register}
                watch={watch}
                onRemove={() => remove(index)}
                onDragStart={() => handleDragStart(index)}
                onDragOver={(e) => handleDragOver(e, index)}
                onDragEnd={handleDragEnd}
                errors={errors.fields?.[index]}
              />
            ))}
          </div>

          {fields.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              No fields added yet. Click the buttons above to add form fields.
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex justify-end gap-4 pt-6 border-t">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isLoading}
            className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50"
          >
            {isLoading ? 'Saving...' : 'Save Template'}
          </button>
        </div>
      </form>
    </div>
  )
}

interface FieldEditorProps {
  field: FieldType
  index: number
  register: any
  watch: any
  onRemove: () => void
  onDragStart: () => void
  onDragOver: (e: React.DragEvent) => void
  onDragEnd: () => void
  errors?: any
}

function FieldEditor({
  field,
  index,
  register,
  watch,
  onRemove,
  onDragStart,
  onDragOver,
  onDragEnd,
  errors
}: FieldEditorProps) {
  const fieldType = watch(`fields.${index}.type`)
  const hasOptions = fieldType === 'select' || fieldType === 'radio'

  return (
    <div
      className="p-4 border border-gray-200 rounded-lg bg-gray-50"
      draggable
      onDragStart={onDragStart}
      onDragOver={onDragOver}
      onDragEnd={onDragEnd}
    >
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <GripVertical className="w-4 h-4 text-gray-400 cursor-move" />
          <span className="font-medium">Field {index + 1}</span>
        </div>
        <button
          type="button"
          onClick={onRemove}
          className="text-red-600 hover:text-red-800"
        >
          <Trash2 className="w-4 h-4" />
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Field Type
          </label>
          <select
            {...register(`fields.${index}.type`)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            <option value="text">Text Input</option>
            <option value="textarea">Text Area</option>
            <option value="number">Number</option>
            <option value="date">Date</option>
            <option value="select">Select Dropdown</option>
            <option value="checkbox">Checkbox</option>
            <option value="radio">Radio Buttons</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Label *
          </label>
          <input
            {...register(`fields.${index}.label`)}
            type="text"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
            placeholder="Field label"
          />
          {errors?.label && (
            <p className="mt-1 text-sm text-red-600">{errors.label.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Placeholder
          </label>
          <input
            {...register(`fields.${index}.placeholder`)}
            type="text"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
            placeholder="Placeholder text"
          />
        </div>
      </div>

      <div className="mt-4 flex items-center gap-4">
        <label className="flex items-center">
          <input
            {...register(`fields.${index}.required`)}
            type="checkbox"
            className="mr-2"
          />
          Required field
        </label>
      </div>

      {hasOptions && (
        <div className="mt-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Options (one per line)
          </label>
          <textarea
            {...register(`fields.${index}.options`)}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
            placeholder="Option 1&#10;Option 2&#10;Option 3"
          />
        </div>
      )}
    </div>
  )
}
